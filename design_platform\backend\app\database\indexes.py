"""
数据库索引优化
"""
from sqlalchemy import text
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)

def create_performance_indexes(db: Session):
    """创建性能优化索引"""
    
    indexes = [
        # 数据集表索引
        "CREATE INDEX IF NOT EXISTS idx_datasets_name ON datasets(name);",
        "CREATE INDEX IF NOT EXISTS idx_datasets_is_active ON datasets(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_datasets_created_at ON datasets(created_at);",
        
        # 实验表索引
        "CREATE INDEX IF NOT EXISTS idx_experiments_dataset_id ON experiments(dataset_id);",
        "CREATE INDEX IF NOT EXISTS idx_experiments_status ON experiments(status);",
        "CREATE INDEX IF NOT EXISTS idx_experiments_created_at ON experiments(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_experiments_model_name ON experiments(model_name);",
        
        # 复合索引
        "CREATE INDEX IF NOT EXISTS idx_experiments_dataset_status ON experiments(dataset_id, status);",
        "CREATE INDEX IF NOT EXISTS idx_datasets_active_name ON datasets(is_active, name);",
    ]
    
    created_count = 0
    
    for index_sql in indexes:
        try:
            db.execute(text(index_sql))
            created_count += 1
            logger.info(f"创建索引成功: {index_sql}")
        except Exception as e:
            logger.warning(f"创建索引失败: {index_sql} - {e}")
    
    try:
        db.commit()
        logger.info(f"索引创建完成，共创建 {created_count} 个索引")
    except Exception as e:
        db.rollback()
        logger.error(f"索引创建提交失败: {e}")
    
    return created_count

def analyze_table_performance(db: Session):
    """分析表性能"""
    
    analysis_queries = [
        # 分析数据集表
        """
        SELECT 
            'datasets' as table_name,
            COUNT(*) as total_rows,
            COUNT(CASE WHEN is_active = true THEN 1 END) as active_rows,
            AVG(student_num) as avg_students,
            AVG(exercise_num) as avg_exercises
        FROM datasets;
        """,
        
        # 分析实验表
        """
        SELECT 
            'experiments' as table_name,
            COUNT(*) as total_rows,
            COUNT(DISTINCT dataset_id) as unique_datasets,
            COUNT(DISTINCT model_name) as unique_models,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_experiments
        FROM experiments;
        """,
    ]
    
    results = []
    
    for query in analysis_queries:
        try:
            result = db.execute(text(query)).fetchone()
            if result:
                results.append(dict(result._mapping))
        except Exception as e:
            logger.error(f"性能分析查询失败: {e}")
    
    return results

def get_slow_queries_suggestions():
    """获取慢查询优化建议"""
    
    suggestions = [
        {
            'query_type': '数据集列表查询',
            'original': 'SELECT * FROM datasets WHERE is_active = true',
            'optimized': 'SELECT id, name, display_name, student_num, exercise_num FROM datasets WHERE is_active = true LIMIT 50',
            'improvement': '指定字段，添加限制'
        },
        {
            'query_type': '实验状态查询',
            'original': 'SELECT * FROM experiments WHERE dataset_id = ? ORDER BY created_at DESC',
            'optimized': 'SELECT id, status, model_name, created_at FROM experiments WHERE dataset_id = ? ORDER BY created_at DESC LIMIT 20',
            'improvement': '指定字段，添加限制'
        },
        {
            'query_type': '数据集统计查询',
            'original': 'SELECT COUNT(*) FROM datasets',
            'optimized': 'SELECT COUNT(*) FROM datasets WHERE is_active = true',
            'improvement': '添加过滤条件减少扫描行数'
        }
    ]
    
    return suggestions

def optimize_database_connections():
    """优化数据库连接"""
    
    optimization_tips = [
        {
            'category': '连接池配置',
            'tips': [
                '设置合适的连接池大小（pool_size=10）',
                '配置连接池溢出（max_overflow=20）',
                '设置连接回收时间（pool_recycle=3600）',
                '启用连接预检（pool_pre_ping=True）'
            ]
        },
        {
            'category': '查询优化',
            'tips': [
                '使用批量操作替代循环查询',
                '使用 lazy loading 延迟加载关联数据',
                '使用 select_related 预加载关联对象',
                '避免 N+1 查询问题'
            ]
        },
        {
            'category': '事务管理',
            'tips': [
                '尽量缩短事务持续时间',
                '使用只读事务处理查询',
                '合理使用事务隔离级别',
                '避免长时间持有锁'
            ]
        }
    ]
    
    return optimization_tips

def generate_database_performance_report(db: Session):
    """生成数据库性能报告"""
    
    # 创建索引
    indexes_created = create_performance_indexes(db)
    
    # 分析表性能
    table_analysis = analyze_table_performance(db)
    
    # 获取优化建议
    query_suggestions = get_slow_queries_suggestions()
    connection_tips = optimize_database_connections()
    
    report = {
        'timestamp': '2025-07-30',
        'indexes_created': indexes_created,
        'table_analysis': table_analysis,
        'query_optimization_suggestions': query_suggestions,
        'connection_optimization_tips': connection_tips,
        'summary': {
            'total_indexes': indexes_created,
            'tables_analyzed': len(table_analysis),
            'query_suggestions': len(query_suggestions),
            'connection_tips': sum(len(cat['tips']) for cat in connection_tips)
        }
    }
    
    return report

if __name__ == "__main__":
    print("数据库性能优化模块已加载")
    print("使用 generate_database_performance_report(db) 生成性能报告")
