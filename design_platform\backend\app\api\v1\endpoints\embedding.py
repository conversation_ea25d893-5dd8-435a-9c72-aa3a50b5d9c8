"""
Embedding相关API端点
"""
from fastapi import APIRouter, Depends
from typing import Dict, Any
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.models.experiment import Experiment

router = APIRouter()

@router.get("/examples")
async def get_embedding_examples(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """获取Embedding示例数据 - 基于真实实验结果生成"""

    # 尝试获取最近的实验结果
    recent_experiments = db.query(Experiment).filter(
        Experiment.status == "completed"
    ).order_by(Experiment.completed_at.desc()).limit(3).all()

    # 基于真实实验生成示例
    text_examples = []

    if recent_experiments:
        # 使用真实实验数据生成示例
        for exp in recent_experiments:
            if exp.metrics and 'accuracy' in exp.metrics:
                accuracy = exp.metrics.get('accuracy', 0)
                model_type = exp.model_type or 'EduBrain'

                if accuracy > 75:
                    text_examples.append({
                        "original": f"使用{model_type}模型，学生在数学认知诊断中表现优秀",
                        "embedding_description": f"通过大模型Embedding技术分析，该学生展现出强大的数学认知能力。模型准确率达到{accuracy:.1f}%，表明诊断结果高度可信。语义增强后的表示捕获了学生在抽象思维、逻辑推理等多个维度的优势。",
                        "semantic_features": ["数学天赋", "逻辑思维", "抽象能力", "问题解决", "学习效率"]
                    })
                elif accuracy > 60:
                    text_examples.append({
                        "original": f"使用{model_type}模型，学生认知能力中等，有提升空间",
                        "embedding_description": f"基于{accuracy:.1f}%的模型准确率，语义分析显示学生具备基础认知能力，但在某些知识点存在薄弱环节。Embedding技术帮助识别具体的改进方向。",
                        "semantic_features": ["基础扎实", "部分薄弱", "潜力待挖", "需要指导", "可塑性强"]
                    })
                else:
                    text_examples.append({
                        "original": f"使用{model_type}模型，识别出学生需要重点关注的认知领域",
                        "embedding_description": f"通过语义增强的认知诊断，准确率{accuracy:.1f}%的结果揭示了学生的学习困难点。大模型Embedding帮助深入理解认知障碍的根本原因。",
                        "semantic_features": ["基础薄弱", "需要帮助", "个性化指导", "循序渐进", "重点突破"]
                    })

    # 如果没有足够的真实数据，补充一些高质量的示例
    while len(text_examples) < 4:
        examples = [
            {
                "original": "学生在代数运算方面表现良好",
                "embedding_description": "通过大模型Embedding技术，我们将传统的认知诊断结果转换为丰富的语义表示。这种表示不仅包含了数值化的能力评估，还融入了语义层面的理解，使得诊断结果更加全面和可解释。",
                "semantic_features": ["数学能力", "运算技能", "逻辑思维", "问题解决", "抽象思维"]
            },
            {
                "original": "几何推理能力需要提升",
                "embedding_description": "通过语义增强，系统能够识别学习困难的深层原因，不仅仅是表面的分数低，而是理解学生在空间想象、逻辑推理等方面的具体不足，为个性化教学提供精准指导。",
                "semantic_features": ["空间想象", "逻辑推理", "图形理解", "证明能力", "几何直觉"]
            },
            {
                "original": "概率统计掌握程度中等",
                "embedding_description": "结合大模型的语义理解能力，系统能够分析学生在概率统计领域的认知模式，识别出学生对随机性理解、数据分析思维等方面的具体表现，提供更细粒度的诊断。",
                "semantic_features": ["随机性理解", "数据分析", "统计思维", "概率直觉", "建模能力"]
            },
            {
                "original": "函数概念理解较弱",
                "embedding_description": "通过Embedding技术捕获函数学习中的语义关联，系统能够识别学生在函数概念、图像理解、变化规律等方面的认知状态，为针对性教学设计提供依据。",
                "semantic_features": ["函数概念", "图像理解", "变化规律", "映射思维", "符号操作"]
            }
        ]
        text_examples.extend(examples)
        break

    # 添加数据来源标识
    data_source = "混合数据" if recent_experiments else "示例数据"

    return {
        "text_examples": text_examples[:4],  # 只返回前4个示例
        "embedding_visualization": {
            "dimensions": 768,
            "clustering_info": "相似的认知状态在高维embedding空间中自然聚集，形成不同的认知模式簇",
            "applications": [
                "个性化学习推荐：基于语义相似性推荐适合的学习内容",
                "学习路径规划：根据认知状态设计最优学习序列",
                "同伴匹配：找到认知水平相近的学习伙伴",
                "教师决策支持：为教师提供学生认知状态的深度洞察",
                "自适应测试：动态调整题目难度和类型"
            ]
        },
        "data_source": data_source,
        "experiment_count": len(recent_experiments) if recent_experiments else 0
    }