"""
统计分析模块

实现各种统计检验和分析方法。
"""

import numpy as np
from typing import Dict, List, Tuple, Any
from scipy import stats
import warnings

from ..core.base import BaseStatisticalTest


class WelchTTest(BaseStatisticalTest):
    """Welch's t检验：比较两组数据的均值差异"""
    
    def __init__(self, alpha: float = 0.05):
        super().__init__("WelchTTest", alpha)
    
    def test(self, group1: List[float], group2: List[float]) -> Dict[str, Any]:
        """
        执行Welch's t检验
        
        Args:
            group1: 第一组数据
            group2: 第二组数据
            
        Returns:
            检验结果
        """
        if len(group1) < 2 or len(group2) < 2:
            return {
                'test_name': self.name,
                'statistic': 0.0,
                'p_value': 1.0,
                'significant': False,
                'error': 'Sample size too small'
            }
        
        try:
            # 执行<PERSON>'s t检验
            statistic, p_value = stats.ttest_ind(group1, group2, equal_var=False)
            
            # 计算自由度
            n1, n2 = len(group1), len(group2)
            s1, s2 = np.var(group1, ddof=1), np.var(group2, ddof=1)
            
            if s1 == 0 and s2 == 0:
                df = n1 + n2 - 2
            else:
                df = (s1/n1 + s2/n2)**2 / ((s1/n1)**2/(n1-1) + (s2/n2)**2/(n2-1))
            
            return {
                'test_name': self.name,
                'statistic': float(statistic),
                'p_value': float(p_value),
                'degrees_of_freedom': float(df),
                'significant': p_value < self.alpha,
                'alpha': self.alpha,
                'group1_mean': np.mean(group1),
                'group2_mean': np.mean(group2),
                'group1_std': np.std(group1, ddof=1),
                'group2_std': np.std(group2, ddof=1),
                'group1_size': n1,
                'group2_size': n2
            }
            
        except Exception as e:
            return {
                'test_name': self.name,
                'statistic': 0.0,
                'p_value': 1.0,
                'significant': False,
                'error': str(e)
            }


class CohenDEffect(BaseStatisticalTest):
    """Cohen's d效应量计算"""
    
    def __init__(self):
        super().__init__("CohenD", alpha=0.05)
    
    def test(self, group1: List[float], group2: List[float]) -> Dict[str, Any]:
        """
        计算Cohen's d效应量
        
        Args:
            group1: 第一组数据
            group2: 第二组数据
            
        Returns:
            效应量结果
        """
        if len(group1) < 2 or len(group2) < 2:
            return {
                'test_name': self.name,
                'cohens_d': 0.0,
                'effect_size': 'no_effect',
                'error': 'Sample size too small'
            }
        
        try:
            mean1, mean2 = np.mean(group1), np.mean(group2)
            std1, std2 = np.std(group1, ddof=1), np.std(group2, ddof=1)
            n1, n2 = len(group1), len(group2)
            
            # 计算合并标准差
            pooled_std = np.sqrt(((n1 - 1) * std1**2 + (n2 - 1) * std2**2) / (n1 + n2 - 2))
            
            if pooled_std == 0:
                cohens_d = 0.0
            else:
                cohens_d = (mean1 - mean2) / pooled_std
            
            # 效应量大小分类
            abs_d = abs(cohens_d)
            if abs_d < 0.2:
                effect_size = 'negligible'
            elif abs_d < 0.5:
                effect_size = 'small'
            elif abs_d < 0.8:
                effect_size = 'medium'
            else:
                effect_size = 'large'
            
            return {
                'test_name': self.name,
                'cohens_d': float(cohens_d),
                'effect_size': effect_size,
                'pooled_std': float(pooled_std),
                'mean_difference': float(mean1 - mean2),
                'interpretation': self._interpret_cohens_d(cohens_d)
            }
            
        except Exception as e:
            return {
                'test_name': self.name,
                'cohens_d': 0.0,
                'effect_size': 'no_effect',
                'error': str(e)
            }
    
    def _interpret_cohens_d(self, d: float) -> str:
        """解释Cohen's d值"""
        abs_d = abs(d)
        direction = "Group 1 better" if d > 0 else "Group 2 better"
        
        if abs_d < 0.2:
            return f"Negligible effect ({direction})"
        elif abs_d < 0.5:
            return f"Small effect ({direction})"
        elif abs_d < 0.8:
            return f"Medium effect ({direction})"
        else:
            return f"Large effect ({direction})"


class BonferroniCorrection:
    """Bonferroni多重比较校正"""
    
    def __init__(self, alpha: float = 0.05):
        self.alpha = alpha
    
    def correct_p_values(self, p_values: List[float]) -> Dict[str, Any]:
        """
        应用Bonferroni校正
        
        Args:
            p_values: 原始p值列表
            
        Returns:
            校正结果
        """
        if not p_values:
            return {
                'original_p_values': [],
                'corrected_p_values': [],
                'corrected_alpha': self.alpha,
                'significant_tests': [],
                'num_comparisons': 0
            }
        
        num_comparisons = len(p_values)
        corrected_alpha = self.alpha / num_comparisons
        corrected_p_values = [min(1.0, p * num_comparisons) for p in p_values]
        
        # 确定哪些检验在校正后仍然显著
        significant_tests = [i for i, p in enumerate(corrected_p_values) if p < self.alpha]
        
        return {
            'original_p_values': p_values,
            'corrected_p_values': corrected_p_values,
            'corrected_alpha': corrected_alpha,
            'significant_tests': significant_tests,
            'num_comparisons': num_comparisons,
            'family_wise_error_rate': self.alpha
        }


class ConfidenceInterval:
    """置信区间计算"""
    
    def __init__(self, confidence_level: float = 0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
    
    def calculate_mean_ci(self, data: List[float]) -> Dict[str, Any]:
        """
        计算均值的置信区间
        
        Args:
            data: 数据列表
            
        Returns:
            置信区间结果
        """
        if len(data) < 2:
            return {
                'mean': np.mean(data) if data else 0.0,
                'lower_bound': 0.0,
                'upper_bound': 0.0,
                'margin_of_error': 0.0,
                'error': 'Insufficient data'
            }
        
        try:
            mean = np.mean(data)
            std_err = stats.sem(data)  # 标准误
            
            # 使用t分布
            df = len(data) - 1
            t_critical = stats.t.ppf(1 - self.alpha/2, df)
            
            margin_of_error = t_critical * std_err
            lower_bound = mean - margin_of_error
            upper_bound = mean + margin_of_error
            
            return {
                'mean': float(mean),
                'lower_bound': float(lower_bound),
                'upper_bound': float(upper_bound),
                'margin_of_error': float(margin_of_error),
                'confidence_level': self.confidence_level,
                'sample_size': len(data),
                'standard_error': float(std_err)
            }
            
        except Exception as e:
            return {
                'mean': np.mean(data) if data else 0.0,
                'lower_bound': 0.0,
                'upper_bound': 0.0,
                'margin_of_error': 0.0,
                'error': str(e)
            }
    
    def calculate_proportion_ci(self, successes: int, total: int) -> Dict[str, Any]:
        """
        计算比例的置信区间
        
        Args:
            successes: 成功次数
            total: 总次数
            
        Returns:
            置信区间结果
        """
        if total == 0:
            return {
                'proportion': 0.0,
                'lower_bound': 0.0,
                'upper_bound': 0.0,
                'margin_of_error': 0.0,
                'error': 'No trials'
            }
        
        try:
            p = successes / total
            
            # Wilson score interval (更稳健)
            z = stats.norm.ppf(1 - self.alpha/2)
            
            denominator = 1 + z**2 / total
            center = (p + z**2 / (2 * total)) / denominator
            margin = z * np.sqrt(p * (1 - p) / total + z**2 / (4 * total**2)) / denominator
            
            lower_bound = max(0.0, center - margin)
            upper_bound = min(1.0, center + margin)
            
            return {
                'proportion': float(p),
                'lower_bound': float(lower_bound),
                'upper_bound': float(upper_bound),
                'margin_of_error': float(margin),
                'confidence_level': self.confidence_level,
                'successes': successes,
                'total': total
            }
            
        except Exception as e:
            return {
                'proportion': successes / total if total > 0 else 0.0,
                'lower_bound': 0.0,
                'upper_bound': 0.0,
                'margin_of_error': 0.0,
                'error': str(e)
            }


class StatisticalAnalyzer:
    """统计分析器：综合统计分析工具"""
    
    def __init__(self, alpha: float = 0.05, confidence_level: float = 0.95):
        self.alpha = alpha
        self.confidence_level = confidence_level
        
        self.t_test = WelchTTest(alpha)
        self.effect_size = CohenDEffect()
        self.bonferroni = BonferroniCorrection(alpha)
        self.ci_calculator = ConfidenceInterval(confidence_level)
    
    def compare_two_groups(self, group1: List[float], group2: List[float], 
                          group1_name: str = "Group1", group2_name: str = "Group2") -> Dict[str, Any]:
        """
        比较两组数据的完整统计分析
        
        Args:
            group1: 第一组数据
            group2: 第二组数据
            group1_name: 第一组名称
            group2_name: 第二组名称
            
        Returns:
            完整的比较结果
        """
        # t检验
        t_test_result = self.t_test.test(group1, group2)
        
        # 效应量
        effect_result = self.effect_size.test(group1, group2)
        
        # 置信区间
        ci1 = self.ci_calculator.calculate_mean_ci(group1)
        ci2 = self.ci_calculator.calculate_mean_ci(group2)
        
        # 描述性统计
        desc_stats = {
            group1_name: {
                'mean': np.mean(group1),
                'std': np.std(group1, ddof=1),
                'median': np.median(group1),
                'min': np.min(group1),
                'max': np.max(group1),
                'size': len(group1),
                'confidence_interval': ci1
            },
            group2_name: {
                'mean': np.mean(group2),
                'std': np.std(group2, ddof=1),
                'median': np.median(group2),
                'min': np.min(group2),
                'max': np.max(group2),
                'size': len(group2),
                'confidence_interval': ci2
            }
        }
        
        return {
            'descriptive_statistics': desc_stats,
            't_test': t_test_result,
            'effect_size': effect_result,
            'summary': self._generate_comparison_summary(t_test_result, effect_result, group1_name, group2_name)
        }
    
    def compare_multiple_groups(self, groups: Dict[str, List[float]]) -> Dict[str, Any]:
        """
        多组比较分析
        
        Args:
            groups: 组名到数据的映射
            
        Returns:
            多组比较结果
        """
        group_names = list(groups.keys())
        group_data = list(groups.values())
        
        if len(group_names) < 2:
            return {'error': 'Need at least 2 groups for comparison'}
        
        # 两两比较
        pairwise_comparisons = []
        p_values = []
        
        for i in range(len(group_names)):
            for j in range(i + 1, len(group_names)):
                name1, name2 = group_names[i], group_names[j]
                data1, data2 = group_data[i], group_data[j]
                
                comparison = self.compare_two_groups(data1, data2, name1, name2)
                pairwise_comparisons.append({
                    'groups': (name1, name2),
                    'comparison': comparison
                })
                
                p_values.append(comparison['t_test']['p_value'])
        
        # Bonferroni校正
        bonferroni_result = self.bonferroni.correct_p_values(p_values)
        
        # 添加校正后的显著性
        for i, comparison in enumerate(pairwise_comparisons):
            comparison['bonferroni_significant'] = i in bonferroni_result['significant_tests']
            comparison['corrected_p_value'] = bonferroni_result['corrected_p_values'][i]
        
        return {
            'pairwise_comparisons': pairwise_comparisons,
            'bonferroni_correction': bonferroni_result,
            'summary': self._generate_multiple_comparison_summary(pairwise_comparisons, bonferroni_result)
        }
    
    def _generate_comparison_summary(self, t_test: Dict, effect: Dict, 
                                   name1: str, name2: str) -> str:
        """生成两组比较的文字总结"""
        if 'error' in t_test:
            return f"无法比较{name1}和{name2}: {t_test['error']}"
        
        p_val = t_test['p_value']
        significant = t_test['significant']
        cohens_d = effect.get('cohens_d', 0)
        effect_size = effect.get('effect_size', 'unknown')
        
        summary = f"{name1} vs {name2}: "
        
        if significant:
            summary += f"存在显著差异 (p={p_val:.4f}, "
        else:
            summary += f"无显著差异 (p={p_val:.4f}, "
        
        summary += f"Cohen's d={cohens_d:.3f}, 效应量: {effect_size})"
        
        return summary
    
    def _generate_multiple_comparison_summary(self, comparisons: List, bonferroni: Dict) -> str:
        """生成多组比较的文字总结"""
        total_comparisons = len(comparisons)
        significant_before = sum(1 for comp in comparisons if comp['comparison']['t_test']['significant'])
        significant_after = len(bonferroni['significant_tests'])
        
        summary = f"进行了{total_comparisons}次两两比较。"
        summary += f"Bonferroni校正前有{significant_before}个显著差异，"
        summary += f"校正后有{significant_after}个显著差异。"
        
        if significant_after > 0:
            summary += "显著差异的组对："
            for i, comp in enumerate(comparisons):
                if comp['bonferroni_significant']:
                    groups = comp['groups']
                    summary += f" {groups[0]} vs {groups[1]},"
            summary = summary.rstrip(',')
        
        return summary
