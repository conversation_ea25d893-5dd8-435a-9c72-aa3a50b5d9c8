#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练真实的AFM模型用于智能体仿真
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pickle
import os
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AFMLayer(nn.Module):
    """AFM注意力层"""
    def __init__(self, embedding_size, attention_factor, l2_reg, dropout_rate):
        super(AFMLayer, self).__init__()
        self.attention_factor = attention_factor
        self.l2_reg = l2_reg
        
        self.attention_W = nn.Linear(embedding_size, attention_factor, bias=False)
        self.attention_b = nn.Parameter(torch.zeros(attention_factor))
        self.projection_h = nn.Linear(attention_factor, 1, bias=False)
        self.projection_p = nn.Linear(embedding_size, 1, bias=False)
        
        self.dropout = nn.Dropout(dropout_rate)
        
        # 初始化权重
        nn.init.xavier_uniform_(self.attention_W.weight)
        nn.init.xavier_uniform_(self.projection_h.weight)
        nn.init.xavier_uniform_(self.projection_p.weight)
    
    def forward(self, inputs):
        # inputs: list of embeddings [batch_size, 1, embedding_size]
        embeds = torch.cat(inputs, dim=1)  # [batch_size, field_num, embedding_size]
        
        # 计算所有特征对的交互
        row, col = [], []
        for i in range(len(inputs)):
            for j in range(i+1, len(inputs)):
                row.append(i)
                col.append(j)
        
        if len(row) == 0:
            return torch.zeros(embeds.size(0), 1).to(embeds.device)
        
        # 特征交互
        p = embeds[:, row]  # [batch_size, num_pairs, embedding_size]
        q = embeds[:, col]  # [batch_size, num_pairs, embedding_size]
        inner_product = p * q  # [batch_size, num_pairs, embedding_size]
        
        # 注意力机制
        attention_temp = torch.relu(self.attention_W(inner_product) + self.attention_b)
        attention_out = self.projection_h(attention_temp)  # [batch_size, num_pairs, 1]
        attention_out = torch.softmax(attention_out, dim=1)
        
        # 加权求和
        attention_out = attention_out * inner_product  # [batch_size, num_pairs, embedding_size]
        attention_out = torch.sum(attention_out, dim=1)  # [batch_size, embedding_size]
        
        # 投影到标量
        afm_out = self.projection_p(attention_out)  # [batch_size, 1]
        return afm_out

class RealAFM(nn.Module):
    """真实的AFM模型"""
    def __init__(self, user_emb_dim, item_emb_dim, attention_factor=8, l2_reg=0.00001, dropout_rate=0.5):
        super(RealAFM, self).__init__()
        
        self.user_emb_dim = user_emb_dim
        self.item_emb_dim = item_emb_dim
        
        # 线性层
        self.linear = nn.Linear(user_emb_dim + item_emb_dim, 1)
        
        # AFM层
        self.afm_layer = AFMLayer(user_emb_dim, attention_factor, l2_reg, dropout_rate)
        
        # 输出层
        self.sigmoid = nn.Sigmoid()
        
        # 初始化
        nn.init.xavier_uniform_(self.linear.weight)
        nn.init.zeros_(self.linear.bias)
    
    def forward(self, user_emb, item_emb):
        """
        前向传播
        user_emb: [batch_size, user_emb_dim]
        item_emb: [batch_size, item_emb_dim]
        """
        # 线性部分
        concat_emb = torch.cat([user_emb, item_emb], dim=1)
        linear_out = self.linear(concat_emb)
        
        # AFM部分 - 将嵌入转换为AFM层需要的格式
        user_emb_reshaped = user_emb.unsqueeze(1)  # [batch_size, 1, user_emb_dim]
        item_emb_reshaped = item_emb.unsqueeze(1)  # [batch_size, 1, item_emb_dim]
        
        # 为了AFM层，我们需要将用户和物品嵌入分割成多个field
        # 这里简化处理，将嵌入分成若干段
        field_size = min(8, self.user_emb_dim // 4)  # 每个field的大小
        user_fields = torch.split(user_emb_reshaped, field_size, dim=2)
        item_fields = torch.split(item_emb_reshaped, field_size, dim=2)
        
        # 组合所有fields
        all_fields = list(user_fields) + list(item_fields)
        
        # AFM交互
        afm_out = self.afm_layer(all_fields)
        
        # 组合输出
        output = linear_out + afm_out
        return self.sigmoid(output)

def create_synthetic_data(user_embeddings, item_embeddings, num_samples=10000):
    """创建合成训练数据"""
    np.random.seed(42)

    # 处理嵌入维度 - 展平到2D
    if len(user_embeddings.shape) == 3:
        user_embeddings = user_embeddings.reshape(user_embeddings.shape[0], -1)
    if len(item_embeddings.shape) == 3:
        item_embeddings = item_embeddings.reshape(item_embeddings.shape[0], -1)

    # 随机选择用户和物品
    user_indices = np.random.choice(len(user_embeddings), num_samples)
    item_indices = np.random.choice(len(item_embeddings), num_samples)

    X_user = user_embeddings[user_indices]
    X_item = item_embeddings[item_indices]

    # 创建标签 - 基于用户和物品嵌入的相似度
    similarities = np.sum(X_user * X_item, axis=1)
    # 添加噪声
    similarities += np.random.normal(0, 0.1, num_samples)
    # 转换为二分类标签
    y = (similarities > np.median(similarities)).astype(np.float32)

    return X_user, X_item, y

def train_afm_model():
    """训练AFM模型"""
    logging.info("开始训练真实AFM模型...")
    
    # 加载嵌入数据
    try:
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings = pickle.load(f)
        with open('itm_emb_np.pkl', 'rb') as f:
            item_embeddings = pickle.load(f)
        
        user_embeddings = np.array(user_embeddings)
        item_embeddings = np.array(item_embeddings)

        # 处理嵌入维度
        if len(user_embeddings.shape) == 3:
            user_embeddings = user_embeddings.reshape(user_embeddings.shape[0], -1)
        if len(item_embeddings.shape) == 3:
            item_embeddings = item_embeddings.reshape(item_embeddings.shape[0], -1)

        logging.info(f"用户嵌入形状: {user_embeddings.shape}")
        logging.info(f"物品嵌入形状: {item_embeddings.shape}")
        
    except Exception as e:
        logging.error(f"加载嵌入文件失败: {e}")
        return None
    
    # 创建训练数据
    X_user, X_item, y = create_synthetic_data(user_embeddings, item_embeddings)
    
    # 划分训练集和验证集
    X_user_train, X_user_val, X_item_train, X_item_val, y_train, y_val = train_test_split(
        X_user, X_item, y, test_size=0.2, random_state=42
    )
    
    # 转换为PyTorch张量
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"使用设备: {device}")
    
    X_user_train = torch.FloatTensor(X_user_train).to(device)
    X_item_train = torch.FloatTensor(X_item_train).to(device)
    y_train = torch.FloatTensor(y_train).to(device)
    
    X_user_val = torch.FloatTensor(X_user_val).to(device)
    X_item_val = torch.FloatTensor(X_item_val).to(device)
    y_val = torch.FloatTensor(y_val).to(device)
    
    # 创建数据加载器
    train_dataset = TensorDataset(X_user_train, X_item_train, y_train)
    val_dataset = TensorDataset(X_user_val, X_item_val, y_val)
    
    train_loader = DataLoader(train_dataset, batch_size=256, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=256, shuffle=False)
    
    # 创建模型
    model = RealAFM(
        user_emb_dim=user_embeddings.shape[1],
        item_emb_dim=item_embeddings.shape[1],
        attention_factor=8,
        l2_reg=0.00001,
        dropout_rate=0.3
    ).to(device)
    
    # 优化器和损失函数
    optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=0.00001)
    criterion = nn.BCELoss()
    
    # 训练循环
    num_epochs = 50
    best_auc = 0
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0
        for batch_user, batch_item, batch_y in train_loader:
            optimizer.zero_grad()
            
            outputs = model(batch_user, batch_item).squeeze()
            loss = criterion(outputs, batch_y)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for batch_user, batch_item, batch_y in val_loader:
                outputs = model(batch_user, batch_item).squeeze()
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()
                
                val_preds.extend(outputs.cpu().numpy())
                val_targets.extend(batch_y.cpu().numpy())
        
        # 计算AUC
        val_auc = roc_auc_score(val_targets, val_preds)
        
        if (epoch + 1) % 10 == 0:
            logging.info(f'Epoch [{epoch+1}/{num_epochs}], '
                        f'Train Loss: {train_loss/len(train_loader):.4f}, '
                        f'Val Loss: {val_loss/len(val_loader):.4f}, '
                        f'Val AUC: {val_auc:.4f}')
        
        # 保存最佳模型
        if val_auc > best_auc:
            best_auc = val_auc
            torch.save({
                'model_state_dict': model.state_dict(),
                'user_emb_dim': user_embeddings.shape[1],
                'item_emb_dim': item_embeddings.shape[1],
                'best_auc': best_auc
            }, 'real_afm_model.pth')
    
    logging.info(f"训练完成！最佳验证AUC: {best_auc:.4f}")
    logging.info("模型已保存为 real_afm_model.pth")
    
    return model, user_embeddings, item_embeddings

if __name__ == "__main__":
    train_afm_model()
