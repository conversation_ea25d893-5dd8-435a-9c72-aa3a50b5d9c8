#!/usr/bin/env python3
"""
简化的数据处理器测试
"""
import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional

def test_assist0910_data():
    """测试Assist0910数据集"""
    print("=== 测试Assist0910数据集 ===")
    
    dataset_path = Path("data/datasets/Assist0910")
    
    if not dataset_path.exists():
        print(f"数据集路径不存在: {dataset_path}")
        return False
    
    # 检查配置文件
    config_file = dataset_path / "config.json"
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"配置信息: {config}")
    else:
        print("配置文件不存在")
        return False
    
    # 检查响应数据
    response_file = dataset_path / "response.csv"
    if response_file.exists():
        # 读取前几行
        df = pd.read_csv(response_file, header=None, nrows=10)
        print(f"响应数据样例 (前10行):")
        print(df)
        
        # 获取完整统计
        df_full = pd.read_csv(response_file, header=None)
        df_full.columns = ['student_id', 'exercise_id', 'score']
        
        print(f"响应数据统计:")
        print(f"  总记录数: {len(df_full)}")
        print(f"  学生数: {df_full['student_id'].nunique()}")
        print(f"  题目数: {df_full['exercise_id'].nunique()}")
        print(f"  平均分: {df_full['score'].mean():.3f}")
        print(f"  分数分布: {df_full['score'].value_counts().to_dict()}")
    else:
        print("响应数据文件不存在")
        return False
    
    # 检查Q矩阵
    q_matrix_file = dataset_path / "q_matrix.csv"
    if q_matrix_file.exists():
        q_matrix = pd.read_csv(q_matrix_file, header=None)
        print(f"Q矩阵信息:")
        print(f"  形状: {q_matrix.shape}")
        print(f"  知识点数: {q_matrix.shape[1]}")
        print(f"  每题平均知识点数: {q_matrix.sum(axis=1).mean():.2f}")
        print(f"  每知识点平均题目数: {q_matrix.sum(axis=0).mean():.2f}")
    else:
        print("Q矩阵文件不存在")
    
    # 检查嵌入文件
    stu_emb_dir = dataset_path / "stu_emb"
    exer_emb_dir = dataset_path / "exer_emb"
    
    if stu_emb_dir.exists():
        emb_files = list(stu_emb_dir.glob("*.npy"))
        print(f"学生嵌入文件: {len(emb_files)} 个")
        if emb_files:
            emb = np.load(emb_files[0])
            print(f"  嵌入维度: {emb.shape}")
    
    if exer_emb_dir.exists():
        emb_files = list(exer_emb_dir.glob("*.npy"))
        print(f"题目嵌入文件: {len(emb_files)} 个")
        if emb_files:
            emb = np.load(emb_files[0])
            print(f"  嵌入维度: {emb.shape}")
    
    return True

def test_all_datasets():
    """测试所有数据集"""
    print("=== 发现所有数据集 ===")
    
    datasets_dir = Path("data/datasets")
    
    if not datasets_dir.exists():
        print(f"数据集目录不存在: {datasets_dir}")
        return
    
    datasets = []
    for dataset_dir in datasets_dir.iterdir():
        if not dataset_dir.is_dir():
            continue
        
        print(f"\n检查数据集: {dataset_dir.name}")
        
        # 检查必要文件
        has_config = (dataset_dir / "config.json").exists()
        has_response = (dataset_dir / "response.csv").exists()
        has_q_matrix = (dataset_dir / "q_matrix.csv").exists()
        
        print(f"  配置文件: {'✓' if has_config else '✗'}")
        print(f"  响应数据: {'✓' if has_response else '✗'}")
        print(f"  Q矩阵: {'✓' if has_q_matrix else '✗'}")
        
        if has_response:
            try:
                df = pd.read_csv(dataset_dir / "response.csv", header=None)
                df.columns = ['student_id', 'exercise_id', 'score']
                
                print(f"  学生数: {df['student_id'].nunique()}")
                print(f"  题目数: {df['exercise_id'].nunique()}")
                print(f"  记录数: {len(df)}")
                
                datasets.append({
                    'name': dataset_dir.name,
                    'path': str(dataset_dir),
                    'student_num': df['student_id'].nunique(),
                    'exercise_num': df['exercise_id'].nunique(),
                    'response_num': len(df),
                    'has_config': has_config,
                    'has_q_matrix': has_q_matrix
                })
                
            except Exception as e:
                print(f"  读取失败: {e}")
    
    print(f"\n=== 数据集汇总 ===")
    print(f"发现 {len(datasets)} 个有效数据集:")
    for ds in datasets:
        print(f"  {ds['name']}: {ds['student_num']}学生, {ds['exercise_num']}题目, {ds['response_num']}记录")

def test_data_preparation():
    """测试数据准备过程"""
    print("\n=== 测试数据准备 ===")
    
    dataset_path = Path("data/datasets/Assist0910")
    response_file = dataset_path / "response.csv"
    
    if not response_file.exists():
        print("Assist0910数据集不存在，跳过测试")
        return
    
    # 加载数据
    df = pd.read_csv(response_file, header=None)
    df.columns = ['student_id', 'exercise_id', 'score']
    
    print(f"原始数据: {len(df)} 条记录")
    
    # 数据预处理
    df_clean = df.dropna()
    df_clean['score'] = df_clean['score'].clip(0, 1)
    
    print(f"清洗后数据: {len(df_clean)} 条记录")
    
    # 划分训练测试集
    np.random.seed(42)
    unique_students = df_clean['student_id'].unique()
    np.random.shuffle(unique_students)
    
    split_idx = int(len(unique_students) * 0.8)
    train_students = unique_students[:split_idx]
    test_students = unique_students[split_idx:]
    
    train_df = df_clean[df_clean['student_id'].isin(train_students)]
    test_df = df_clean[df_clean['student_id'].isin(test_students)]
    
    print(f"训练集: {len(train_df)} 条记录, {len(train_students)} 个学生")
    print(f"测试集: {len(test_df)} 条记录, {len(test_students)} 个学生")
    
    # ID重编码
    all_students = sorted(df_clean['student_id'].unique())
    all_exercises = sorted(df_clean['exercise_id'].unique())
    
    student_id_map = {old_id: new_id for new_id, old_id in enumerate(all_students)}
    exercise_id_map = {old_id: new_id for new_id, old_id in enumerate(all_exercises)}
    
    print(f"ID映射: {len(student_id_map)} 个学生, {len(exercise_id_map)} 个题目")
    
    return True

if __name__ == "__main__":
    print("开始测试数据处理器...")
    
    # 测试单个数据集
    test_assist0910_data()
    
    # 测试所有数据集
    test_all_datasets()
    
    # 测试数据准备
    test_data_preparation()
    
    print("\n测试完成!")
