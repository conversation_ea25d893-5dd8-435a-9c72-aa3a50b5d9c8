"""
LLM服务 - 接入硅基流动API生成诊断文本
"""
import httpx
import json
import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class LLMService:
    """LLM服务类，用于生成诊断报告文本"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or settings.SILICONFLOW_API_KEY or "sk-nncubqwtmpkzknkqepoyhqapagmllicumpbpwsxsqaapvtsz"
        self.base_url = settings.LLM_BASE_URL
        self.model = settings.LLM_MODEL
        
    async def generate_diagnosis_report(self, diagnosis_data: Dict[str, Any]) -> Dict[str, str]:
        """
        基于诊断数据生成用户友好的诊断报告

        Args:
            diagnosis_data: 诊断数据，包含学生ID、知识点掌握情况等

        Returns:
            包含不同类型诊断文本的字典
        """
        student_id = diagnosis_data.get("student_id", "未知学生")
        logger.info(f"🤖 开始为学生 {student_id} 生成LLM诊断报告")
        logger.info(f"📊 输入诊断数据: {diagnosis_data}")

        try:
            # 构建提示词
            prompt = self._build_diagnosis_prompt(diagnosis_data)
            logger.info(f"📝 构建的提示词: {prompt[:200]}...")

            # 调用LLM API
            response = await self._call_llm_api(prompt)
            logger.info(f"🔄 LLM API响应: {response}")

            # 解析响应
            diagnosis_text = self._parse_llm_response(response)
            logger.info(f"📋 解析后的诊断文本: {diagnosis_text}")

            result = {
                "summary": diagnosis_text.get("summary", ""),
                "detailed_analysis": diagnosis_text.get("detailed_analysis", ""),
                "recommendations": diagnosis_text.get("recommendations", ""),
                "learning_suggestions": diagnosis_text.get("learning_suggestions", ""),
                "generated_at": datetime.now().isoformat()
            }

            logger.info(f"✅ LLM诊断报告生成成功: {result}")
            return result

        except Exception as e:
            logger.error(f"❌ 生成诊断报告失败: {e}")
            logger.error(f"📍 错误详情: {str(e)}")
            # 检查是否是503错误(服务不可用)
            if "503" in str(e) or "Service Unavailable" in str(e):
                logger.warning("⚠️ LLM服务暂时不可用，使用智能备用报告")
            # 返回智能备用文本
            fallback_report = self._generate_fallback_report(diagnosis_data)
            logger.info(f"🔄 返回备用报告: {fallback_report}")
            return fallback_report

    async def generate_diagnosis_report_stream(self, diagnosis_data: Dict[str, Any]):
        """
        基于诊断数据生成用户友好的诊断报告(流式输出)

        Args:
            diagnosis_data: 诊断数据，包含学生ID、知识点掌握情况等

        Yields:
            流式输出的报告内容
        """
        try:
            # 构建提示词
            prompt = self._build_diagnosis_prompt_with_thinking(diagnosis_data)

            # 调用LLM API(流式)
            async for chunk in self._call_llm_api_stream(prompt):
                yield chunk

        except Exception as e:
            logger.error(f"生成流式诊断报告失败: {e}")
            # 返回备用报告
            fallback_report = self._generate_fallback_report(diagnosis_data)
            yield fallback_report.get("summary", "诊断报告生成中，请稍后刷新查看。")

    def _build_diagnosis_prompt_with_thinking(self, diagnosis_data: Dict[str, Any]) -> str:
        """构建包含思考过程的诊断提示词(适用于DeepSeek等支持思考的模型)"""
        student_id = diagnosis_data.get("student_id", "未知学生")
        overall_ability = diagnosis_data.get("overall_ability", 0)
        knowledge_diagnosis = diagnosis_data.get("knowledge_diagnosis", {})
        cognitive_vector = diagnosis_data.get("cognitive_vector", [])

        # 分析知识点掌握情况
        mastered_kcs = []
        partial_kcs = []
        weak_kcs = []

        # 计算详细统计信息
        total_kcs = len(knowledge_diagnosis)
        mastery_scores = []

        for kc_name, kc_data in knowledge_diagnosis.items():
            mastery_prob = kc_data.get("mastery_probability", 0)
            mastery_scores.append(mastery_prob)
            confidence = kc_data.get("confidence", 0)
            data_source = kc_data.get("data_source", "模拟数据")

            if mastery_prob > 0.7:
                mastered_kcs.append(f"{kc_name}(掌握度:{mastery_prob:.2f}, 置信度:{confidence:.2f})")
            elif mastery_prob > 0.5:
                partial_kcs.append(f"{kc_name}(掌握度:{mastery_prob:.2f}, 置信度:{confidence:.2f})")
            else:
                weak_kcs.append(f"{kc_name}(掌握度:{mastery_prob:.2f}, 置信度:{confidence:.2f})")

        # 计算学习稳定性和发展潜力
        mastery_variance = sum((score - overall_ability) ** 2 for score in mastery_scores) / len(mastery_scores) if mastery_scores else 0
        learning_stability = "稳定" if mastery_variance < 0.05 else "波动较大" if mastery_variance < 0.15 else "极不稳定"

        # 能力等级评定
        ability_level = "优秀" if overall_ability > 0.8 else "良好" if overall_ability > 0.6 else "中等" if overall_ability > 0.4 else "需要提升"

        prompt = f"""你是一位资深的教育心理学专家和认知诊断分析师，拥有丰富的个性化教育经验。请基于以下ORCDF认知诊断模型的分析结果，为学生生成一份专业、详细、个性化的学习诊断报告。

【诊断数据分析】
学生编号: {student_id}
整体认知能力: {overall_ability:.3f} ({ability_level}水平)
知识点总数: {total_kcs}个
学习稳定性: {learning_stability} (方差: {mastery_variance:.3f})

【知识掌握分布】
✅ 已熟练掌握 ({len(mastered_kcs)}个): {', '.join(mastered_kcs) if mastered_kcs else '暂无'}
🔄 部分掌握 ({len(partial_kcs)}个): {', '.join(partial_kcs) if partial_kcs else '暂无'}
❌ 薄弱环节 ({len(weak_kcs)}个): {', '.join(weak_kcs) if weak_kcs else '暂无'}

【认知向量】: {cognitive_vector[:5] if len(cognitive_vector) > 5 else cognitive_vector}...

请首先在<think></think>标签内展示你的专业分析思考过程，然后提供结构化的JSON诊断报告。

<think>
作为教育专家，我需要从多个维度深入分析这位学生的学习状况：

1. 【整体能力评估】
   - 能力水平: {overall_ability:.3f} 属于{ability_level}水平
   - 这个分数意味着学生在认知能力方面的表现如何？
   - 与同龄学生相比处于什么位置？

2. 【知识结构分析】
   - 掌握知识点比例: {len(mastered_kcs)}/{total_kcs} = {len(mastered_kcs)/total_kcs*100:.1f}%
   - 学习稳定性: {learning_stability}
   - 知识掌握是否均衡？是否存在明显的强弱分化？

3. 【学习特征识别】
   - 从掌握的知识点看，学生的学习优势在哪里？
   - 薄弱环节反映了什么学习问题？
   - 学习模式是什么类型？(全面型/偏科型/基础薄弱型等)

4. 【发展潜力评估】
   - 基于当前表现，学生的提升空间有多大？
   - 哪些是可以快速改善的？哪些需要长期努力？

5. 【个性化建议制定】
   - 针对具体薄弱点，应该采用什么学习策略？
   - 如何巩固已掌握的知识？
   - 学习计划应该如何安排优先级？
</think>

请严格按照以下JSON格式回复，确保内容专业、具体、可操作：

{{
  "summary": "基于ORCDF认知诊断模型分析，简要总结学生的整体学习状况和主要特征",
  "detailed_analysis": {{
    "cognitive_profile": "学生的认知特征画像，包括学习类型、思维特点等",
    "strength_analysis": "详细分析学生的学习优势和强项知识点",
    "weakness_analysis": "深入分析薄弱环节的根本原因和影响",
    "learning_pattern": "学生的学习模式和习惯特征分析"
  }},
  "ability_assessment": {{
    "current_level": "{ability_level}",
    "percentile_rank": "在同龄学生中的大致排名位置",
    "development_potential": "学习发展潜力评估",
    "key_indicators": ["关键能力指标1", "关键能力指标2", "关键能力指标3"]
  }},
  "personalized_recommendations": {{
    "priority_actions": ["最优先需要采取的学习行动", "次优先行动"],
    "study_strategies": ["针对性学习策略1", "针对性学习策略2", "针对性学习策略3"],
    "improvement_methods": ["具体的改进方法1", "具体的改进方法2"],
    "practice_suggestions": ["练习建议1", "练习建议2"]
  }},
  "learning_roadmap": {{
    "immediate_goals": ["1-2周内的学习目标"],
    "short_term_plan": ["1-3个月的学习计划"],
    "long_term_vision": ["半年到一年的发展目标"],
    "milestone_checkpoints": ["重要的学习里程碑检查点"]
  }},
  "resource_recommendations": {{
    "study_materials": ["推荐的学习资料1", "推荐的学习资料2"],
    "practice_resources": ["练习资源1", "练习资源2"],
    "supplementary_tools": ["辅助学习工具1", "辅助学习工具2"]
  }},
  "motivation_support": {{
    "encouragement": "基于学生具体表现的个性化鼓励",
    "confidence_building": "提升学习信心的具体建议",
    "progress_tracking": "如何跟踪和庆祝学习进步"
  }}
}}

学生数据：
- 学生编号：{student_id}
- 整体能力：{overall_ability:.1%}
- 掌握良好：{', '.join(mastered_kcs) if mastered_kcs else '暂无'}
- 需要加强：{', '.join(partial_kcs + weak_kcs) if (partial_kcs + weak_kcs) else '暂无'}

请确保返回的是有效的JSON格式，不要包含任何其他内容。"""
        return prompt

    def _build_diagnosis_prompt(self, diagnosis_data: Dict[str, Any]) -> str:
        """构建诊断提示词"""
        student_id = diagnosis_data.get("student_id", "未知学生")
        overall_ability = diagnosis_data.get("overall_ability", 0)
        knowledge_diagnosis = diagnosis_data.get("knowledge_diagnosis", {})
        
        # 分析知识点掌握情况
        mastered_kcs = []
        partial_kcs = []
        weak_kcs = []
        
        for kc_name, kc_data in knowledge_diagnosis.items():
            mastery_prob = kc_data.get("mastery_probability", 0)
            if mastery_prob > 0.7:
                mastered_kcs.append(f"{kc_name}({mastery_prob:.2f})")
            elif mastery_prob > 0.5:
                partial_kcs.append(f"{kc_name}({mastery_prob:.2f})")
            else:
                weak_kcs.append(f"{kc_name}({mastery_prob:.2f})")
        
        prompt = f"""你是专业的教育分析师，基于学生认知诊断数据生成详细学习报告。

学生信息：
- ID: {student_id}
- 整体能力: {overall_ability:.2f}
- 掌握知识点: {', '.join(mastered_kcs) if mastered_kcs else '暂无'}
- 部分掌握: {', '.join(partial_kcs) if partial_kcs else '暂无'}
- 薄弱知识点: {', '.join(weak_kcs) if weak_kcs else '暂无'}

请生成JSON格式报告：
{{
  "summary": "学习现状总结，包含整体评价和主要特点，80-120字",
  "detailed_analysis": "详细分析各知识点掌握情况、学习特点和能力分布，200-300字",
  "recommendations": "针对薄弱环节的具体学习建议和改进方案，150-200字",
  "learning_suggestions": "推荐的学习方法、策略和资源，包含具体可操作的建议，150-200字"
}}

要求：内容详实、建议具体、语言友好鼓励。直接返回JSON。"""
        return prompt
    
    async def _call_llm_api(self, prompt: str, max_retries: int = 3) -> Dict[str, Any]:
        """调用硅基流动API，带重试机制"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,  # 适中温度，平衡创造性和一致性
            "max_tokens": 2500,  # 大幅增加token数量，支持更详细的报告
            "stream": False
        }

        logger.info(f"🚀 开始调用LLM API: {self.base_url}")
        logger.info(f"🤖 使用模型: {self.model}")
        logger.info(f"📝 Prompt长度: {len(prompt)} 字符")
        logger.info(f"🔧 参数: temperature={data['temperature']}, max_tokens={data['max_tokens']}")

        last_error = None
        for attempt in range(max_retries + 1):
            try:
                logger.info(f"📡 第 {attempt + 1} 次API调用...")
                start_time = time.time()

                # 增加超时时间到60秒
                timeout = httpx.Timeout(60.0, connect=10.0)
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(
                        self.base_url,
                        headers=headers,
                        json=data
                    )

                    end_time = time.time()
                    duration = end_time - start_time
                    logger.info(f"⏱️ API响应时间: {duration:.2f} 秒")

                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"✅ API调用成功!")

                        # 记录响应内容长度
                        content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
                        logger.info(f"📄 响应内容长度: {len(content)} 字符")
                        logger.info(f"📝 响应内容预览: {content[:200]}...")

                        return result
                    elif response.status_code == 503:
                        logger.warning(f"API服务暂时不可用 (503)，尝试 {attempt + 1}/{max_retries + 1}")
                        if attempt < max_retries:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                        else:
                            raise httpx.HTTPStatusError(
                                f"API服务持续不可用: {response.status_code}",
                                request=response.request,
                                response=response
                            )
                    else:
                        response.raise_for_status()

            except httpx.TimeoutException as e:
                last_error = e
                logger.warning(f"API请求超时，尝试 {attempt + 1}/{max_retries + 1}")
                if attempt < max_retries:
                    # 指数退避，等待更长时间
                    wait_time = 2 ** attempt + 1
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    continue
            except httpx.HTTPStatusError as e:
                last_error = e
                if e.response.status_code in [503, 502, 429] and attempt < max_retries:
                    wait_time = 2 ** attempt + 2
                    logger.warning(f"{e.response.status_code}错误，等待 {wait_time} 秒后重试 {attempt + 1}/{max_retries + 1}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"HTTP错误 {e.response.status_code}: {e.response.text}")
                    raise
            except Exception as e:
                last_error = e
                logger.error(f"API调用异常: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(1)
                    continue
                break

        # 如果所有重试都失败了，抛出最后一个错误
        if last_error:
            raise last_error
        else:
            raise Exception("API调用失败，原因未知")

    async def _call_llm_api_stream(self, prompt: str, max_retries: int = 3):
        """调用LLM API(流式输出)"""
        import httpx
        import asyncio

        # 调用真实LLM API进行流式诊断
        logger.info("调用真实LLM API进行流式诊断...")
        logger.info(f"使用模型: {self.model}")
        logger.info(f"API URL: {self.base_url}")
        logger.info(f"提示词长度: {len(prompt)} 字符")

        # 以下是真实API调用代码(暂时注释)
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 2000,
            "stream": True
        }

        last_error = None
        for attempt in range(max_retries + 1):
            try:
                timeout = httpx.Timeout(120.0, connect=10.0)
                async with httpx.AsyncClient(timeout=timeout) as client:
                    async with client.stream(
                        "POST",
                        self.base_url,
                        headers=headers,
                        json=data
                    ) as response:
                        if response.status_code == 200:
                            logger.info("开始接收流式响应...")
                            async for line in response.aiter_lines():
                                logger.debug(f"收到行: {line}")
                                if line.startswith("data: "):
                                    data_str = line[6:]  # 移除 "data: " 前缀
                                    logger.debug(f"数据内容: {data_str}")
                                    if data_str.strip() == "[DONE]":
                                        logger.info("流式响应完成")
                                        break
                                    try:
                                        chunk_data = json.loads(data_str)
                                        logger.debug(f"解析的数据: {chunk_data}")
                                        if "choices" in chunk_data and len(chunk_data["choices"]) > 0:
                                            delta = chunk_data["choices"][0].get("delta", {})
                                            if "content" in delta:
                                                content = delta["content"]
                                                logger.debug(f"输出内容: {repr(content)}")
                                                # 确保内容不为None且不为空
                                                if content is not None and content != "":
                                                    yield content
                                    except json.JSONDecodeError as e:
                                        logger.warning(f"JSON解析失败: {e}, 数据: {data_str}")
                                        continue
                            return
                        else:
                            response.raise_for_status()

            except Exception as e:
                last_error = e
                logger.warning(f"流式API请求失败，尝试 {attempt + 1}/{max_retries + 1}: {e}")
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)
                    continue

        # 如果所有重试都失败了，返回备用内容
        logger.error(f"流式API调用失败: {last_error}")
        yield "抱歉，AI诊断服务暂时不可用，请稍后再试。"
    
    def _parse_llm_response(self, response: Dict[str, Any]) -> Dict[str, str]:
        """解析LLM响应"""
        logger.info(f"🔍 开始解析LLM响应: {response}")

        try:
            content = response["choices"][0]["message"]["content"]
            logger.info(f"📄 原始LLM内容: {content}")

            # 清理内容，移除markdown代码块标记和思考标签
            content = content.strip()

            # 移除思考标签内容
            if "<think>" in content and "</think>" in content:
                start_idx = content.find("<think>")
                end_idx = content.find("</think>") + 8
                content = content[:start_idx] + content[end_idx:]
                logger.info("🧹 移除了思考标签内容")

            if content.startswith("```json"):
                content = content[7:]  # 移除 ```json
                logger.info("🧹 移除了```json标记")
            if content.startswith("```"):
                content = content[3:]   # 移除 ```
                logger.info("🧹 移除了```标记")
            if content.endswith("```"):
                content = content[:-3]  # 移除结尾的 ```
                logger.info("🧹 移除了结尾```标记")
            content = content.strip()

            logger.info(f"🧹 清理后的内容: {content}")

            # 尝试解析JSON
            parsed_json = json.loads(content)
            logger.info(f"✅ JSON解析成功: {parsed_json}")

            # 处理LLM返回的数据格式
            detailed_analysis_data = parsed_json.get("detailed_analysis", "")
            # 优先使用LLM直接返回的字段，如果没有则尝试结构化字段
            recommendations_data = parsed_json.get("recommendations", "") or parsed_json.get("personalized_recommendations", {})
            learning_plan_data = parsed_json.get("learning_suggestions", "") or parsed_json.get("learning_roadmap", {})

            logger.info(f"📊 详细分析数据: {detailed_analysis_data}")
            logger.info(f"💡 个性化建议数据: {recommendations_data}")
            logger.info(f"🗺️ 学习路径数据: {learning_plan_data}")

            # 如果是字符串，直接使用；如果是对象，则格式化
            formatted_analysis = detailed_analysis_data if isinstance(detailed_analysis_data, str) else self._format_detailed_analysis(detailed_analysis_data)
            formatted_recommendations = recommendations_data if isinstance(recommendations_data, str) else self._format_recommendations(recommendations_data)
            formatted_learning_plan = learning_plan_data if isinstance(learning_plan_data, str) else self._format_learning_plan(learning_plan_data)

            logger.info(f"📝 格式化后的详细分析: {formatted_analysis}")
            logger.info(f"📝 格式化后的个性化建议: {formatted_recommendations}")
            logger.info(f"📝 格式化后的学习计划: {formatted_learning_plan}")

            result = {
                "summary": parsed_json.get("summary", ""),
                "detailed_analysis": formatted_analysis,
                "recommendations": formatted_recommendations,
                "learning_suggestions": formatted_learning_plan,
                "ability_assessment": parsed_json.get("ability_assessment", {}),
                "resource_recommendations": parsed_json.get("resource_recommendations", {}),
                "motivation_support": parsed_json.get("motivation_support", {}),
                "generated_at": datetime.now().isoformat()
            }

            logger.info(f"✅ 最终解析结果: {result}")
            return result

        except (KeyError, json.JSONDecodeError) as e:
            logger.warning(f"解析LLM响应失败: {e}")
            # 如果JSON解析失败，尝试智能提取文本内容
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
            return self._extract_text_from_content(content)
        except Exception as e:
            logger.error(f"解析LLM响应时发生未知错误: {e}")
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "")
            return self._extract_text_from_content(content)

    def _format_detailed_analysis(self, analysis_data: Dict) -> str:
        """格式化详细分析数据"""
        if not analysis_data:
            return "详细分析数据处理中..."

        formatted = []
        if "cognitive_profile" in analysis_data:
            formatted.append(f"🧠 认知特征: {analysis_data['cognitive_profile']}")
        if "strength_analysis" in analysis_data:
            formatted.append(f"💪 优势分析: {analysis_data['strength_analysis']}")
        if "weakness_analysis" in analysis_data:
            formatted.append(f"📈 改进空间: {analysis_data['weakness_analysis']}")
        if "learning_pattern" in analysis_data:
            formatted.append(f"📚 学习模式: {analysis_data['learning_pattern']}")

        return "\n\n".join(formatted) if formatted else str(analysis_data)

    def _format_recommendations(self, recommendations_data: Dict) -> str:
        """格式化推荐建议数据"""
        logger.info(f"💡 开始格式化个性化建议: {recommendations_data}")

        if not recommendations_data:
            logger.info("⚠️ 个性化建议数据为空，使用默认内容")
            return "基于您的诊断结果，建议重点关注薄弱知识点的学习，通过针对性练习来提升掌握程度。同时保持已掌握知识点的复习，确保知识体系的完整性。"

        # 如果是字符串，直接返回
        if isinstance(recommendations_data, str):
            logger.info(f"📝 个性化建议是字符串格式: {recommendations_data}")
            return recommendations_data

        formatted = []
        if "priority_actions" in recommendations_data:
            actions = recommendations_data["priority_actions"]
            if isinstance(actions, list):
                formatted.append("🎯 优先行动:\n" + "\n".join(f"• {action}" for action in actions))
                logger.info(f"✅ 添加优先行动: {actions}")

        if "study_strategies" in recommendations_data:
            strategies = recommendations_data["study_strategies"]
            if isinstance(strategies, list):
                formatted.append("📖 学习策略:\n" + "\n".join(f"• {strategy}" for strategy in strategies))
                logger.info(f"✅ 添加学习策略: {strategies}")

        if "improvement_methods" in recommendations_data:
            methods = recommendations_data["improvement_methods"]
            if isinstance(methods, list):
                formatted.append("🔧 改进方法:\n" + "\n".join(f"• {method}" for method in methods))

        return "\n\n".join(formatted) if formatted else str(recommendations_data)

    def _format_learning_plan(self, plan_data: Dict) -> str:
        """格式化学习计划数据"""
        logger.info(f"🗺️ 开始格式化学习计划: {plan_data}")

        if not plan_data:
            logger.info("⚠️ 学习计划数据为空，使用默认内容")
            return "建议制定个性化学习计划：\n\n🎯 短期目标：重点突破薄弱知识点\n📅 学习安排：每日30-60分钟专项练习\n📚 学习方法：理论学习与实践练习相结合\n🔄 复习计划：定期回顾已掌握的知识点"

        # 如果是字符串，直接返回
        if isinstance(plan_data, str):
            logger.info(f"📝 学习计划是字符串格式: {plan_data}")
            return plan_data

        formatted = []
        if "immediate_goals" in plan_data:
            goals = plan_data["immediate_goals"]
            if isinstance(goals, list):
                formatted.append("🎯 近期目标:\n" + "\n".join(f"• {goal}" for goal in goals))
                logger.info(f"✅ 添加近期目标: {goals}")

        if "short_term_plan" in plan_data:
            plan = plan_data["short_term_plan"]
            if isinstance(plan, list):
                formatted.append("📅 短期计划:\n" + "\n".join(f"• {item}" for item in plan))
                logger.info(f"✅ 添加短期计划: {plan}")

        if "long_term_vision" in plan_data:
            vision = plan_data["long_term_vision"]
            if isinstance(vision, list):
                formatted.append("🌟 长期愿景:\n" + "\n".join(f"• {item}" for item in vision))
                logger.info(f"✅ 添加长期愿景: {vision}")

        result = "\n\n".join(formatted) if formatted else str(plan_data)
        logger.info(f"📝 最终格式化的学习计划: {result}")
        return result

    def _extract_text_from_content(self, content: str) -> Dict[str, str]:
        """从文本内容中智能提取诊断信息"""
        if not content:
            return {
                "summary": "诊断报告生成中...",
                "detailed_analysis": "系统正在分析您的学习数据，详细报告将很快生成。",
                "recommendations": "请稍后查看个性化学习建议。",
                "learning_suggestions": "请稍后查看学习方法建议。",
                "generated_at": datetime.now().isoformat()
            }

        # 尝试从文本中提取不同部分
        lines = content.split('\n')
        summary = ""
        detailed_analysis = ""
        recommendations = ""
        learning_suggestions = ""

        current_section = "summary"
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测章节标题
            if "详细分析" in line or "detailed_analysis" in line.lower():
                current_section = "detailed_analysis"
                continue
            elif "建议" in line or "recommendation" in line.lower():
                current_section = "recommendations"
                continue
            elif "学习方法" in line or "learning_suggestion" in line.lower():
                current_section = "learning_suggestions"
                continue

            # 将内容添加到相应章节
            if current_section == "summary" and not summary:
                summary = line[:200]  # 限制摘要长度
            elif current_section == "detailed_analysis":
                detailed_analysis += line + " "
            elif current_section == "recommendations":
                recommendations += line + " "
            elif current_section == "learning_suggestions":
                learning_suggestions += line + " "

        # 如果没有找到明确的章节，将所有内容作为详细分析
        if not detailed_analysis and not recommendations and not learning_suggestions:
            if len(content) > 200:
                summary = content[:200] + "..."
                detailed_analysis = content
            else:
                summary = content
                detailed_analysis = content
            recommendations = "基于以上分析，建议重点关注薄弱知识点，通过针对性练习提升学习效果。"
            learning_suggestions = "建议采用多样化的学习方法，结合理论学习和实践练习。"

        return {
            "summary": summary.strip() or "学习诊断分析完成",
            "detailed_analysis": detailed_analysis.strip() or content,
            "recommendations": recommendations.strip() or "请根据诊断结果制定个性化学习计划",
            "learning_suggestions": learning_suggestions.strip() or "建议采用循序渐进的学习方式",
            "generated_at": datetime.now().isoformat()
        }
    
    def _generate_fallback_report(self, diagnosis_data: Dict[str, Any]) -> Dict[str, str]:
        """生成备用诊断报告(当LLM调用失败时使用)"""
        student_id = diagnosis_data.get("student_id", "未知学生")
        overall_ability = diagnosis_data.get("overall_ability", 0)
        knowledge_diagnosis = diagnosis_data.get("knowledge_diagnosis", {})
        
        # 统计掌握情况
        total_kcs = len(knowledge_diagnosis)
        mastered_count = sum(1 for kc_data in knowledge_diagnosis.values() 
                           if kc_data.get("mastery_probability", 0) > 0.7)
        weak_count = sum(1 for kc_data in knowledge_diagnosis.values() 
                        if kc_data.get("mastery_probability", 0) < 0.5)
        
        # 分析知识点掌握情况
        weak_kcs = []
        strong_kcs = []
        partial_kcs = []

        for kc_name, kc_data in knowledge_diagnosis.items():
            mastery_prob = kc_data.get("mastery_probability", 0)
            if mastery_prob > 0.7:
                strong_kcs.append(kc_name)
            elif mastery_prob > 0.5:
                partial_kcs.append(kc_name)
            else:
                weak_kcs.append(kc_name)

        ability_level = "优秀" if overall_ability > 0.8 else "良好" if overall_ability > 0.6 else "一般" if overall_ability > 0.4 else "需要提升"

        # 生成个性化的详细分析
        detailed_analysis = f"通过ORCDF认知诊断模型分析，学生{student_id}的整体能力得分为{overall_ability:.3f}，属于{ability_level}水平。"

        if strong_kcs:
            detailed_analysis += f"在{', '.join(strong_kcs[:3])}等{len(strong_kcs)}个知识点上表现优秀，显示出良好的学习基础。"

        if partial_kcs:
            detailed_analysis += f"在{', '.join(partial_kcs[:2])}等{len(partial_kcs)}个知识点上有一定掌握，但仍有提升空间。"

        if weak_kcs:
            detailed_analysis += f"在{', '.join(weak_kcs[:3])}等{len(weak_kcs)}个知识点上掌握程度较低，需要重点关注和加强练习。"

        # 生成针对性建议
        recommendations = "基于诊断结果，建议采取以下学习策略：\n"
        if weak_kcs:
            recommendations += f"1. 优先加强薄弱知识点：{', '.join(weak_kcs[:3])}，通过基础概念复习和大量练习来提升。\n"
        if partial_kcs:
            recommendations += f"2. 巩固部分掌握的知识点：{', '.join(partial_kcs[:2])}，通过应用练习来深化理解。\n"
        if strong_kcs:
            recommendations += f"3. 保持优势知识点：{', '.join(strong_kcs[:2])}，可以作为学习其他知识点的基础。"

        # 生成学习方法建议
        learning_suggestions = "建议的学习方法：\n"
        if overall_ability < 0.4:
            learning_suggestions += "• 从基础概念开始，循序渐进地学习\n• 多做简单练习题，建立学习信心\n• 寻求老师或同学的帮助和指导"
        elif overall_ability < 0.7:
            learning_suggestions += "• 保持当前的学习节奏和方法\n• 适当增加练习题的难度\n• 注重知识点之间的联系和整合"
        else:
            learning_suggestions += "• 可以尝试更有挑战性的题目\n• 主动探索知识的深层应用\n• 帮助其他同学，巩固自己的理解"

        learning_suggestions += "\n\n推荐具体方法：\n"
        learning_suggestions += "1. 制定个性化学习计划，优先解决薄弱环节\n"
        learning_suggestions += "2. 采用多样化学习资源，如视频教程、互动练习等\n"
        learning_suggestions += "3. 定期进行自我测试，及时调整学习策略\n"
        learning_suggestions += "4. 寻求教师或同学的帮助，特别是在困难知识点上"

        return {
            "summary": f"学生{student_id}整体能力水平为{ability_level}，在{total_kcs}个知识点中掌握了{mastered_count}个，有{weak_count}个知识点需要重点关注。",
            "detailed_analysis": detailed_analysis,
            "recommendations": recommendations,
            "learning_suggestions": learning_suggestions,
            "generated_at": datetime.now().isoformat()
        }

    async def generate_learning_path_description(self, learning_path_data: Dict[str, Any]) -> str:
        """生成学习路径的自然语言描述"""
        try:
            prompt = self._build_learning_path_prompt(learning_path_data)
            response = await self._call_llm_api(prompt)
            content = response["choices"][0]["message"]["content"]
            return content
        except Exception as e:
            logger.error(f"生成学习路径描述失败: {e}")
            return self._generate_fallback_learning_path_description(learning_path_data)
    
    def _build_learning_path_prompt(self, learning_path_data: Dict[str, Any]) -> str:
        """构建学习路径提示词"""
        student_id = learning_path_data.get("student_id", "未知学生")
        learning_stages = learning_path_data.get("learning_path", [])
        weak_kcs = learning_path_data.get("weak_knowledge_components", [])
        
        stages_info = []
        for i, stage in enumerate(learning_stages[:3]):  # 只取前3个阶段
            kc = stage.get("knowledge_component", "")
            current = stage.get("current_mastery", 0)
            target = stage.get("target_mastery", 0)
            strategy = stage.get("learning_strategy", "")
            stages_info.append(f"阶段{i+1}: {kc} (当前{current:.2f} → 目标{target:.2f}, 策略: {strategy})")
        
        prompt = f"""
请为学生{student_id}生成一段简洁友好的个性化学习路径描述(100-150字)。

学习路径信息：
{chr(10).join(stages_info)}

薄弱知识点：{', '.join([kc.get('name', '') for kc in weak_kcs[:3]])}

要求：
- 语言亲和友好，鼓励学生
- 突出个性化和针对性
- 说明学习的优先级和步骤
- 给出预期的学习效果

请直接返回描述文本，不要包含其他格式。
"""
        return prompt
    
    def _generate_fallback_learning_path_description(self, learning_path_data: Dict[str, Any]) -> str:
        """生成备用学习路径描述"""
        student_id = learning_path_data.get("student_id", "未知学生")
        stages_count = len(learning_path_data.get("learning_path", []))
        weak_kcs = learning_path_data.get("weak_knowledge_components", [])
        
        weak_kc_names = [kc.get("name", "") for kc in weak_kcs[:3]]
        
        return f"为学生{student_id}制定了包含{stages_count}个学习阶段的个性化学习路径。重点关注{', '.join(weak_kc_names)}等薄弱知识点，通过循序渐进的方式帮助学生逐步提升。每个阶段都有明确的学习目标和具体的学习活动，预计能够有效提升学生的整体能力水平。"
