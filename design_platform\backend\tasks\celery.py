"""
Celery配置和应用实例
"""
from celery import Celery
from app.core.config import settings

# 创建Celery应用实例
celery_app = Celery(
    "orcdf_platform",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "tasks.training_tasks",
        "tasks.data_tasks",
        "tasks.analysis_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30分钟超时
    task_soft_time_limit=25 * 60,  # 25分钟软超时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# 定时任务配置
celery_app.conf.beat_schedule = {
    'cleanup-old-experiments': {
        'task': 'tasks.data_tasks.cleanup_old_experiments',
        'schedule': 3600.0,  # 每小时执行一次
    },
}

if __name__ == "__main__":
    celery_app.start()
