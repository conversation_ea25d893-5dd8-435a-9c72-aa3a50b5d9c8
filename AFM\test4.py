import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import roc_auc_score
from sklearn.decomposition import PCA
import pickle
from scipy.sparse import vstack
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
import config
import os
from sklearn.preprocessing import StandardScaler
import logging  # 添加日志模块

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training.log'),  # 将日志保存到文件
        logging.StreamHandler()  # 同时输出到控制台
    ]
)

# 获取 AUC（增强版，处理异常情况）
def get_auc(loader, model, device):
    pred, target = [], []
    model.eval()
    with torch.no_grad():
        for x, y in loader:
            x, y = x.to(device).float(), y.to(device).float()
            with autocast():
                y_hat = model(x)
                # 将logits转换为概率
                y_hat = torch.sigmoid(y_hat)
            pred += list(y_hat.cpu().numpy())
            target += list(y.cpu().numpy())
    
    # 检查预测值和标签
    pred = np.array(pred)
    target = np.array(target)
    
    # 检查是否有 nan 或 inf
    if np.isnan(pred).any() or np.isinf(pred).any():
        print("Warning: NaN or Inf values found in predictions")
        return 0.0
    
    # 检查标签是否全为 0 或全为 1
    unique_labels = np.unique(target)
    if len(unique_labels) < 2:
        print(f"Warning: Only one class in labels: {unique_labels}")
        return 0.0
    
    try:
        auc = roc_auc_score(target, pred)
        return auc
    except Exception as e:
        print(f"Warning: Error calculating AUC: {e}")
        return 0.0


# 基础 FM 层
class FM(nn.Module):
    def __init__(self):
        super(FM, self).__init__()

    def forward(self, inputs):
        fm_input = inputs
        square_of_sum = torch.pow(torch.sum(fm_input, dim=1, keepdim=True), 2)
        sum_of_square = torch.sum(fm_input * fm_input, dim=1, keepdim=True)
        cross_term = square_of_sum - sum_of_square
        cross_term = 0.5 * torch.sum(cross_term, dim=2, keepdim=False)
        return cross_term


# 优化后的 AFM Layer
class AFMLayer(nn.Module):
    def __init__(self, embedding_size, attention_factor=4, l2_reg=0.0, drop_rate=0.0):
        super(AFMLayer, self).__init__()
        self.embedding_size = embedding_size
        self.attention_factor = attention_factor
        self.l2_reg = l2_reg
        self.drop_rate = drop_rate

        self.attention_W = nn.Parameter(torch.Tensor(embedding_size, attention_factor))
        self.attention_b = nn.Parameter(torch.Tensor(attention_factor))
        self.projection_h = nn.Parameter(torch.Tensor(attention_factor, 1))
        self.projection_p = nn.Parameter(torch.Tensor(embedding_size, 1))

        nn.init.xavier_normal_(self.attention_W)
        nn.init.xavier_normal_(self.projection_h)
        nn.init.xavier_normal_(self.projection_p)
        nn.init.zeros_(self.attention_b)

        self.drop = nn.Dropout(drop_rate)
        self.relu = nn.ReLU()
        self.softmax = nn.Softmax(dim=1)

    def forward(self, inputs):
        # 优化后的交互计算
        x = torch.stack(inputs, dim=1)  # [B, F, D]
        
        # 计算所有特征对交互（内存高效）
        p = x.unsqueeze(2)  # [B, F, 1, D]
        q = x.unsqueeze(1)  # [B, 1, F, D]
        inner_product = p * q  # [B, F, F, D]
        
        # 只取上三角部分（避免重复）
        rows, cols = torch.triu_indices(x.size(1), x.size(1), offset=1)
        bi_interaction = inner_product[:, rows, cols]  # [B, C(F,2), D]

        # 👇 确保 bi_interaction 是 3D，如果不是，手动 squeeze 掉多余的维度
        if bi_interaction.dim() == 4:
            bi_interaction = bi_interaction.squeeze(2)  # [B, C(F,2), D]

        # 注意力计算
        attention_temp = self.relu(
            torch.einsum('bnd,df->bnf', bi_interaction, self.attention_W) + self.attention_b
        )
        normalized_att_score = self.softmax(
            torch.einsum('bnf,fg->bng', attention_temp, self.projection_h)
        )
        attention_output = torch.sum(normalized_att_score * bi_interaction, dim=1)
        attention_output = self.drop(attention_output)
        afm_out = torch.einsum('bd,dg->bg', attention_output, self.projection_p)

        return afm_out


# AFM 模型主类（增强稳定性）
class AFM(nn.Module):
    def __init__(self, input_dim, use_attention=True, attention_factor=8, l2_reg=0.00001, drop_rate=0.5):
        super(AFM, self).__init__()
        self.use_attention = use_attention

        if self.use_attention:
            self.fm = AFMLayer(input_dim, attention_factor, l2_reg, drop_rate)
        else:
            self.fm = FM()

        self.out = nn.Sigmoid()
        self.dropout = nn.Dropout(drop_rate)

    def forward(self, X):
        if self.use_attention:
            x_list = torch.split(X, 1, dim=1)
            logit = self.fm(x_list)
        else:
            logit = self.fm(X)

        # 直接返回logits，不使用sigmoid（因为使用BCEWithLogitsLoss）
        return logit


# 数据加载和预处理
def load_and_preprocess_data(file_paths):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    def load_pkl(relative_path):
        path = os.path.join(current_dir, relative_path)
        if not os.path.exists(path):
            raise FileNotFoundError(f"File not found: {path}")
        print(f"Loading file: {path}")
        with open(path, 'rb') as f:
            data = pickle.load(f)

        # 如果是稀疏矩阵，直接返回
        if 'scipy.sparse' in str(type(data)):
            return data
        # 如果是 list，转为 numpy.ndarray
        elif isinstance(data, list):
            return np.array(data)
        # 如果是 numpy.ndarray，直接返回
        elif isinstance(data, np.ndarray):
            return data
        else:
            raise TypeError(f"Unsupported data type: {type(data)}")
        
    print("Loading data...")
    trn_mat = load_pkl(file_paths['trn_mat'])
    val_mat = load_pkl(file_paths['val_mat'])
    tst_mat = load_pkl(file_paths['tst_mat'])
    usr_emb = load_pkl(file_paths['usr_emb_np'])
    itm_emb = load_pkl(file_paths['itm_emb_np'])

    # 合并交互矩阵（保持稀疏格式）
    full_interaction = vstack([trn_mat, val_mat, tst_mat]).tocsr()

    # ✅ 仅提取最后一列标签（避免转为稠密）
    labels = np.array((full_interaction.getcol(-1) != 0).toarray(), dtype=np.float32).flatten()

    # 特征工程：组合用户和物品嵌入
    print("Original embedding dimensions:")
    print(f"User: {usr_emb.shape}, Item: {itm_emb.shape}")

    # PCA降维
    def reduce_dim(data, n_components=32):
        if data.shape[1] <= n_components:
            return data
        pca = PCA(n_components=n_components)
        return pca.fit_transform(data)
    
    usr_emb = usr_emb.reshape(usr_emb.shape[0], -1)  # (N, 102)
    itm_emb = itm_emb.reshape(itm_emb.shape[0], -1)  # (N, 102)

    # 修改特征构造部分
    usr_emb = usr_emb.reshape(usr_emb.shape[0], -1)  # (N, 102)
    itm_emb = itm_emb.reshape(itm_emb.shape[0], -1)  # (N, 102)

    # 拆分为两个字段
    features = np.stack([usr_emb, itm_emb], axis=1)  # (N, 2, 102)

    # 数据归一化
    features = features.reshape(-1, 102)
    features = StandardScaler().fit_transform(features)
    features = features.reshape(-1, 2, 102)
    
    print(f"Reduced feature dimension: {features.shape[1]}")
    
    return features, labels, usr_emb, itm_emb


# 推荐函数
def recommend_knowledge_paths(model, user_emb, all_knowledge_embs, device, top_k=10):
    """
    给定用户嵌入和所有知识点嵌入，输出推荐顺序
    :param model: 训练好的 AFM 模型
    :param user_emb: 用户嵌入 (102,)
    :param all_knowledge_embs: 所有知识点嵌入 (K, 102)
    :param device: 使用的设备 (cpu/cuda)
    :param top_k: 返回前 K 个知识点
    :return: 排序后的知识点索引
    """
    model.eval()
    scores = []

    with torch.no_grad():
        for idx, knowledge_emb in enumerate(all_knowledge_embs):
            # 按照训练时的格式构造输入：(1, 2, 102)
            x = np.stack([user_emb, knowledge_emb], axis=0)  # (2, 102)
            x = torch.FloatTensor(x).unsqueeze(0).to(device)  # (1, 2, 102)
            logits = model(x)
            y_pred = torch.sigmoid(logits).cpu().numpy()[0, 0]  # 将logits转换为概率
            scores.append((idx, y_pred))

    # 按得分排序
    scores.sort(key=lambda x: x[1], reverse=True)
    ranked_indices = [idx for idx, _ in scores[:top_k]]
    return ranked_indices, scores  # 返回原始分数以便记录

def recommend_for_learning(model, user_emb, all_knowledge_embs, device, top_k=10):
    """
    基于学习价值的知识点推荐（改进版）
    推荐最适合学习的知识点，而不是最容易答对的
    """
    model.eval()
    learning_scores = []

    with torch.no_grad():
        for idx, knowledge_emb in enumerate(all_knowledge_embs):
            # 按照训练时的格式构造输入：(1, 2, 102)
            x = np.stack([user_emb, knowledge_emb], axis=0)  # (2, 102)
            x = torch.FloatTensor(x).unsqueeze(0).to(device)  # (1, 2, 102)
            logits = model(x)
            answer_prob = torch.sigmoid(logits).cpu().numpy()[0, 0]  # 答题正确概率

            # 基于ZPD理论计算学习价值
            if answer_prob < 0.2:
                # 太难，学习困难
                learning_value = 0.2 + answer_prob * 0.5  # 0.2-0.3
            elif answer_prob > 0.8:
                # 太简单，已掌握
                learning_value = 0.4 - (answer_prob - 0.8) * 2  # 0.4-0.0
            else:
                # 适中难度，学习价值高
                # 在0.5附近达到峰值
                learning_value = 1.0 - 1.5 * abs(answer_prob - 0.5)

            learning_scores.append((idx, learning_value, answer_prob))

    # 按学习价值排序
    learning_scores.sort(key=lambda x: x[1], reverse=True)
    ranked_indices = [idx for idx, _, _ in learning_scores[:top_k]]

    return ranked_indices, learning_scores


if __name__ == '__main__':
    # 配置
    config = {
        'file_paths': {
            'trn_mat': 'trn_mat.pkl',
            'val_mat': 'val_mat.pkl',
            'tst_mat': 'tst_mat.pkl',
            'usr_emb_np': 'usr_emb_np.pkl',
            'itm_emb_np': 'itm_emb_np.pkl'
        },
        'batch_size': 128,
        'lr': 1e-3,
        'epochs': 50,
        'attention_factor': 8,
        'drop_rate': 0.2,
        'use_attention': True,
        'accumulation_steps': 4
    }

    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 加载和预处理数据
    features, labels, usr_emb, itm_emb = load_and_preprocess_data(config['file_paths'])
    
    labels = labels[:len(features)]
    
    # 检查数据
    print(f"Features shape: {features.shape}")
    print(f"Labels shape: {labels.shape}")
    print(f"Label distribution: {np.unique(labels, return_counts=True)}")
    
    # 创建数据集
    dataset = TensorDataset(
        torch.FloatTensor(features),
        torch.FloatTensor(labels).unsqueeze(1)
    )
    
    # 划分训练/验证/测试集 (按原始比例)
    train_size = int(0.7 * len(dataset))
    val_size = int(0.15 * len(dataset))
    test_size = len(dataset) - train_size - val_size
    train_set, val_set, test_set = torch.utils.data.random_split(
        dataset, [train_size, val_size, test_size]
    )

    # 数据加载器
    train_loader = DataLoader(train_set, batch_size=config['batch_size'], shuffle=True)
    val_loader = DataLoader(val_set, batch_size=config['batch_size'])
    test_loader = DataLoader(test_set, batch_size=config['batch_size'])

    # 初始化模型
    model = AFM(
        input_dim=102,  # 每个字段的维度
        use_attention=config['use_attention'],
        attention_factor=config['attention_factor'],
        drop_rate=config['drop_rate']
    ).to(device)
    
    # 损失函数和优化器 - 使用BCEWithLogitsLoss以支持autocast
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=config['lr'])
    scaler = GradScaler()

    # 训练循环
    print("Starting training...")
    logging.info("Starting training...")  # 添加日志记录
    
    # 用于存储训练历史的列表
    train_history = {
        'loss': [],
        'val_auc': [],
        'test_auc': []
    }
    
    for epoch in range(config['epochs']):
        model.train()
        optimizer.zero_grad()
        
        total_loss = 0
        for i, (x, y) in enumerate(train_loader):
            x, y = x.to(device), y.to(device)
            
            with autocast():
                y_pred = model(x)
                loss = criterion(y_pred, y) / config['accumulation_steps']
            
            scaler.scale(loss).backward()
            
            if (i + 1) % config['accumulation_steps'] == 0:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()
            
            total_loss += loss.item() * config['accumulation_steps']
        
        # 验证
        val_auc = get_auc(val_loader, model, device)
        
        # 记录训练历史
        avg_loss = total_loss/len(train_loader)
        train_history['loss'].append(avg_loss)
        train_history['val_auc'].append(val_auc)
        
        # 日志记录
        log_message = f'Epoch {epoch+1}/{config["epochs"]} | Loss: {avg_loss:.4f} | Val AUC: {val_auc:.4f}'
        print(log_message)
        logging.info(log_message)  # 添加日志记录
    
    # 最终测试
    test_auc = get_auc(test_loader, model, device)
    train_history['test_auc'].append(test_auc)
    
    final_message = f'Final Test AUC: {test_auc:.4f}'
    print(final_message)
    logging.info(final_message)  # 添加日志记录
    
    # 保存模型
    torch.save(model, 'afm_model.pth')
    logging.info("Model saved to afm_model.pth")
    print("Model saved to afm_model.pth")
    
    # 将训练历史保存到日志文件
    logging.info("Training History:")
    for i in range(len(train_history['loss'])):
        logging.info(f"Epoch {i+1} - Loss: {train_history['loss'][i]:.4f}, Val AUC: {train_history['val_auc'][i]:.4f}")
    logging.info(f"Final Test AUC: {test_auc:.4f}")
    
    # 示例推荐
    print("\nGenerating recommendation paths...")
    logging.info("Generating recommendation paths...")
    
    # 数据预处理（与训练时一致）
    usr_emb_flat = usr_emb.reshape(usr_emb.shape[0], -1)  # (N, 102)
    itm_emb_flat = itm_emb.reshape(itm_emb.shape[0], -1)  # (M, 102)
    
    # 数据归一化（使用训练时的标准化参数）
    all_features = np.vstack([usr_emb_flat, itm_emb_flat])
    all_features = all_features.reshape(-1, 102)
    scaler_obj = StandardScaler()
    all_features_scaled = scaler_obj.fit_transform(all_features)
    
    user_features = all_features_scaled[:len(usr_emb_flat)]
    item_features = all_features_scaled[len(usr_emb_flat):]
    
    # 选择一个用户进行推荐
    user_idx = 2
    user_emb_vector = user_features[user_idx]
    
    # 加载模型进行推荐
    model.eval()
    recommended_indices, scores = recommend_knowledge_paths(model, user_emb_vector, item_features, device, top_k=10)
    
    # 获取原始知识点ID（itm_emb中的索引）
    # 由于itm_emb_flat是从itm_emb.reshape得到的，所以索引是连续的
    original_knowledge_ids = recommended_indices  # 这些就是原始知识点的ID
    
    recommendation_msg = f"Recommended knowledge path for user {user_idx}: {original_knowledge_ids}"
    print(recommendation_msg)
    logging.info(recommendation_msg)
    
    # 输出详细的推荐结果（包括分数）
    detailed_recommendation_msg = f"Top 10 recommendations with scores for user {user_idx}:"
    for i, (idx, score) in enumerate(scores[:10]):
        detailed_recommendation_msg += f"\n  Rank {i+1}: Knowledge ID {idx} (Score: {score:.4f})"
    
    print(detailed_recommendation_msg)
    logging.info(detailed_recommendation_msg)
    
    # 如果有知识点名称映射，可以输出名称
    # 示例知识点名称映射（替换为你自己的）
    knowledge_names = [f"Knowledge_{i}" for i in range(len(item_features))]
    recommended_names = [knowledge_names[i] for i in original_knowledge_ids]
    
    recommendation_names_msg = f"Recommended knowledge names for user {user_idx}: {recommended_names}"
    print(recommendation_names_msg)
    logging.info(recommendation_names_msg)

    # 保存模型 - 同时保存完整模型和state_dict
    print("\n保存模型...")
    torch.save(model, 'afm_model.pth')
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': {
            'feature_dims': [102, 102],
            'embed_dim': 64,
            'att_size': 32,
            'drop_rate': 0.1,
            'use_attention': True
        }
    }, 'afm_model_state.pth')
    print(f"✓ 模型已保存到 afm_model.pth 和 afm_model_state.pth")