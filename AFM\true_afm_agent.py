#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实训练的AFM模型的智能体实现
"""

import numpy as np
import torch
import torch.nn as nn
import pickle
import os
import sys
from typing import List, Dict, Tuple
import logging

# 添加当前目录到路径，以便导入AFM模型
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class TrueAFMAgent:
    """使用真实训练AFM模型的智能体"""
    
    def __init__(self, model_path: str = 'afm_model.pth', user_id: int = 0):
        """
        初始化真实AFM智能体
        
        Args:
            model_path: 训练好的AFM模型路径
            user_id: 用户ID，用于选择用户嵌入
        """
        self.model_path = model_path
        self.user_id = user_id
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型和嵌入
        self.model = None
        self.user_embeddings = None
        self.item_embeddings = None
        self.user_embedding = None
        
        self._load_model_and_embeddings()
        
        logging.info(f"真实AFM智能体初始化完成")
        logging.info(f"用户ID: {user_id}")
        logging.info(f"用户嵌入维度: {self.user_embedding.shape}")
        logging.info(f"知识点嵌入数量: {len(self.item_embeddings)}")
    
    def _load_model_and_embeddings(self):
        """加载模型和嵌入数据"""
        try:
            # 加载嵌入数据
            with open('usr_emb_np.pkl', 'rb') as f:
                self.user_embeddings = np.array(pickle.load(f))
            with open('itm_emb_np.pkl', 'rb') as f:
                self.item_embeddings = np.array(pickle.load(f))
            
            # 处理嵌入维度
            if len(self.user_embeddings.shape) == 3:
                self.user_embeddings = self.user_embeddings.reshape(self.user_embeddings.shape[0], -1)
            if len(self.item_embeddings.shape) == 3:
                self.item_embeddings = self.item_embeddings.reshape(self.item_embeddings.shape[0], -1)
            
            # 选择特定用户的嵌入
            if self.user_id >= len(self.user_embeddings):
                self.user_id = 0
                logging.warning(f"用户ID超出范围，使用用户0")
            
            self.user_embedding = self.user_embeddings[self.user_id]
            
            # 加载训练好的模型
            if os.path.exists(self.model_path):
                # 使用自定义的加载方式，避免类定义问题
                checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)

                # 如果checkpoint是完整的模型对象，直接使用
                if hasattr(checkpoint, 'eval'):
                    self.model = checkpoint
                else:
                    # 如果是state_dict，需要重新创建模型
                    # 这里需要从test4.py导入AFM类
                    try:
                        from test4 import AFM
                        # 创建模型实例（需要知道具体参数）
                        self.model = AFM(
                            feature_dims=[102, 102],  # 用户和物品嵌入维度
                            embed_dim=64,
                            att_size=32,
                            drop_rate=0.1,
                            use_attention=True
                        )
                        if isinstance(checkpoint, dict) and 'state_dict' in checkpoint:
                            self.model.load_state_dict(checkpoint['state_dict'])
                        else:
                            self.model.load_state_dict(checkpoint)
                    except ImportError:
                        # 如果无法导入，直接使用checkpoint
                        self.model = checkpoint

                self.model.to(self.device)
                self.model.eval()
                logging.info(f"成功加载训练好的AFM模型: {self.model_path}")
            else:
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
                
        except Exception as e:
            logging.error(f"加载模型和嵌入失败: {e}")
            raise
    
    def reset(self):
        """重置智能体状态"""
        pass
    
    def predict_learning_effect(self, knowledge_point_id: int) -> float:
        """
        使用真实AFM模型预测学习效果
        
        Args:
            knowledge_point_id: 知识点ID
            
        Returns:
            预测的学习效果分数 [0, 1]
        """
        if self.model is None:
            raise RuntimeError("AFM模型未加载")
        
        # 确保知识点ID在有效范围内
        if knowledge_point_id >= len(self.item_embeddings):
            knowledge_point_id = knowledge_point_id % len(self.item_embeddings)
        
        try:
            # 准备输入数据 - 按照训练时的格式 (1, 2, 102)
            user_emb = self.user_embedding  # [102]
            item_emb = self.item_embeddings[knowledge_point_id]  # [102]

            # 按照训练时的格式构造输入：(1, 2, 102)
            combined_features = np.stack([user_emb, item_emb], axis=0)  # [2, 102]

            # 转换为PyTorch张量
            input_tensor = torch.FloatTensor(combined_features).unsqueeze(0).to(self.device)  # [1, 2, 102]

            # 模型预测
            with torch.no_grad():
                logits = self.model(input_tensor)
                # 将logits转换为概率
                prediction = torch.sigmoid(logits).cpu().numpy()[0, 0]

            return float(prediction)
            
        except Exception as e:
            logging.warning(f"AFM预测失败: {e}, 使用默认值")
            return 0.5  # 返回默认值
    
    def get_action(self, observation: np.ndarray) -> int:
        """
        基于真实AFM模型预测选择最佳学习动作
        
        Args:
            observation: 当前学习状态 [掌握程度1, 掌握程度2, ...]
            
        Returns:
            选择的动作（知识点ID）
        """
        action_scores = []
        afm_predictions = []
        
        for action in range(len(observation)):
            # 使用真实AFM模型预测学习效果
            afm_prediction = self.predict_learning_effect(action)
            afm_predictions.append(afm_prediction)
            
            # 计算当前掌握需求
            mastery_need = 1.0 - observation[action]
            
            # 综合得分：AFM预测 + 掌握需求
            # 这里可以调整权重比例
            combined_score = afm_prediction * 0.7 + mastery_need * 0.3
            action_scores.append(combined_score)
        
        # 选择得分最高的动作
        best_action = np.argmax(action_scores)
        
        # 记录决策过程（可选）
        logging.debug(f"AFM预测: {[f'{p:.3f}' for p in afm_predictions]}")
        logging.debug(f"掌握需求: {[f'{1-obs:.3f}' for obs in observation]}")
        logging.debug(f"综合得分: {[f'{s:.3f}' for s in action_scores]}")
        logging.debug(f"选择动作: {best_action}")
        
        return best_action

class EnhancedTrueAFMAgent(TrueAFMAgent):
    """增强版真实AFM智能体，考虑更多因素"""
    
    def __init__(self, model_path: str = 'afm_model.pth', user_id: int = 0, 
                 dependency_matrix: np.ndarray = None):
        super().__init__(model_path, user_id)
        self.dependency_matrix = dependency_matrix
        self.learning_history = []  # 记录学习历史
    
    def reset(self):
        """重置智能体状态"""
        self.learning_history = []
    
    def get_action(self, observation: np.ndarray) -> int:
        """
        增强版决策：考虑AFM预测、掌握需求、依赖关系和学习历史
        """
        action_scores = []
        
        for action in range(len(observation)):
            # 1. AFM预测学习效果
            afm_prediction = self.predict_learning_effect(action)
            
            # 2. 当前掌握需求
            mastery_need = 1.0 - observation[action]
            
            # 3. 依赖关系考虑
            dependency_bonus = 0
            if self.dependency_matrix is not None:
                for prereq in range(len(observation)):
                    if self.dependency_matrix[action, prereq] > 0:
                        prereq_mastery = observation[prereq]
                        dependency_weight = self.dependency_matrix[action, prereq]
                        
                        if prereq_mastery > 0.5:  # 前置条件满足
                            dependency_bonus += dependency_weight * 0.2
                        else:  # 前置条件不满足
                            dependency_bonus -= dependency_weight * 0.3
            
            # 4. 学习历史考虑（避免重复学习刚学过的内容）
            history_penalty = 0
            if len(self.learning_history) > 0:
                recent_actions = self.learning_history[-3:]  # 最近3步
                if action in recent_actions:
                    history_penalty = -0.1 * recent_actions.count(action)
            
            # 5. 综合得分
            combined_score = (afm_prediction * 0.5 + 
                            mastery_need * 0.3 + 
                            dependency_bonus * 0.15 + 
                            history_penalty * 0.05)
            
            action_scores.append(combined_score)
        
        # 选择最佳动作
        best_action = np.argmax(action_scores)
        
        # 记录到学习历史
        self.learning_history.append(best_action)
        
        return best_action

def create_true_afm_agent(user_id: int = 0, enhanced: bool = False, 
                         dependency_matrix: np.ndarray = None) -> TrueAFMAgent:
    """
    创建真实AFM智能体的工厂函数
    
    Args:
        user_id: 用户ID
        enhanced: 是否使用增强版智能体
        dependency_matrix: 知识点依赖关系矩阵
        
    Returns:
        AFM智能体实例
    """
    model_path = 'afm_model.pth'
    
    if enhanced and dependency_matrix is not None:
        return EnhancedTrueAFMAgent(model_path, user_id, dependency_matrix)
    else:
        return TrueAFMAgent(model_path, user_id)

if __name__ == "__main__":
    # 测试真实AFM智能体
    logging.basicConfig(level=logging.DEBUG)
    
    try:
        agent = create_true_afm_agent(user_id=0)
        
        # 模拟观察状态
        observation = np.array([0.2, 0.3, 0.1, 0.4, 0.15])
        
        # 获取动作
        action = agent.get_action(observation)
        print(f"推荐的学习动作: {action}")
        
        # 测试预测功能
        for i in range(5):
            prediction = agent.predict_learning_effect(i)
            print(f"知识点{i}的学习效果预测: {prediction:.4f}")
            
    except Exception as e:
        logging.error(f"测试失败: {e}")
