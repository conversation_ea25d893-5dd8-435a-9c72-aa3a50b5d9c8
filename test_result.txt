(orcdf)PS D:\Code\Project\ORCDF> python exp_orcdf.py --method=orcdf --if_type=kscd --data_type=NeurIPS2020 --ssl_temp=0.5  --ssl_weight=1e-3  --flip_ratio=0.15  --test_size=0.2 --seed=0 --batch_size=2048 --device=cuda:0 --gcn_layers=3 --epoch=3  --keep_prob=1.0  --lr=4e-3 --weight_decay=0       
{'batch_size': 2048,
 'data_type': 'NeurIPS2020',
 'device': 'cuda:0',
 'dtype': torch.float64,
 'epoch': 3,
 'exp_type': 'cdm',
 'flip_ratio': 0.15,
 'gcn_layers': 3,
 'if_type': 'kscd',
 'keep_prob': 1.0,
 'latent_dim': 32,
 'lr': 0.004,
 'method': 'orcdf-w|o-',
 'mode': '',
 'name': 'orcdf-kscd-w|o--NeurIPS2020-seed0',
 'noise_ratio': None,
 'seed': 0,
 'ssl_temp': 0.5,
 'ssl_weight': 0.001,
 'test_size': 0.2,
 'weight_decay': 0.0,
 'weight_reg': 0.05}
wandb: Tracking run with wandb version 0.21.0                                                                                                       
wandb: W&B syncing is set to `offline` in this directory. Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.
Number of response logs 214828
[Epoch 1]
Training:   0%|                                                                                                             | 0/84 [00:00<?, ?it/s]D:\Code\Project\ORCDF\inscd\extractor\orcdf.py:134: UserWarning: torch.sparse.SparseTensor(indices, values, shape, *, device=) is deprecated.  Please use torch.sparse_coo_tensor(indices, values, shape, dtype=, device=). (Triggered internally at ..\torch\csrc\utils\tensor_new.cpp:623.)
  g = torch.sparse.DoubleTensor(index.t(), values, size)
Training: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████| 84/84 [02:24<00:00,  1.72s/it]
Average loss: 0.6747300719358341
s/it]
{'acc': 63.73,
 'ap': 76.13,
 'auc': 66.12,
 'doa': 31.85,
 'f1': 77.65,
 'mad': 2.59,
 'rmse': 46.6}
[Epoch 2]
Training:   2%|██▍                                                                                                  | 2/84 [00:1Training:   4%|███▌                                                                                                 | 3/84 [00:1Training:   5%|████▊                                                                                                | 4/84 [00:2Training:   6%|██████                                                                                               | 5/84 [00:2Training:   7%|███████▏                                                                                             | 6/84 [00:3Training:   8%|████████▍                                                                                            | 7/84 [00:4Training:  10%|█████████▌                                                                                           | 8/84 [00:4Training:  11%|██████████▊                                                                                          | 9/84 [00:5Training:  12%|███████████▉                                                                                        | 10/84 [00:5Training:  13%|█████████████                                                                                       | 11/84 [01:0Training:  14%|██████████████▎                                                                                     | 12/84 [01:0Training:  15%|███████████████▍                                                                                    | 13/84 [01:1Training:  17%|████████████████▋                                                                                   | 14/84 [01:2Training:  18%|█████████████████▊                                                                                  | 15/84 [01:2Training:  19%|███████████████████                                                                                 | 16/84 [01:3Training:  20%|████████████████████▏                                                                               | 17/84 [01:3Training:  21%|█████████████████████▍                                                                              | 18/84 [01:4Training:  23%|██████████████████████▌                                                                             | 19/84 [01:5Training:  24%|███████████████████████▊                                                                            | 20/84 [01:5Training:  25%|█████████████████████████                                                                           | 21/84 [02:0Training:  26%|██████████████████████████▏                                                                         | 22/84 [02:0Training:  27%|███████████████████████████▍                                                                        | 23/84 [02:1Training:  29%|████████████████████████████▌                                                                       | 24/84 [02:1Training:  30%|█████████████████████████████▊                                                                      | 25/84 [02:2Training:  31%|██████████████████████████████▉                                                                     | 26/84 [02:3Training:  32%|████████████████████████████████▏                                                                   | 27/84 [02:3Training:  33%|█████████████████████████████████▎                                                                  | 28/84 [02:4Training:  35%|██████████████████████████████████▌                                                                 | 29/84 [02:4Training:  36%|███████████████████████████████████▋                                                                | 30/84 [02:5Training:  37%|████████████████████████████████████▉                                                               | 31/84 [03:0Training:  38%|██████████████████████████████████████                                                              | 32/84 [03:0Training:  39%|███████████████████████████████████████▎                                                            | 33/84 [03:1Training:  40%|████████████████████████████████████████▍                                                           | 34/84 [03:1Training:  42%|█████████████████████████████████████████▋                                                          | 35/84 [03:2Training:  43%|██████████████████████████████████████████▊                                                         | 36/84 [03:3Training:  44%|████████████████████████████████████████████                                                        | 37/84 [03:3Training:  45%|█████████████████████████████████████████████▏                                                      | 38/84 [03:4Training:  46%|██████████████████████████████████████████████▍                                                     | 39/84 [03:4Training:  48%|███████████████████████████████████████████████▌                                                    | 40/84 [03:5Training:  49%|████████████████████████████████████████████████▊                                                   | 41/84 [04:0Training:  50%|██████████████████████████████████████████████████                                                  | 42/84 [04:1Training:  51%|███████████████████████████████████████████████████▏                                                | 43/84 [04:2Training:  52%|████████████████████████████████████████████████████▍                                               | 44/84 [04:3Training:  54%|█████████████████████████████████████████████████████▌                                              | 45/84 [04:4Training:  55%|██████████████████████████████████████████████████████▊                                             | 46/84 [04:5Training:  56%|███████████████████████████████████████████████████████▉                                            | 47/84 [05:0Training:  57%|█████████████████████████████████████████████████████████▏                                          | 48/84 [05:1Training:  58%|██████████████████████████████████████████████████████████▎                                         | 49/84 [05:2Training:  60%|███████████████████████████████████████████████████████████▌                                        | 50/84 [05:3Training:  61%|████████████████████████████████████████████████████████████▋                                       | 51/84 [05:3Training:  62%|█████████████████████████████████████████████████████████████▉                                      | 52/84 [05:4Training:  63%|███████████████████████████████████████████████████████████████                                     | 53/84 [05:5Training:  64%|████████████████████████████████████████████████████████████████▎                                   | 54/84 [05:5Training:  65%|█████████████████████████████████████████████████████████████████▍                                  | 55/84 [06:0Training:  67%|██████████████████████████████████████████████████████████████████▋                                 | 56/84 [06:1Training:  68%|███████████████████████████████████████████████████████████████████▊                                | 57/84 [06:1Training:  69%|█████████████████████████████████████████████████████████████████████                               | 58/84 [06:2Training:  70%|██████████████████████████████████████████████████████████████████████▏                             | 59/84 [06:3Training:  71%|███████████████████████████████████████████████████████████████████████▍                            | 60/84 [06:3Training:  73%|████████████████████████████████████████████████████████████████████████▌                           | 61/84 [06:4Training:  74%|█████████████████████████████████████████████████████████████████████████▊                          | 62/84 [06:5Training:  75%|███████████████████████████████████████████████████████████████████████████                         | 63/84 [06:5Training:  76%|████████████████████████████████████████████████████████████████████████████▏                       | 64/84 [07:0Training:  77%|█████████████████████████████████████████████████████████████████████████████▍                      | 65/84 [07:0Training:  79%|██████████████████████████████████████████████████████████████████████████████▌                     | 66/84 [07:1Training:  80%|███████████████████████████████████████████████████████████████████████████████▊                    | 67/84 [07:2Training:  81%|████████████████████████████████████████████████████████████████████████████████▉                   | 68/84 [07:2Training:  82%|██████████████████████████████████████████████████████████████████████████████████▏                 | 69/84 [07:3Training:  83%|███████████████████████████████████████████████████████████████████████████████████▎                | 70/84 [07:4Training:  85%|████████████████████████████████████████████████████████████████████████████████████▌               | 71/84 [07:4Training:  86%|█████████████████████████████████████████████████████████████████████████████████████▋              | 72/84 [07:5Training:  87%|██████████████████████████████████████████████████████████████████████████████████████▉             | 73/84 [08:0Training:  88%|████████████████████████████████████████████████████████████████████████████████████████            | 74/84 [08:1Training:  89%|█████████████████████████████████████████████████████████████████████████████████████████▎          | 75/84 [08:2Training:  90%|██████████████████████████████████████████████████████████████████████████████████████████▍         | 76/84 [08:3Training:  92%|███████████████████████████████████████████████████████████████████████████████████████████▋        | 77/84 [08:4Training:  93%|████████████████████████████████████████████████████████████████████████████████████████████▊       | 78/84 [08:5Training:  94%|██████████████████████████████████████████████████████████████████████████████████████████████      | 79/84 [09:0Training:  95%|███████████████████████████████████████████████████████████████████████████████████████████████▏    | 80/84 [09:1Training:  96%|████████████████████████████████████████████████████████████████████████████████████████████████▍   | 81/84 [09:2Training:  98%|█████████████████████████████████████████████████████████████████████████████████████████████████▌  | 82/84 [09:3Training:  99%|██████████████████████████████████████████████████████████████████████████████████████████████████▊ | 83/84 [09:4Training: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████| 84/84 [09:5Training: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████| 84/84 [09:58<00:00,  7.13s/it]
Average loss: 0.6368136807243769
Evaluating: 100%|██████████████████████████████████████████████████████████████████████████████| 21/21 [01:11<00:00,  3.39s/it]
{'acc': 65.37,
 'ap': 76.09,
 'auc': 66.21,
 'doa': 31.42,
 'f1': 74.81,
 'mad': 3.14,
 'rmse': 46.52}
[Epoch 3]
Training: 100%|████████████████████████████████████████████████████████████████████████████████| 84/84 [11:58<00:00,  8.55s/it]
Average loss: 0.6303217576953267
Evaluating: 100%|██████████████████████████████████████████████████████████████████████████████| 21/21 [01:02<00:00,  2.95s/it]
{'acc': 64.93,
 'ap': 76.1,
 'auc': 66.28,
 'doa': 31.0,
 'f1': 73.24,
 'mad': 3.89,
 'rmse': 46.54}
Student 0 diagnosis result:
tensor([0.6111, 0.6228, 0.6221, 0.6207, 0.6099, 0.6247, 0.6196, 0.6214, 0.6241,
        0.6258, 0.6230, 0.6306, 0.6186, 0.6242, 0.6174, 0.6272, 0.6256, 0.6237,
        0.6318, 0.6242, 0.6330, 0.6312, 0.6204, 0.6121, 0.6259, 0.6157, 0.6169,
        0.6213, 0.6273, 0.6204, 0.6274, 0.6347, 0.6336, 0.6249, 0.6239, 0.6216,
        0.6120, 0.6237, 0.6031, 0.6100, 0.6287, 0.6211, 0.6214, 0.6174, 0.6199,
        0.6269, 0.6142, 0.6240, 0.6282, 0.6160, 0.6225, 0.6206, 0.6205, 0.6165,
        0.6188, 0.6287, 0.6288, 0.6069, 0.6171, 0.6125, 0.6162, 0.6204, 0.6080,
        0.6338, 0.6211, 0.6231, 0.6057, 0.6119, 0.6253, 0.6154, 0.6183, 0.6208,
        0.6256, 0.6152, 0.6246, 0.6177, 0.6259, 0.6285, 0.6219, 0.6110, 0.6203,
        0.6265, 0.6207, 0.6307, 0.6186, 0.6173, 0.6181, 0.6329, 0.6181, 0.6280,
        0.6338, 0.6357, 0.6199, 0.6211, 0.6149, 0.6197, 0.6127, 0.6079, 0.6249,
        0.6253, 0.6241, 0.6170, 0.6247, 0.6223, 0.6194, 0.6153, 0.6183, 0.6130,
        0.6176, 0.6183, 0.6254, 0.6206, 0.6245, 0.6276, 0.6210, 0.6308, 0.6226,
        0.6197, 0.6225, 0.6181, 0.6118, 0.6226, 0.6186, 0.6259, 0.6204, 0.6230,
        0.6238, 0.6135, 0.6206, 0.6331, 0.6327, 0.6269, 0.6283, 0.6293, 0.6284,
        0.6270, 0.6252, 0.6252, 0.6321, 0.6261, 0.6272, 0.6223, 0.6224, 0.6136,
        0.6080, 0.6245, 0.6185, 0.6238, 0.6230, 0.6274, 0.6249, 0.6246, 0.6301,
        0.6236, 0.6192, 0.6284, 0.6244, 0.6265, 0.6276, 0.6252, 0.6205, 0.6183,
        0.6157, 0.6236, 0.6249, 0.6256, 0.6238, 0.6277, 0.6245, 0.6271, 0.6166,
        0.6221, 0.6260, 0.6296, 0.6272, 0.6251, 0.6241, 0.6274, 0.6278, 0.6285,
        0.6270, 0.6240, 0.6281, 0.6238, 0.6251, 0.6248, 0.6240, 0.6230, 0.6240,
        0.6233, 0.6248, 0.6216, 0.6161, 0.6179, 0.6213, 0.6200, 0.6203, 0.6232,
        0.6241, 0.6154, 0.6259, 0.6081, 0.6071, 0.6118, 0.6343, 0.6203, 0.6249,
        0.6285, 0.6263, 0.6218, 0.6206, 0.6216, 0.6189, 0.6223, 0.6093, 0.6174,
        0.6168, 0.6149, 0.6186, 0.6172, 0.6139, 0.6143, 0.6182, 0.6235, 0.6200,
        0.6239, 0.6246, 0.6298, 0.6241, 0.6239, 0.6244, 0.6209, 0.6183, 0.6184,
        0.6235, 0.6284, 0.6219, 0.6245, 0.6188, 0.6171, 0.6174, 0.6177, 0.6164,
        0.6221, 0.6267, 0.6099, 0.6098, 0.6193, 0.6226, 0.6189, 0.6042, 0.6181,
        0.6178, 0.5990, 0.6163, 0.6170, 0.6184, 0.6134, 0.6279, 0.6235, 0.6195,
        0.6142, 0.6098, 0.6165, 0.6274, 0.6183, 0.6250, 0.6086],
       device='cuda:0', dtype=torch.float64, grad_fn=<SelectBackward0>)
wandb:
wandb: You can sync this run to the cloud by running:
wandb: wandb sync D:\Code\Project\ORCDF\wandb\offline-run-20250729_023813-mb9pk0cg
wandb: Find logs at: wandb\offline-run-20250729_023813-mb9pk0cg\logs
wandb: wandb sync D:\Code\Projectpython exp_orcdf.py --method=kscd --data_type=NeurIPS2020 --ssl_temp=0.5  --ssl_weight=1e-3  --flip_ratio=0.15  --test_size=0.2 --seed=0 --batch_size=2048 --device=cuda:0 --gcn_layers=3 --epoch=3  --keep_prob=1.0  --lr=4e-3 --weight_decay=0de\Project\ORCDF>
{'batch_size': 2048,
 'data_type': 'NeurIPS2020',
 'device': 'cuda:0',
 'dtype': torch.float64,
 'epoch': 3,
 'exp_type': 'cdm',
 'flip_ratio': 0.15,
 'gcn_layers': 3,
 'if_type': 'kscd',
 'keep_prob': 1.0,
 'latent_dim': 32,
 'lr': 0.004,
 'method': 'kscd',
 'mode': '',
 'name': 'kscd-NeurIPS2020-seed0',
 'noise_ratio': None,
 'seed': 0,
 'ssl_temp': 0.5,
 'ssl_weight': 0.001,
 'test_size': 0.2,
 'weight_decay': 0.0}
wandb: Tracking run with wandb version 0.21.0
wandb: W&B syncing is set to `offline` in this directory. Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.   
Number of response logs 214828
[Epoch 1]
Training: 100%|█████████████████████████████████████████████████████████████████████████████████| 84/84 [02:16<00:00,  1.62s/it]
Average loss: 0.6242904444486866
Evaluating: 100%|███████████████████████████████████████████████████████████████████████████████| 21/21 [00:38<00:00,  1.84s/it]
{'acc': 70.78,
 'ap': 83.22,
 'auc': 75.29,
 'doa': 70.39,
 'f1': 78.53,
 'mad': 3.3,
 'rmse': 43.75}
[Epoch 2]
Training: 100%|█████████████████████████████████████████████████████████████████████████████████| 84/84 [23:09<00:00, 16.54s/it]
Average loss: 0.5368158643269114
Evaluating: 100%|███████████████████████████████████████████████████████████████████████████████| 21/21 [00:20<00:00,  1.00it/s]
{'acc': 71.19,
 'ap': 84.23,
 'auc': 76.35,
 'doa': 71.01,
 'f1': 78.53,
 'mad': 3.49,
 'rmse': 43.3}
[Epoch 3]
Training: 100%|█████████████████████████████████████████████████████████████████████████████████| 84/84 [02:32<00:00,  1.81s/it]
Average loss: 0.5258266143081158
Evaluating: 100%|███████████████████████████████████████████████████████████████████████████████| 21/21 [00:20<00:00,  1.00it/s]
{'acc': 71.34,
 'ap': 84.39,
 'auc': 76.48,
 'doa': 66.18,
 'f1': 78.03,
 'mad': 3.66,
 'rmse': 43.31}
wandb: 
wandb: You can sync this run to the cloud by running:
wandb: wandb sync D:\Code\Project\ORCDF\wandb\offline-run-20250729_031556-76oqsetd
wandb: Find logs at: wandb\offline-run-20250729_031556-76oqsetd\logs