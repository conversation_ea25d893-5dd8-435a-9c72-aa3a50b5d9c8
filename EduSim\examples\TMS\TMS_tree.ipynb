{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": "{'mode': 'with_measurement_error',\n 'skill_num': 7,\n 'test_item_for_each_skill': 2}"}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4.2 Study II\n", "import gym\n", "from EduSim.Envs.TMS import TMSEnv, TMSAgent, tms_train_eval\n", "\n", "env: TMSEnv = gym.make(\n", "    \"TMS-v1\",\n", "    name=\"tree\",\n", "    parameters={\"test_item_for_each_skill\": 4}  # 2 or 4\n", ")\n", "agent = TMSAgent(env.action_space)\n", "\n", "env"]}, {"cell_type": "code", "execution_count": 2, "outputs": [{"data": {"text/plain": "['d0', 'd1', 'd2', 'd3', 'd4', 'd5', 'd6']"}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["env.action_space"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 3, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Episode|    Total-E   Episode-Reward             Progress           \n", "       100|        100         2.505315    [00:00<00:00, 137.25it/s]   \n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Expected Reward: 2.17\n"]}], "source": ["from longling import set_logging_info\n", "set_logging_info()\n", "tms_train_eval(\n", "    agent,\n", "    env,\n", "    max_steps=6,\n", "    max_episode_num=100,  # 4.2.1\n", "    level=\"summary\",\n", "    board_dir=\"./tms_tree\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}}, "nbformat": 4, "nbformat_minor": 1}