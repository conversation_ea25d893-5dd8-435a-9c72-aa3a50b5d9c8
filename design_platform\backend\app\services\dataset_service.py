"""
数据集管理服务 - 真实数据版本
"""
import os
import json
import pandas as pd
import numpy as np
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
import logging

from app.models.experiment import Dataset
from app.schemas.dataset import DatasetCreate, DatasetStats
from app.core.config import settings
from app.services.data_processor import DatasetRegistry, DataProcessor, DatasetIntegrator
from app.utils.serialization import optimize_training_data_response, safe_serialize

logger = logging.getLogger(__name__)


class DatasetService:
    """数据集服务类 - 真实数据版本"""

    def __init__(self, db: Session):
        self.db = db
        self.registry = DatasetRegistry()
        self.integrator = DatasetIntegrator()
    
    def get_datasets(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        is_active: Optional[bool] = None
    ) -> List[Dataset]:
        """获取数据集列表"""
        query = self.db.query(Dataset)
        
        if is_active is not None:
            query = query.filter(Dataset.is_active == is_active)
        
        return query.offset(skip).limit(limit).all()
    
    def get_dataset(self, dataset_id: int) -> Optional[Dataset]:
        """获取单个数据集"""
        return self.db.query(Dataset).filter(Dataset.id == dataset_id).first()
    
    def get_dataset_by_name(self, name: str) -> Optional[Dataset]:
        """根据名称获取数据集"""
        return self.db.query(Dataset).filter(Dataset.name == name).first()
    
    def create_dataset(self, dataset: DatasetCreate) -> Dataset:
        """创建数据集"""
        # 检查名称是否已存在
        existing = self.get_dataset_by_name(dataset.name)
        if existing:
            raise ValueError(f"数据集 '{dataset.name}' 已存在")
        
        db_dataset = Dataset(**dataset.dict())
        self.db.add(db_dataset)
        self.db.commit()
        self.db.refresh(db_dataset)
        return db_dataset
    
    def update_dataset(self, dataset_id: int, dataset_update: DatasetCreate) -> Optional[Dataset]:
        """更新数据集"""
        db_dataset = self.get_dataset(dataset_id)
        if not db_dataset:
            return None
        
        for field, value in dataset_update.dict(exclude_unset=True).items():
            setattr(db_dataset, field, value)
        
        self.db.commit()
        self.db.refresh(db_dataset)
        return db_dataset
    
    def delete_dataset(self, dataset_id: int) -> bool:
        """删除数据集"""
        db_dataset = self.get_dataset(dataset_id)
        if not db_dataset:
            return False
        
        self.db.delete(db_dataset)
        self.db.commit()
        return True
    
    def sync_datasets_from_filesystem(self) -> int:
        """从文件系统同步数据集 - 使用新的数据处理器"""
        synced_count = 0

        try:
            # 使用新的数据集注册表发现数据集
            discovered_datasets = self.registry.discover_datasets()

            for dataset_name, adapter in discovered_datasets.items():
                try:
                    # 检查数据集是否已存在
                    existing = self.get_dataset_by_name(dataset_name)

                    if existing:
                        # 更新统计信息
                        self._update_dataset_from_adapter(existing, adapter)
                    else:
                        # 创建新数据集
                        dataset_data = DatasetCreate(
                            name=adapter.metadata.name,
                            display_name=adapter.metadata.display_name,
                            description=adapter.metadata.description,
                            is_demo=(dataset_name == "Assist0910")  # Assist0910作为演示数据集
                        )

                        db_dataset = self.create_dataset(dataset_data)
                        self._update_dataset_from_adapter(db_dataset, adapter)

                    synced_count += 1
                    logger.info(f"同步数据集成功: {dataset_name}")

                except Exception as e:
                    logger.error(f"同步数据集 {dataset_name} 失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"数据集同步过程失败: {e}")

        return synced_count

    def _update_dataset_from_adapter(self, dataset: Dataset, adapter):
        """从适配器更新数据集信息"""
        try:
            metadata = adapter.metadata

            # 更新基本信息
            dataset.student_num = metadata.student_num
            dataset.exercise_num = metadata.exercise_num
            dataset.knowledge_num = metadata.knowledge_num
            dataset.response_num = metadata.response_num

            # 设置路径信息
            dataset.data_path = str(adapter.dataset_path)
            dataset.config_path = str(adapter.dataset_path / "config.json")

            # 更新显示名称和描述
            dataset.display_name = metadata.display_name
            dataset.description = metadata.description

            self.db.commit()

        except Exception as e:
            logger.error(f"更新数据集信息失败: {e}")
            self.db.rollback()
    
    # 保持向后兼容性的方法
    def _update_dataset_stats(self, dataset: Dataset, config: Dict[str, Any], dataset_path: str):
        """更新数据集统计信息（向后兼容）"""
        try:
            # 尝试使用新的适配器
            from pathlib import Path
            dataset_dir = Path(dataset_path)
            adapter = self.registry._create_adapter(dataset_dir)

            if adapter:
                self._update_dataset_from_adapter(dataset, adapter)
            else:
                # 回退到旧方法
                info = config.get("info", {})
                dataset.student_num = info.get("student_num", 0)
                dataset.exercise_num = info.get("exercise_num", 0)
                dataset.knowledge_num = info.get("knowledge_num", 0)
                dataset.data_path = dataset_path
                dataset.config_path = os.path.join(dataset_path, "config.json")

                response_file = os.path.join(dataset_path, "response.csv")
                if os.path.exists(response_file):
                    response_df = pd.read_csv(response_file, header=None)
                    dataset.response_num = len(response_df)

                self.db.commit()

        except Exception as e:
            logger.error(f"更新数据集统计信息失败: {e}")
    
    def get_dataset_statistics(self, dataset_id: int) -> Optional[DatasetStats]:
        """获取数据集详细统计信息 - 使用新的数据处理器"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            return None

        try:
            # 使用新的数据处理器
            processor = DataProcessor(dataset_name=dataset.name)
            stats = processor.get_dataset_statistics()

            basic_info = stats.get('basic_info', {})
            response_stats = stats.get('response_stats', {})
            q_matrix_stats = stats.get('q_matrix_stats', {})
            data_quality = stats.get('data_quality', {})

            return DatasetStats(
                student_num=basic_info.get('student_num', 0),
                exercise_num=basic_info.get('exercise_num', 0),
                knowledge_num=basic_info.get('knowledge_num', 0),
                response_num=basic_info.get('response_num', 0),
                avg_responses_per_student=response_stats.get('avg_responses_per_student', 0),
                avg_responses_per_exercise=response_stats.get('avg_responses_per_exercise', 0),
                sparsity=stats.get('sparsity', 0),
                difficulty_distribution=stats.get('difficulty_distribution', {}),
                knowledge_coverage=q_matrix_stats
            )

        except Exception as e:
            logger.error(f"计算数据集统计信息失败: {e}")
            return None

    def get_dataset_for_training(self, dataset_name: str) -> Optional[Dict[str, Any]]:
        """获取用于训练的数据集"""
        try:
            # 获取原始训练数据
            raw_training_data = self.integrator.get_dataset_for_training(dataset_name)

            if raw_training_data:
                # 使用优化的序列化
                optimized_data = optimize_training_data_response(raw_training_data)
                logger.info(f"训练数据优化完成: {dataset_name}")
                return optimized_data
            else:
                return None

        except Exception as e:
            logger.error(f"获取训练数据失败: {e}")
            return None

    def get_dataset_for_visualization(self, dataset_name: str) -> Optional[Dict[str, Any]]:
        """获取用于可视化的数据集"""
        try:
            return self.integrator.get_dataset_for_visualization(dataset_name)
        except Exception as e:
            logger.error(f"获取可视化数据失败: {e}")
            return None

    def get_available_datasets_info(self) -> Dict[str, Any]:
        """获取所有可用数据集信息"""
        try:
            return self.integrator.integrate_all_datasets()
        except Exception as e:
            logger.error(f"获取数据集信息失败: {e}")
            return {
                'available_datasets': [],
                'training_ready': [],
                'visualization_ready': []
            }

    def validate_dataset(self, dataset_name: str) -> Dict[str, Any]:
        """验证数据集"""
        try:
            adapter = self.registry.get_dataset(dataset_name)
            if not adapter:
                return {
                    'valid': False,
                    'error': f'数据集 {dataset_name} 不存在'
                }

            is_valid = adapter.validate_data()

            return {
                'valid': is_valid,
                'metadata': {
                    'name': adapter.metadata.name,
                    'display_name': adapter.metadata.display_name,
                    'type': adapter.metadata.dataset_type.value,
                    'student_num': adapter.metadata.student_num,
                    'exercise_num': adapter.metadata.exercise_num,
                    'knowledge_num': adapter.metadata.knowledge_num,
                    'has_q_matrix': adapter.metadata.has_q_matrix,
                    'has_embeddings': adapter.metadata.has_embeddings
                }
            }

        except Exception as e:
            logger.error(f"验证数据集失败: {e}")
            return {
                'valid': False,
                'error': str(e)
            }
