import React, { useState } from 'react';
import {
  Card, Row, Col, Form, Input, Switch, Button, Select, Slider, 
  Typography, Divider, Space, message, InputNumber, Alert
} from 'antd';
import {
  SettingOutlined,
  DatabaseOutlined,
  ExperimentOutlined,
  SecurityScanOutlined,
  CloudOutlined,
  BellOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

interface SystemConfig {
  // 数据库配置
  database: {
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
  };
  // 模型配置
  model: {
    batchSize: number;
    learningRate: number;
    epochs: number;
    hiddenSize: number;
    dropout: number;
  };
  // 系统配置
  system: {
    maxConcurrentJobs: number;
    autoBackup: boolean;
    logLevel: string;
    sessionTimeout: number;
  };
  // 通知配置
  notification: {
    emailEnabled: boolean;
    emailHost: string;
    emailPort: number;
    emailUser: string;
    emailPassword: string;
  };
}

const SystemSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<SystemConfig>({
    database: {
      host: 'localhost',
      port: 8000,
      username: 'admin',
      password: '',
      database: 'edubrain'
    },
    model: {
      batchSize: 32,
      learningRate: 0.001,
      epochs: 100,
      hiddenSize: 128,
      dropout: 0.2
    },
    system: {
      maxConcurrentJobs: 5,
      autoBackup: true,
      logLevel: 'INFO',
      sessionTimeout: 30
    },
    notification: {
      emailEnabled: false,
      emailHost: 'smtp.gmail.com',
      emailPort: 587,
      emailUser: '',
      emailPassword: ''
    }
  });

  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // 这里应该调用API保存配置
      console.log('保存配置:', values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success('配置保存成功！');
    } catch (error) {
      message.error('配置保存失败，请检查输入！');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info('配置已重置');
  };

  const handleTestConnection = async () => {
    try {
      setLoading(true);
      // 模拟测试连接
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success('数据库连接测试成功！');
    } catch (error) {
      message.error('数据库连接测试失败！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px', background: '#ffffff', minHeight: '100vh' }}>
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <SettingOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
          <div>
            <Title level={2} style={{ margin: 0 }}>系统设置</Title>
            <Text type="secondary">配置系统参数和运行环境</Text>
          </div>
        </div>
      </Card>

      <Form
        form={form}
        layout="vertical"
        initialValues={config}
        onValuesChange={(changedValues, allValues) => setConfig(allValues)}
      >
        <Row gutter={[24, 24]}>
          {/* 数据库配置 */}
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <DatabaseOutlined style={{ color: '#52c41a' }} />
                  数据库配置
                </Space>
              }
              style={{ height: '100%' }}
            >
              <Form.Item
                label="数据库主机"
                name={['database', 'host']}
                rules={[{ required: true, message: '请输入数据库主机地址' }]}
              >
                <Input placeholder="localhost" />
              </Form.Item>
              
              <Form.Item
                label="端口"
                name={['database', 'port']}
                rules={[{ required: true, message: '请输入端口号' }]}
              >
                <InputNumber min={1} max={65535} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                label="用户名"
                name={['database', 'username']}
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="admin" />
              </Form.Item>
              
              <Form.Item
                label="密码"
                name={['database', 'password']}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
              
              <Form.Item
                label="数据库名"
                name={['database', 'database']}
                rules={[{ required: true, message: '请输入数据库名' }]}
              >
                <Input placeholder="edubrain" />
              </Form.Item>
              
              <Button 
                type="dashed" 
                onClick={handleTestConnection}
                loading={loading}
                style={{ width: '100%' }}
              >
                测试连接
              </Button>
            </Card>
          </Col>

          {/* 模型配置 */}
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <ExperimentOutlined style={{ color: '#722ed1' }} />
                  模型配置
                </Space>
              }
              style={{ height: '100%' }}
            >
              <Form.Item
                label="批次大小"
                name={['model', 'batchSize']}
              >
                <Slider
                  min={8}
                  max={128}
                  step={8}
                  marks={{
                    8: '8',
                    32: '32',
                    64: '64',
                    128: '128'
                  }}
                />
              </Form.Item>
              
              <Form.Item
                label="学习率"
                name={['model', 'learningRate']}
              >
                <InputNumber
                  min={0.0001}
                  max={0.1}
                  step={0.0001}
                  style={{ width: '100%' }}
                  formatter={value => `${value}`}
                />
              </Form.Item>
              
              <Form.Item
                label="训练轮数"
                name={['model', 'epochs']}
              >
                <Slider
                  min={10}
                  max={500}
                  step={10}
                  marks={{
                    10: '10',
                    100: '100',
                    300: '300',
                    500: '500'
                  }}
                />
              </Form.Item>
              
              <Form.Item
                label="隐藏层大小"
                name={['model', 'hiddenSize']}
              >
                <Select>
                  <Option value={64}>64</Option>
                  <Option value={128}>128</Option>
                  <Option value={256}>256</Option>
                  <Option value={512}>512</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                label="Dropout率"
                name={['model', 'dropout']}
              >
                <Slider
                  min={0}
                  max={0.8}
                  step={0.1}
                  marks={{
                    0: '0',
                    0.2: '0.2',
                    0.5: '0.5',
                    0.8: '0.8'
                  }}
                />
              </Form.Item>
            </Card>
          </Col>

          {/* 系统配置 */}
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <CloudOutlined style={{ color: '#faad14' }} />
                  系统配置
                </Space>
              }
            >
              <Form.Item
                label="最大并发任务数"
                name={['system', 'maxConcurrentJobs']}
              >
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                label="自动备份"
                name={['system', 'autoBackup']}
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
              
              <Form.Item
                label="日志级别"
                name={['system', 'logLevel']}
              >
                <Select>
                  <Option value="DEBUG">DEBUG</Option>
                  <Option value="INFO">INFO</Option>
                  <Option value="WARNING">WARNING</Option>
                  <Option value="ERROR">ERROR</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                label="会话超时时间（分钟）"
                name={['system', 'sessionTimeout']}
              >
                <InputNumber min={5} max={120} style={{ width: '100%' }} />
              </Form.Item>
            </Card>
          </Col>

          {/* 通知配置 */}
          <Col xs={24} lg={12}>
            <Card 
              title={
                <Space>
                  <BellOutlined style={{ color: '#eb2f96' }} />
                  通知配置
                </Space>
              }
            >
              <Form.Item
                label="启用邮件通知"
                name={['notification', 'emailEnabled']}
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
              
              <Form.Item
                label="SMTP服务器"
                name={['notification', 'emailHost']}
              >
                <Input placeholder="smtp.gmail.com" />
              </Form.Item>
              
              <Form.Item
                label="SMTP端口"
                name={['notification', 'emailPort']}
              >
                <InputNumber min={1} max={65535} style={{ width: '100%' }} />
              </Form.Item>
              
              <Form.Item
                label="邮箱用户名"
                name={['notification', 'emailUser']}
              >
                <Input placeholder="<EMAIL>" />
              </Form.Item>
              
              <Form.Item
                label="邮箱密码"
                name={['notification', 'emailPassword']}
              >
                <Input.Password placeholder="请输入邮箱密码或应用密码" />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        <Divider />

        <Alert
          message="配置说明"
          description="修改配置后需要重启系统才能生效。请确保配置正确后再保存。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={loading}
            size="large"
          >
            保存配置
          </Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={handleReset}
            size="large"
          >
            重置配置
          </Button>
        </Space>
      </Form>
    </div>
  );
};

export default SystemSettings;
