/**
 * API服务封装 - 统一的API调用接口
 */
import axios, { AxiosResponse, AxiosError } from 'axios';

import { detectEnvironment, getRuntimeConfig } from '../utils/envConfig';

// 动态API配置 - 支持内网穿透
export const getApiBaseUrl = () => {
  console.log('🔍 环境检测开始...');

  // 首先检查运行时配置
  const runtimeConfig = getRuntimeConfig();
  if (runtimeConfig.apiUrl) {
    console.log('🌐 API Base URL (运行时配置):', runtimeConfig.apiUrl);
    return runtimeConfig.apiUrl;
  }

  // 使用环境检测工具
  const envConfig = detectEnvironment();
  console.log('� 环境检测结果:', envConfig);

  return envConfig.apiUrl;
};

const API_CONFIG = {
  timeout: 120000, // 增加到120秒，支持LLM API调用
  retries: 3,
  retryDelay: 1000,
};

// 创建axios实例 - 不设置固定的baseURL
const api = axios.create({
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 动态设置baseURL
    if (!config.url?.startsWith('http')) {
      config.baseURL = getApiBaseUrl();
    }

    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    console.log('🚀 API请求:', `${config.baseURL || ''}${config.url || ''}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 增强错误处理和日志记录
api.interceptors.response.use(
  (response) => {
    // 成功响应的日志记录
    console.log(`✅ API Success: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    return response;
  },
  (error: AxiosError) => {
    // 详细的错误日志记录
    if (error.response) {
      console.error(`❌ API Error: ${error.response.status} ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.response.data);
    } else if (error.request) {
      console.error(`🌐 Network Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.request);
    } else {
      console.error(`⚠️ Request Error: ${error.message}`);
    }

    // 处理特定错误状态
    if (error.response?.status === 401) {
      // 处理未授权错误
      console.warn('🔐 Unauthorized access, redirecting to login...');
      // window.location.href = '/login';
    } else if (error.response?.status === 404) {
      console.warn('🔍 Resource not found:', error.config?.url);
    } else if (error.response?.status && error.response.status >= 500) {
      console.error('🚨 Server error:', error.response?.status);
    }

    return Promise.reject(error);
  }
);

// API健康检查和工具函数
export const apiUtils = {
  // 检查API连接状态
  checkHealth: async (): Promise<boolean> => {
    try {
      const baseUrl = getApiBaseUrl().replace('/api/v1', '');
      const response = await axios.get(`${baseUrl}/health`);
      console.log('🏥 API Health Check:', response.data);
      return response.status === 200;
    } catch (error) {
      console.error('💔 API Health Check Failed:', error);
      return false;
    }
  },

  // 获取API基础信息
  getApiInfo: async () => {
    try {
      const baseUrl = getApiBaseUrl().replace('/api/v1', '');
      const response = await axios.get(`${baseUrl}/`);
      return response.data;
    } catch (error) {
      console.error('📋 Failed to get API info:', error);
      return null;
    }
  },

  // 测试所有主要API端点
  testAllEndpoints: async () => {
    const endpoints = [
      { name: 'Datasets', url: '/datasets/' },
      { name: 'Experiments', url: '/experiments/' },
      { name: 'Models', url: '/models/supported' },
    ];

    const results: Record<string, boolean> = {};

    for (const endpoint of endpoints) {
      try {
        await api.get(endpoint.url);
        results[endpoint.name] = true;
        console.log(`✅ ${endpoint.name} API: OK`);
      } catch (error) {
        results[endpoint.name] = false;
        console.error(`❌ ${endpoint.name} API: Failed`);
      }
    }

    return results;
  }
};

// 数据集相关API
export const datasetService = {
  // 获取数据集列表
  getDatasets: (params?: {
    skip?: number;
    limit?: number;
    is_active?: boolean;
  }): Promise<AxiosResponse<any[]>> => {
    return api.get('/datasets/', { params });
  },

  // 获取单个数据集
  getDataset: (id: number): Promise<AxiosResponse<any>> => {
    return api.get(`/datasets/${id}`);
  },

  // 获取数据集统计信息
  getDatasetStats: (id: number): Promise<AxiosResponse<any>> => {
    return api.get(`/datasets/${id}/stats`);
  },

  // 同步数据集
  syncDatasets: (): Promise<AxiosResponse<any>> => {
    return api.post('/datasets/sync');
  },

  // 创建数据集
  createDataset: (data: any): Promise<AxiosResponse<any>> => {
    return api.post('/datasets/', data);
  },

  // 更新数据集
  updateDataset: (id: number, data: any): Promise<AxiosResponse<any>> => {
    return api.put(`/datasets/${id}`, data);
  },

  // 删除数据集
  deleteDataset: (id: number): Promise<AxiosResponse<any>> => {
    return api.delete(`/datasets/${id}`);
  },

  // 预处理数据集
  preprocessDataset: (id: number): Promise<AxiosResponse<any>> => {
    return api.post(`/datasets/${id}/preprocess`);
  },

  // === 新增真实数据API ===

  // 获取所有可用数据集信息
  getAvailableDatasets: (): Promise<AxiosResponse<any>> => {
    return api.get('/datasets/available');
  },

  // 验证数据集
  validateDataset: (datasetName: string): Promise<AxiosResponse<any>> => {
    return api.get(`/datasets/${datasetName}/validate`);
  },

  // 获取用于训练的数据集
  getTrainingData: (datasetName: string): Promise<AxiosResponse<any>> => {
    return api.get(`/datasets/${datasetName}/training-data`);
  },

  // 获取用于可视化的数据集
  getVisualizationData: (datasetName: string): Promise<AxiosResponse<any>> => {
    return api.get(`/datasets/${datasetName}/visualization-data`);
  },

  // 准备训练数据
  prepareTrainingData: (
    datasetName: string,
    params?: {
      test_size?: number;
      validation_size?: number;
      seed?: number;
    }
  ): Promise<AxiosResponse<any>> => {
    return api.post(`/datasets/${datasetName}/prepare-training`, params);
  },
};

// 实验相关API
export const experimentService = {
  // 获取实验列表
  getExperiments: (params?: {
    skip?: number;
    limit?: number;
    status_filter?: string;
  }): Promise<AxiosResponse<any[]>> => {
    return api.get('/experiments/', { params });
  },

  // 获取单个实验
  getExperiment: (id: number): Promise<AxiosResponse<any>> => {
    return api.get(`/experiments/${id}`);
  },

  // 创建实验
  createExperiment: (data: any): Promise<AxiosResponse<any>> => {
    return api.post('/experiments/', data);
  },

  // 更新实验
  updateExperiment: (id: number, data: any): Promise<AxiosResponse<any>> => {
    return api.put(`/experiments/${id}`, data);
  },

  // 删除实验
  deleteExperiment: (id: number): Promise<AxiosResponse<any>> => {
    return api.delete(`/experiments/${id}`);
  },

  // 启动实验
  startExperiment: (id: number, useRealTraining: boolean = true): Promise<AxiosResponse<any>> => {
    return api.post(`/experiments/${id}/start`, { use_real_training: useRealTraining });
  },

  // 启动真实训练
  startRealTraining: (id: number): Promise<AxiosResponse<any>> => {
    return api.post(`/experiments/${id}/start`, { use_real_training: true });
  },

  // 启动模拟训练
  startSimulationTraining: (id: number): Promise<AxiosResponse<any>> => {
    return api.post(`/experiments/${id}/start`, { use_real_training: false });
  },

  // 停止实验
  stopExperiment: (id: number): Promise<AxiosResponse<any>> => {
    return api.post(`/experiments/${id}/stop`);
  },

  // 获取实验进度
  getExperimentProgress: (id: number): Promise<AxiosResponse<any>> => {
    return api.get(`/experiments/${id}/progress`);
  },

  // 获取实验日志
  getExperimentLogs: (id: number, lines?: number): Promise<AxiosResponse<any>> => {
    return api.get(`/experiments/${id}/logs`, { params: { lines } });
  },
};

// 模型相关API
export const modelService = {
  // 获取支持的模型列表
  getSupportedModels: (): Promise<AxiosResponse<any[]>> => {
    return api.get('/models/supported');
  },

  // 获取模型信息
  getModelInfo: (modelType: string): Promise<AxiosResponse<any>> => {
    return api.get(`/models/${modelType}/info`);
  },

  // 获取模型默认配置
  getModelDefaultConfig: (modelType: string): Promise<AxiosResponse<any>> => {
    return api.get(`/models/${modelType}/default-config`);
  },
};

// 可视化相关API
export const visualizationService = {
  // 获取实验结果图表数据
  getExperimentChartData: (id: number, chartType: string): Promise<AxiosResponse<any>> => {
    return api.get(`/visualization/experiment/${id}/${chartType}`);
  },

  // 获取数据集可视化数据
  getDatasetVisualization: (id: number, vizType: string): Promise<AxiosResponse<any>> => {
    return api.get(`/visualization/dataset/${id}/${vizType}`);
  },

  // 获取模型对比数据
  getModelComparison: (experimentIds: number[]): Promise<AxiosResponse<any>> => {
    return api.post('/visualization/model-comparison', { experiment_ids: experimentIds });
  },

  // 获取过度平滑分析数据
  getOversmoothingAnalysis: (id: number): Promise<AxiosResponse<any>> => {
    return api.get(`/visualization/experiment/${id}/oversmoothing`);
  },
};

// 系统相关API
export const systemService = {
  // 获取系统状态
  getSystemStatus: (): Promise<AxiosResponse<any>> => {
    return api.get('/system/status');
  },

  // 获取系统统计信息
  getSystemStats: (): Promise<AxiosResponse<any>> => {
    return api.get('/system/stats');
  },

  // 健康检查
  healthCheck: (): Promise<AxiosResponse<any>> => {
    return api.get('/health');
  },
};

// 认知诊断相关API
export const cognitiveService = {
  // 基础诊断
  diagnoseStudent: (data: {
    student_id: string;
    experiment_id: number;
  }): Promise<AxiosResponse<any>> => {
    return api.post('/cognitive-diagnosis/diagnose', data);
  },

  // 带LLM报告的诊断（使用更长的超时时间）
  diagnoseStudentWithReport: (data: {
    student_id: string;
    experiment_id: number;
  }): Promise<AxiosResponse<any>> => {
    return api.post('/cognitive-diagnosis/diagnose-with-report', data, {
      timeout: 300000, // 5分钟超时，确保LLM有足够时间生成报告
    });
  },

  // 获取诊断报告列表
  getDiagnosisReports: (params?: {
    student_id?: string;
    experiment_id?: number;
    limit?: number;
    offset?: number;
  }): Promise<AxiosResponse<any>> => {
    return api.get('/cognitive-diagnosis/reports', { params });
  },

  // 获取单个诊断报告详情
  getDiagnosisReport: (reportId: number): Promise<AxiosResponse<any>> => {
    return api.get(`/cognitive-diagnosis/reports/${reportId}`);
  },

  // 删除诊断报告
  deleteDiagnosisReport: (reportId: number): Promise<AxiosResponse<any>> => {
    return api.delete(`/cognitive-diagnosis/reports/${reportId}`);
  },

  // 批量诊断
  batchDiagnose: (data: {
    experiment_id: number;
    student_ids: string[];
  }): Promise<AxiosResponse<any>> => {
    return api.post('/cognitive-diagnosis/batch-diagnosis', data);
  },

  // 批量诊断带LLM报告
  batchDiagnoseWithReports: (data: {
    experiment_id: number;
    student_ids: string[];
  }): Promise<AxiosResponse<any>> => {
    return api.post('/cognitive-diagnosis/batch-diagnosis-with-reports', data);
  },

  // 生成学习路径
  generateLearningPath: (data: {
    student_id: string;
    experiment_id: number;
    preferences?: any;
  }): Promise<AxiosResponse<any>> => {
    return api.post('/cognitive-diagnosis/learning-path', data);
  },

  // 获取知识点列表
  getKnowledgeComponents: (): Promise<AxiosResponse<any>> => {
    return api.get('/cognitive-diagnosis/knowledge-components');
  },

  // 获取诊断记录列表
  getDiagnosisRecords: (params?: {
    experiment_id?: number;
    student_id?: string;
    diagnosis_type?: string;
    skip?: number;
    limit?: number;
  }): Promise<AxiosResponse<any>> => {
    return api.get('/cognitive-diagnosis/diagnosis-records', { params });
  },

  // 获取单个诊断记录详情
  getDiagnosisRecord: (recordId: number): Promise<AxiosResponse<any>> => {
    return api.get(`/cognitive-diagnosis/diagnosis-records/${recordId}`);
  },
};

// 认证相关API
export const authService = {
  // 用户登录
  login: (data: {
    username: string;
    password: string;
  }): Promise<AxiosResponse<any>> => {
    return api.post('/login', data);
  },

  // 用户登出
  logout: (): Promise<AxiosResponse<any>> => {
    return api.post('/logout');
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<AxiosResponse<any>> => {
    return api.get('/me');
  },

  // 获取用户列表
  getUsers: (): Promise<AxiosResponse<any>> => {
    return api.get('/users');
  },
};

export default api;
