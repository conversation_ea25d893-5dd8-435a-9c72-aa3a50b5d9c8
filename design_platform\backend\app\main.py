"""
EduBrain设计平台后端主应用
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse
from contextlib import asynccontextmanager
import uvicorn
import logging

from app.core.config import settings
from app.core.database import engine, Base
from app.api.v1.api import api_router
from app.core.init_data import init_sample_data

# 配置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 控制台输出
        logging.FileHandler('logs/app.log', encoding='utf-8')  # 文件输出
    ]
)

# 设置特定模块的日志级别
logging.getLogger('app.services.llm_service').setLevel(logging.INFO)
logging.getLogger('app.services.cognitive_diagnosis_service').setLevel(logging.INFO)
logging.getLogger('uvicorn.access').setLevel(logging.WARNING)  # 减少访问日志


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger = logging.getLogger(__name__)

    # 启动时创建数据库表
    Base.metadata.create_all(bind=engine)
    logger.info("🗄️ 数据库表创建完成")

    # 初始化示例数据
    try:
        init_sample_data()
        logger.info("📊 示例数据初始化完成")
    except Exception as e:
        logger.error(f"❌ 示例数据初始化失败: {e}")

    # 测试日志输出
    logger.info("🚀 EduBrain设计平台后端服务启动完成")
    logger.info("📋 日志系统已配置，可以查看详细的服务日志")

    yield
    # 关闭时的清理工作
    logger.info("🔚 应用关闭")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="知擎EduBrain双驱协同认知诊断与规划系统API - 智能教育认知诊断与学习规划平台后端服务",
    version=settings.VERSION,
    openapi_url="/openapi.json",  # 修复API文档路径
    lifespan=lifespan
)

# 配置CORS - 支持内网穿透
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件
import os
static_dir = "static"
if not os.path.exists(static_dir):
    os.makedirs(static_dir)
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 添加重定向路由来处理错误的API调用
@app.get("/datasets")
@app.get("/datasets/")
async def redirect_datasets():
    """重定向错误的数据集API调用"""
    return RedirectResponse(url="/api/v1/datasets/", status_code=301)

@app.get("/datasets/{dataset_id}")
async def redirect_dataset_detail(dataset_id: int):
    """重定向错误的数据集详情API调用"""
    return RedirectResponse(url=f"/api/v1/datasets/{dataset_id}", status_code=301)

@app.get("/datasets/{dataset_id}/stats")
async def redirect_dataset_stats(dataset_id: int):
    """重定向错误的数据集统计API调用"""
    return RedirectResponse(url=f"/api/v1/datasets/{dataset_id}/stats", status_code=301)

@app.get("/experiments")
@app.get("/experiments/")
async def redirect_experiments():
    """重定向错误的实验API调用"""
    return RedirectResponse(url="/api/v1/experiments/", status_code=301)

@app.get("/experiments/{experiment_id}")
async def redirect_experiment_detail(experiment_id: int):
    """重定向错误的实验详情API调用"""
    return RedirectResponse(url=f"/api/v1/experiments/{experiment_id}", status_code=301)

# 可视化API重定向
@app.get("/visualization/experiment/{experiment_id}/training-progress")
async def redirect_training_progress(experiment_id: int):
    """重定向错误的训练进度可视化API调用"""
    return RedirectResponse(url=f"/api/v1/visualization/experiment/{experiment_id}/training-progress", status_code=301)

@app.get("/visualization/experiment/{experiment_id}/oversmoothing")
async def redirect_oversmoothing(experiment_id: int):
    """重定向错误的过度平滑分析API调用"""
    return RedirectResponse(url=f"/api/v1/visualization/experiment/{experiment_id}/oversmoothing", status_code=301)

@app.get("/visualization/experiment/{experiment_id}/metrics")
async def redirect_experiment_metrics(experiment_id: int):
    """重定向错误的实验指标API调用"""
    return RedirectResponse(url=f"/api/v1/visualization/experiment/{experiment_id}/metrics", status_code=301)

@app.get("/visualization/dataset/{dataset_id}/overview")
async def redirect_dataset_overview(dataset_id: int):
    """重定向错误的数据集概览API调用"""
    return RedirectResponse(url=f"/api/v1/visualization/dataset/{dataset_id}/overview", status_code=301)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "EduBrain设计平台API",
        "version": settings.VERSION,
        "docs_url": "/docs"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
