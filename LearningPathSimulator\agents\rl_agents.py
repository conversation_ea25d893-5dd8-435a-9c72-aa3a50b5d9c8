"""
强化学习智能体实现

包含各种强化学习算法的路径规划智能体。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical
import random
from collections import deque, namedtuple
from typing import Dict, List, Tuple, Any, Optional
import math

from .base_agent import BaseAgent


# ============================================================================
# Q-Learning智能体
# ============================================================================

class QLearningAgent(BaseAgent):
    """Q-Learning智能体：经典的值函数学习算法"""
    
    def __init__(self, action_space_size: int, 
                 learning_rate: float = 0.1,
                 discount_factor: float = 0.95,
                 epsilon: float = 0.1,
                 epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01):
        super().__init__(action_space_size, "QLearningAgent")
        
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        
        # Q表：使用字典存储状态-动作值
        self.q_table = {}
        
        # 上一步的状态和动作（用于Q值更新）
        self.last_state = None
        self.last_action = None
    
    def _state_to_key(self, state: np.ndarray) -> str:
        """将连续状态转换为离散的键值"""
        # 将状态离散化为0.1的精度
        discretized = np.round(state, 1)
        return str(discretized.tolist())
    
    def get_action(self, observation: np.ndarray) -> int:
        """使用ε-贪心策略选择动作"""
        state_key = self._state_to_key(observation)
        
        # 初始化Q值（如果状态未见过）
        if state_key not in self.q_table:
            self.q_table[state_key] = np.zeros(self.action_space_size)
        
        # ε-贪心策略
        if random.random() < self.epsilon:
            # 探索：随机选择
            action = random.randint(0, self.action_space_size - 1)
        else:
            # 利用：选择Q值最大的动作
            q_values = self.q_table[state_key]
            action = np.argmax(q_values)
        
        # 保存当前状态和动作
        self.last_state = state_key
        self.last_action = action
        
        return action
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """更新Q值"""
        super().update(observation, action, reward, next_observation, done)
        
        if self.last_state is not None and self.last_action is not None:
            # 获取下一状态的最大Q值
            next_state_key = self._state_to_key(next_observation)
            if next_state_key not in self.q_table:
                self.q_table[next_state_key] = np.zeros(self.action_space_size)
            
            if done:
                next_q_max = 0  # 终止状态的Q值为0
            else:
                next_q_max = np.max(self.q_table[next_state_key])
            
            # Q-Learning更新公式
            current_q = self.q_table[self.last_state][self.last_action]
            target_q = reward + self.discount_factor * next_q_max
            
            self.q_table[self.last_state][self.last_action] += (
                self.learning_rate * (target_q - current_q)
            )
        
        # 衰减探索率
        if done:
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
    
    def get_exploration_rate(self) -> float:
        return self.epsilon
    
    def set_exploration_rate(self, rate: float):
        self.epsilon = max(self.epsilon_min, min(1.0, rate))
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        state_key = self._state_to_key(observation)
        q_values = self.q_table.get(state_key, np.zeros(self.action_space_size))
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'q_values': q_values.tolist(),
            'epsilon': self.epsilon,
            'explanation': f"Q-Learning选择动作{action}，Q值={q_values[action]:.3f}，ε={self.epsilon:.3f}",
            'strategy': 'epsilon_greedy_q_learning'
        }


# ============================================================================
# 深度Q网络(DQN)智能体
# ============================================================================

class DQNNetwork(nn.Module):
    """DQN神经网络"""
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 128):
        super(DQNNetwork, self).__init__()
        
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, action_size)
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            module.bias.data.fill_(0.01)
    
    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        return self.fc3(x)


Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])


class DQNAgent(BaseAgent):
    """深度Q网络智能体"""
    
    def __init__(self, action_space_size: int, state_size: int,
                 learning_rate: float = 0.001,
                 discount_factor: float = 0.95,
                 epsilon: float = 0.1,
                 epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01,
                 memory_size: int = 10000,
                 batch_size: int = 32,
                 target_update_freq: int = 100,
                 hidden_size: int = 128):
        super().__init__(action_space_size, "DQNAgent")
        
        self.state_size = state_size
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = epsilon_min
        self.batch_size = batch_size
        self.target_update_freq = target_update_freq
        
        # 神经网络
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.q_network = DQNNetwork(state_size, action_space_size, hidden_size).to(self.device)
        self.target_network = DQNNetwork(state_size, action_space_size, hidden_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=learning_rate)
        
        # 经验回放缓冲区
        self.memory = deque(maxlen=memory_size)
        
        # 训练计数器
        self.update_count = 0
        
        # 同步目标网络
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def get_action(self, observation: np.ndarray) -> int:
        """使用ε-贪心策略选择动作"""
        if random.random() < self.epsilon:
            return random.randint(0, self.action_space_size - 1)
        
        # 使用神经网络预测Q值
        state_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
        with torch.no_grad():
            q_values = self.q_network(state_tensor)
        
        return q_values.argmax().item()
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """存储经验并训练网络"""
        super().update(observation, action, reward, next_observation, done)
        
        # 存储经验
        experience = Experience(observation, action, reward, next_observation, done)
        self.memory.append(experience)
        
        # 如果有足够的经验，进行训练
        if len(self.memory) >= self.batch_size:
            self._train()
        
        # 衰减探索率
        if done:
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
    
    def _train(self):
        """训练DQN网络"""
        # 随机采样批次
        batch = random.sample(self.memory, self.batch_size)
        
        states = torch.FloatTensor([e.state for e in batch]).to(self.device)
        actions = torch.LongTensor([e.action for e in batch]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in batch]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in batch]).to(self.device)
        dones = torch.BoolTensor([e.done for e in batch]).to(self.device)
        
        # 当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # 目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (self.discount_factor * next_q_values * ~dones)
        
        # 计算损失
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        # 更新目标网络
        self.update_count += 1
        if self.update_count % self.target_update_freq == 0:
            self.target_network.load_state_dict(self.q_network.state_dict())
    
    def get_exploration_rate(self) -> float:
        return self.epsilon
    
    def set_exploration_rate(self, rate: float):
        self.epsilon = max(self.epsilon_min, min(1.0, rate))


# ============================================================================
# PPO智能体
# ============================================================================

class PPONetwork(nn.Module):
    """PPO策略网络"""
    
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 128):
        super(PPONetwork, self).__init__()
        
        # 共享特征层
        self.shared_layers = nn.Sequential(
            nn.Linear(state_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU()
        )
        
        # 策略头（输出动作概率）
        self.policy_head = nn.Linear(hidden_size, action_size)
        
        # 价值头（输出状态价值）
        self.value_head = nn.Linear(hidden_size, 1)
        
        # 权重初始化
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.orthogonal_(module.weight, gain=np.sqrt(2))
            module.bias.data.fill_(0.0)
    
    def forward(self, x):
        shared_features = self.shared_layers(x)
        
        # 策略输出（动作概率）
        policy_logits = self.policy_head(shared_features)
        
        # 价值输出
        value = self.value_head(shared_features)
        
        return policy_logits, value
    
    def get_action_and_value(self, x):
        """获取动作和价值"""
        policy_logits, value = self.forward(x)
        
        # 创建动作分布
        action_dist = Categorical(logits=policy_logits)
        action = action_dist.sample()
        
        return action, action_dist.log_prob(action), action_dist.entropy(), value


PPOExperience = namedtuple('PPOExperience', 
    ['state', 'action', 'reward', 'next_state', 'done', 'log_prob', 'value'])


class PPOAgent(BaseAgent):
    """PPO（Proximal Policy Optimization）智能体"""
    
    def __init__(self, action_space_size: int, state_size: int,
                 learning_rate: float = 3e-4,
                 discount_factor: float = 0.99,
                 gae_lambda: float = 0.95,
                 clip_epsilon: float = 0.2,
                 value_loss_coef: float = 0.5,
                 entropy_coef: float = 0.01,
                 max_grad_norm: float = 0.5,
                 ppo_epochs: int = 4,
                 batch_size: int = 64,
                 buffer_size: int = 2048,
                 hidden_size: int = 128):
        super().__init__(action_space_size, "PPOAgent")
        
        self.state_size = state_size
        self.learning_rate = learning_rate
        self.discount_factor = discount_factor
        self.gae_lambda = gae_lambda
        self.clip_epsilon = clip_epsilon
        self.value_loss_coef = value_loss_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.ppo_epochs = ppo_epochs
        self.batch_size = batch_size
        self.buffer_size = buffer_size
        
        # 神经网络
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.network = PPONetwork(state_size, action_space_size, hidden_size).to(self.device)
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)
        
        # 经验缓冲区
        self.buffer = []
        self.buffer_full = False
        
        # 训练统计
        self.training_stats = {
            'policy_loss': [],
            'value_loss': [],
            'entropy_loss': [],
            'total_loss': []
        }
    
    def get_action(self, observation: np.ndarray) -> int:
        """使用策略网络选择动作"""
        state_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action, log_prob, entropy, value = self.network.get_action_and_value(state_tensor)
        
        # 保存用于训练的信息
        self.last_log_prob = log_prob.item()
        self.last_value = value.item()
        
        return action.item()
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """存储经验"""
        super().update(observation, action, reward, next_observation, done)
        
        # 存储经验
        if hasattr(self, 'last_log_prob') and hasattr(self, 'last_value'):
            experience = PPOExperience(
                observation, action, reward, next_observation, done,
                self.last_log_prob, self.last_value
            )
            self.buffer.append(experience)
        
        # 如果缓冲区满了，进行训练
        if len(self.buffer) >= self.buffer_size:
            self._train()
            self.buffer = []
    
    def _compute_gae(self, rewards, values, next_values, dones):
        """计算广义优势估计(GAE)"""
        advantages = []
        gae = 0
        
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_value = next_values[i]
            else:
                next_value = values[i + 1]
            
            delta = rewards[i] + self.discount_factor * next_value * (1 - dones[i]) - values[i]
            gae = delta + self.discount_factor * self.gae_lambda * (1 - dones[i]) * gae
            advantages.insert(0, gae)
        
        return advantages
    
    def _train(self):
        """PPO训练"""
        if len(self.buffer) < self.batch_size:
            return
        
        # 提取数据
        states = torch.FloatTensor([e.state for e in self.buffer]).to(self.device)
        actions = torch.LongTensor([e.action for e in self.buffer]).to(self.device)
        rewards = [e.reward for e in self.buffer]
        dones = [e.done for e in self.buffer]
        old_log_probs = torch.FloatTensor([e.log_prob for e in self.buffer]).to(self.device)
        old_values = torch.FloatTensor([e.value for e in self.buffer]).to(self.device)
        
        # 计算下一状态的价值
        next_states = torch.FloatTensor([e.next_state for e in self.buffer]).to(self.device)
        with torch.no_grad():
            _, next_values = self.network(next_states)
            next_values = next_values.squeeze().cpu().numpy()
        
        # 计算优势和回报
        advantages = self._compute_gae(rewards, old_values.cpu().numpy(), next_values, dones)
        advantages = torch.FloatTensor(advantages).to(self.device)
        returns = advantages + old_values
        
        # 标准化优势
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # PPO训练循环
        for _ in range(self.ppo_epochs):
            # 随机打乱数据
            indices = torch.randperm(len(self.buffer))
            
            for start in range(0, len(self.buffer), self.batch_size):
                end = start + self.batch_size
                batch_indices = indices[start:end]
                
                batch_states = states[batch_indices]
                batch_actions = actions[batch_indices]
                batch_old_log_probs = old_log_probs[batch_indices]
                batch_advantages = advantages[batch_indices]
                batch_returns = returns[batch_indices]
                
                # 前向传播
                policy_logits, values = self.network(batch_states)
                action_dist = Categorical(logits=policy_logits)
                
                new_log_probs = action_dist.log_prob(batch_actions)
                entropy = action_dist.entropy().mean()
                
                # 计算比率
                ratio = torch.exp(new_log_probs - batch_old_log_probs)
                
                # PPO损失
                surr1 = ratio * batch_advantages
                surr2 = torch.clamp(ratio, 1 - self.clip_epsilon, 1 + self.clip_epsilon) * batch_advantages
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # 价值损失
                value_loss = F.mse_loss(values.squeeze(), batch_returns)
                
                # 总损失
                total_loss = (policy_loss + 
                             self.value_loss_coef * value_loss - 
                             self.entropy_coef * entropy)
                
                # 反向传播
                self.optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.network.parameters(), self.max_grad_norm)
                self.optimizer.step()
                
                # 记录统计信息
                self.training_stats['policy_loss'].append(policy_loss.item())
                self.training_stats['value_loss'].append(value_loss.item())
                self.training_stats['entropy_loss'].append(entropy.item())
                self.training_stats['total_loss'].append(total_loss.item())
    
    def get_training_stats(self) -> Dict[str, List[float]]:
        """获取训练统计信息"""
        return self.training_stats
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        state_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            policy_logits, value = self.network(state_tensor)
            action_probs = F.softmax(policy_logits, dim=-1)
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'action_probabilities': action_probs.squeeze().cpu().numpy().tolist(),
            'state_value': value.item(),
            'explanation': f"PPO选择动作{action}，概率={action_probs[0][action]:.3f}，状态价值={value.item():.3f}",
            'strategy': 'ppo_policy_gradient'
        }
