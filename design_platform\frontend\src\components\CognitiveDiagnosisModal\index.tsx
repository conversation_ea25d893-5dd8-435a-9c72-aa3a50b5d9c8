import React, { useState } from 'react';
import {
  Card,
  Button,
  Select,
  Form,
  message,
  Spin,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Switch,
  Alert,
} from 'antd';
import {
  BulbOutlined,
  ExperimentOutlined,
  RobotOutlined,
  UserOutlined,
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { cognitiveService } from '../../services/api';
import DiagnosisReport from '../DiagnosisReport';

const { Title, Text } = Typography;
const { Option } = Select;

interface CognitiveDiagnosisModalProps {
  experimentId: number | string;
  onClose: () => void;
  onDiagnosisComplete?: () => void;
}

interface DiagnosisResult {
  student_id: string;
  knowledge_diagnosis: Record<string, any>;
  overall_ability: number;
  cognitive_vector?: number[];
  data_quality?: string;
  llm_report?: any;
}

const CognitiveDiagnosisModal: React.FC<CognitiveDiagnosisModalProps> = ({
  experimentId,
  onClose,
  onDiagnosisComplete
}) => {
  const [selectedStudent, setSelectedStudent] = useState<string>('student_001');
  const [diagnosisResult, setDiagnosisResult] = useState<DiagnosisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [useLLM, setUseLLM] = useState<boolean>(true);
  const [enhancedDiagnosisResult, setEnhancedDiagnosisResult] = useState<any>(null);

  // 预定义学生列表
  const predefinedStudents = [
    { id: 'student_001', name: '学生001' },
    { id: 'student_002', name: '学生002' },
    { id: 'student_003', name: '学生003' },
    { id: 'student_004', name: '学生004' },
    { id: 'student_005', name: '学生005' },
  ];

  const handleDiagnosis = async () => {
    if (!selectedStudent) {
      message.warning('请选择学生');
      return;
    }

    setLoading(true);
    setDiagnosisResult(null);
    setEnhancedDiagnosisResult(null);

    try {
      // 根据是否启用LLM选择不同的API
      const expId = typeof experimentId === 'string' ? parseInt(experimentId) : experimentId;

      console.log('开始诊断:', {
        student_id: selectedStudent,
        experiment_id: expId,
        useLLM: useLLM
      });

      const response = useLLM
        ? await cognitiveService.diagnoseStudentWithReport({
            student_id: selectedStudent,
            experiment_id: expId
          })
        : await cognitiveService.diagnoseStudent({
            student_id: selectedStudent,
            experiment_id: expId
          });

      console.log('API响应状态:', response.status);
      console.log('API响应头:', response.headers);

      console.log('API响应:', response.data); // 添加调试日志

      if (response.data.success) {
        const result = response.data.diagnosis;
        setDiagnosisResult(result);

        // 如果使用了LLM并且有报告，设置增强诊断结果
        if (useLLM && result.llm_report) {
          setEnhancedDiagnosisResult(result);
        }

        message.success('认知诊断完成！');

        // 调用诊断完成回调
        onDiagnosisComplete?.();
      } else {
        console.error('诊断失败，API返回:', response.data);
        message.error('诊断失败');
      }
    } catch (error: any) {
      console.error('诊断请求失败:', error);

      let errorMessage = '认知诊断失败';
      if (error.response) {
        // 服务器返回了错误响应
        console.error('错误响应状态:', error.response.status);
        console.error('错误响应数据:', error.response.data);
        errorMessage = `诊断失败: ${error.response.data?.detail || error.response.statusText}`;
      } else if (error.request) {
        // 请求发送了但没有收到响应
        console.error('请求超时或网络错误:', error.request);
        errorMessage = '网络连接失败，请检查后端服务是否正常运行';
      } else {
        // 其他错误
        console.error('请求配置错误:', error.message);
        errorMessage = `请求错误: ${error.message}`;
      }

      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getRadarChart = () => {
    if (!diagnosisResult?.knowledge_diagnosis) return null;

    const knowledgeData = Object.entries(diagnosisResult.knowledge_diagnosis).map(
      ([name, data]: [string, any]) => ({
        name,
        value: data.mastery_probability
      })
    );

    const option = {
      title: {
        text: '知识点掌握程度',
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params: any) {
          const dataIndex = params.dataIndex;
          const knowledge = knowledgeData[dataIndex];
          return `${knowledge.name}<br/>掌握程度: ${(knowledge.value * 100).toFixed(1)}%`;
        }
      },
      legend: {
        data: [selectedStudent],
        bottom: 10
      },
      radar: {
        indicator: knowledgeData.map(item => ({
          name: item.name,
          max: 1,
          axisLabel: {
            formatter: function(value: number) {
              return (value * 100).toFixed(0) + '%';
            }
          }
        })),
        shape: 'polygon',
        splitNumber: 4,
        axisLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        splitLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(114, 172, 209, 0.1)', 'rgba(114, 172, 209, 0.05)']
          }
        }
      },
      series: [{
        name: '掌握程度',
        type: 'radar',
        data: [{
          value: knowledgeData.map(item => item.value),
          name: selectedStudent,
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: 'rgba(24, 144, 255, 0.2)'
          },
          lineStyle: {
            width: 2
          }
        }]
      }]
    };

    return (
      <div style={{ height: '300px' }}>
        <ReactECharts
          option={option}
          style={{ height: '100%', width: '100%' }}
          opts={{ renderer: 'canvas' }}
        />
      </div>
    );
  };

  return (
    <div>
      {/* 诊断控制面板 */}
      <Card title="诊断设置" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>选择学生:</Text>
              <Select
                value={selectedStudent}
                onChange={setSelectedStudent}
                style={{ width: '100%' }}
                placeholder="选择要诊断的学生"
              >
                {predefinedStudents.map(student => (
                  <Option key={student.id} value={student.id}>
                    <UserOutlined style={{ marginRight: 8 }} />
                    {student.name}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={8}>
            <Space direction="vertical">
              <Text strong>诊断模式:</Text>
              <Space>
                <Switch
                  checked={useLLM}
                  onChange={setUseLLM}
                  checkedChildren={<RobotOutlined />}
                  unCheckedChildren={<ExperimentOutlined />}
                />
                <Text>{useLLM ? 'AI智能诊断' : '基础诊断'}</Text>
              </Space>
            </Space>
          </Col>
          <Col span={8}>
            <Button
              type="primary"
              size="large"
              icon={useLLM ? <BulbOutlined /> : <ExperimentOutlined />}
              onClick={handleDiagnosis}
              loading={loading}
              style={{ width: '100%' }}
            >
              {useLLM ? '开始AI诊断' : '开始基础诊断'}
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 加载状态 */}
      {loading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text>正在进行认知诊断分析...</Text>
              {useLLM && (
                <div style={{ marginTop: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <RobotOutlined style={{ marginRight: '4px' }} />
                    AI正在生成个性化分析报告
                  </Text>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* 诊断结果 */}
      {diagnosisResult && !loading && (
        <div>
          {/* 概览统计 */}
          <Card title="诊断概览" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="整体能力"
                  value={diagnosisResult.overall_ability}
                  precision={3}
                  suffix="/ 1.0"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="掌握知识点"
                  value={Object.values(diagnosisResult.knowledge_diagnosis || {}).filter((item: any) => item?.mastery_probability > 0.7).length}
                  suffix="个"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="部分掌握"
                  value={Object.values(diagnosisResult.knowledge_diagnosis || {}).filter((item: any) => item?.mastery_probability >= 0.5 && item?.mastery_probability <= 0.7).length}
                  suffix="个"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="薄弱知识点"
                  value={Object.values(diagnosisResult.knowledge_diagnosis || {}).filter((item: any) => item?.mastery_probability < 0.5).length}
                  suffix="个"
                />
              </Col>
            </Row>
          </Card>

          {/* 可视化图表 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="雷达图分析">
                {getRadarChart()}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="知识点详情">
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {Object.entries(diagnosisResult.knowledge_diagnosis || {}).map(([name, data]: [string, any]) => (
                    <div key={name} style={{ marginBottom: 8, padding: 8, border: '1px solid #f0f0f0', borderRadius: 4 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong>{name}</Text>
                        <Text type={data.mastery_probability > 0.7 ? 'success' : data.mastery_probability > 0.5 ? 'warning' : 'danger'}>
                          {(data.mastery_probability * 100).toFixed(1)}%
                        </Text>
                      </div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {data.mastery_level} (置信度: {(data.confidence * 100).toFixed(1)}%)
                      </Text>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>

          {/* AI智能诊断报告 */}
          {enhancedDiagnosisResult && enhancedDiagnosisResult.llm_report && (
            <div style={{ marginTop: '16px' }}>
              <DiagnosisReport
                diagnosisData={enhancedDiagnosisResult}
                onRefresh={handleDiagnosis}
              />
            </div>
          )}
        </div>
      )}

      {/* 提示信息 */}
      {!diagnosisResult && !loading && (
        <Alert
          message="使用已训练模型进行认知诊断"
          description="选择学生并点击诊断按钮，系统将使用当前实验的训练结果对学生进行认知诊断分析。AI智能诊断模式将提供更详细的分析报告和学习建议。"
          type="info"
          showIcon
        />
      )}
    </div>
  );
};

export default CognitiveDiagnosisModal;
