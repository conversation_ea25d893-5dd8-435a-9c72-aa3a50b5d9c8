import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Statistic, Typography, Button, Space, Alert, Spin
} from 'antd';
import ReactECharts from 'echarts-for-react';
import {
  ExperimentOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  Bar<PERSON>hartOutlined,
  UserOutlined,
  FileTextOutlined,
  RobotOutlined,
  SettingOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import Logo from '../../components/Logo';
import { datasetService, experimentService } from '../../services/api';

const { Title, Paragraph } = Typography;

interface DashboardStats {
  totalExperiments: number;
  runningExperiments: number;
  completedExperiments: number;
  availableDatasets: number;
  totalStudents: number;
  totalQuestions: number;
  avgAccuracy: number;
}

const SimpleDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalExperiments: 0,
    runningExperiments: 0,
    completedExperiments: 0,
    availableDatasets: 0,
    totalStudents: 0,
    totalQuestions: 0,
    avgAccuracy: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 加载实验统计
      const experimentsResponse = await experimentService.getExperiments();
      const experimentsData = experimentsResponse.data;
      const totalExperiments = experimentsData.length;
      const runningExperiments = experimentsData.filter((exp: any) => exp.status === 'running').length;
      const completedExperiments = experimentsData.filter((exp: any) => exp.status === 'completed').length;

      // 计算平均准确率 (后端返回0-1格式，需要转换为百分比)
      const completedWithMetrics = experimentsData.filter((exp: any) =>
        exp.status === 'completed' && exp.metrics?.accuracy
      );
      const avgAccuracy = completedWithMetrics.length > 0
        ? (completedWithMetrics.reduce((sum: number, exp: any) => sum + exp.metrics.accuracy, 0) / completedWithMetrics.length) * 100
        : 0;

      setStats(prev => ({
        ...prev,
        totalExperiments,
        runningExperiments,
        completedExperiments,
        avgAccuracy
      }));

      // 加载数据集统计
      const datasetsResponse = await datasetService.getDatasets();
      const datasetsData = datasetsResponse.data;

      let datasets: any[] = [];
      if (Array.isArray(datasetsData)) {
        datasets = datasetsData;
      } else if (datasetsData && typeof datasetsData === 'object') {
        const data = datasetsData as any;
        if (data.datasets && Array.isArray(data.datasets)) {
          datasets = data.datasets;
        } else if (data.data && Array.isArray(data.data)) {
          datasets = data.data;
        }
      }

      const totalStudents = datasets.reduce((sum: number, ds: any) => sum + (ds.student_num || 0), 0);
      const totalQuestions = datasets.reduce((sum: number, ds: any) => sum + (ds.exercise_num || 0), 0);

      setStats(prev => ({
        ...prev,
        availableDatasets: datasets.length,
        totalStudents,
        totalQuestions
      }));
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 快捷操作配置
  const quickActions = [
    {
      title: '开始训练',
      description: '创建新的模型训练任务',
      icon: <RobotOutlined />,
      color: '#1890ff',
      action: () => navigate('/dashboard/training')
    },
    {
      title: '数据管理',
      description: '管理训练数据集',
      icon: <DatabaseOutlined />,
      color: '#52c41a',
      action: () => navigate('/datasets')
    },
    {
      title: '实验监控',
      description: '查看实验运行状态',
      icon: <ExperimentOutlined />,
      color: '#faad14',
      action: () => navigate('/experiments')
    },
    {
      title: '结果分析',
      description: '查看训练结果和性能',
      icon: <BarChartOutlined />,
      color: '#722ed1',
      action: () => navigate('/analysis')
    },
    {
      title: '认知诊断',
      description: '进行学生认知诊断',
      icon: <EyeOutlined />,
      color: '#eb2f96',
      action: () => navigate('/cognitive-diagnosis-center')
    },
    {
      title: '系统设置',
      description: '配置系统参数',
      icon: <SettingOutlined />,
      color: '#13c2c2',
      action: () => navigate('/dashboard/settings')
    }
  ];

  // 实验状态分布饼图
  const getExperimentStatusChart = () => {
    const option = {
      title: {
        text: '实验状态分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['运行中', '已完成', '失败', '等待中']
      },
      series: [
        {
          name: '实验状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: stats.runningExperiments, name: '运行中', itemStyle: { color: '#52c41a' } },
            { value: stats.completedExperiments, name: '已完成', itemStyle: { color: '#1890ff' } },
            { value: stats.totalExperiments - stats.runningExperiments - stats.completedExperiments, name: '等待中', itemStyle: { color: '#faad14' } }
          ]
        }
      ]
    };
    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  // 性能趋势图
  const getPerformanceTrendChart = () => {
    const option = {
      title: {
        text: '模型性能趋势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['准确率', 'AUC', 'F1-Score'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['实验1', '实验2', '实验3', '实验4', '实验5', '实验6', '实验7']
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '准确率',
          type: 'line',
          smooth: true,
          data: [82.5, 85.2, 87.8, 89.1, 91.3, 93.2, 94.8],
          itemStyle: { color: '#1890ff' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          }
        },
        {
          name: 'AUC',
          type: 'line',
          smooth: true,
          data: [78.3, 81.7, 84.2, 86.8, 88.9, 90.5, 92.1],
          itemStyle: { color: '#52c41a' }
        },
        {
          name: 'F1-Score',
          type: 'line',
          smooth: true,
          data: [75.8, 79.2, 82.1, 84.7, 87.2, 89.8, 91.5],
          itemStyle: { color: '#faad14' }
        }
      ]
    };
    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  // 数据集分布柱状图
  const getDatasetDistributionChart = () => {
    const option = {
      title: {
        text: '数据集分布统计',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['学生数', '题目数'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['数学', '英语', '物理', '化学', '生物', '历史']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '学生数',
          type: 'bar',
          data: [1200, 980, 850, 720, 650, 580],
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: '#1890ff' },
                { offset: 1, color: '#69c0ff' }
              ]
            }
          }
        },
        {
          name: '题目数',
          type: 'bar',
          data: [2400, 1960, 1700, 1440, 1300, 1160],
          itemStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: '#52c41a' },
                { offset: 1, color: '#95de64' }
              ]
            }
          }
        }
      ]
    };
    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#ffffff', minHeight: '100vh' }}>
      {/* Logo和标题 */}
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Logo size="large" />
          <div style={{ flex: 1 }}>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              知擎EduBrain 双重协同网络认知诊断与规划系统
            </Title>
            <Paragraph style={{ margin: '4px 0 0 0', color: '#666' }}>
              智能教育认知诊断与学习规划平台 - 欢迎使用
            </Paragraph>
          </div>
        </div>
      </Card>

      {/* 核心数据统计 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总实验数"
              value={stats.totalExperiments}
              prefix={<ExperimentOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.runningExperiments}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completedExperiments}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均准确率"
              value={stats.avgAccuracy}
              precision={1}
              suffix="%"
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据概览 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="可用数据集"
              value={stats.availableDatasets}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总学生数"
              value={stats.totalStudents}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总题目数"
              value={stats.totalQuestions}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 可视化图表 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} lg={8}>
          <Card>
            {getExperimentStatusChart()}
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card>
            {getPerformanceTrendChart()}
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card>
            {getDatasetDistributionChart()}
          </Card>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Card title="快捷操作" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card
                hoverable
                size="small"
                onClick={action.action}
                style={{ 
                  cursor: 'pointer',
                  borderLeft: `4px solid ${action.color}`,
                  transition: 'all 0.3s ease'
                }}
                bodyStyle={{ padding: '16px' }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <div style={{ 
                    fontSize: '24px', 
                    color: action.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '40px',
                    height: '40px',
                    borderRadius: '8px',
                    backgroundColor: `${action.color}15`
                  }}>
                    {action.icon}
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                      {action.title}
                    </div>
                    <div style={{ color: '#666', fontSize: '12px' }}>
                      {action.description}
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 系统状态提示 */}
      <Alert
        message="系统运行正常"
        description="所有服务正常运行，您可以开始使用各项功能。"
        type="success"
        showIcon
        style={{ marginBottom: '24px' }}
      />
    </div>
  );
};

export default SimpleDashboard;
