#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_header() {
    echo
    echo "========================================"
    print_message "$1" "$CYAN"
    echo "========================================"
    echo
}

print_success() {
    print_message "✅ $1" "$GREEN"
}

print_error() {
    print_message "❌ $1" "$RED"
}

print_info() {
    print_message "🔍 $1" "$BLUE"
}

print_warning() {
    print_message "⚠️  $1" "$YELLOW"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装或未添加到 PATH"
        return 1
    fi
    return 0
}

# 检查依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查 Python
    if ! check_command python3; then
        print_error "请安装 Python 3.8+"
        return 1
    fi
    
    # 检查 Node.js
    if ! check_command node; then
        print_error "请安装 Node.js 16+"
        return 1
    fi
    
    # 检查 npm
    if ! check_command npm; then
        print_error "请安装 npm"
        return 1
    fi
    
    print_success "系统依赖检查完成"
    return 0
}

# 检查项目文件
check_project_files() {
    print_info "检查项目文件..."
    
    if [ ! -f "backend/requirements.txt" ]; then
        print_error "未找到 backend/requirements.txt"
        return 1
    fi
    
    if [ ! -f "frontend/package.json" ]; then
        print_error "未找到 frontend/package.json"
        return 1
    fi
    
    print_success "项目文件检查完成"
    return 0
}

# 安装后端依赖
install_backend_deps() {
    print_info "检查后端依赖..."
    
    cd backend
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        print_info "创建 Python 虚拟环境..."
        python3 -m venv venv
        if [ $? -ne 0 ]; then
            print_error "虚拟环境创建失败"
            cd ..
            return 1
        fi
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        print_error "后端依赖安装失败"
        cd ..
        return 1
    fi
    
    cd ..
    print_success "后端依赖安装完成"
    return 0
}

# 安装前端依赖
install_frontend_deps() {
    print_info "检查前端依赖..."
    
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        print_info "安装前端依赖..."
        npm install
        if [ $? -ne 0 ]; then
            print_error "前端依赖安装失败"
            cd ..
            return 1
        fi
    fi
    
    cd ..
    print_success "前端依赖安装完成"
    return 0
}

# 启动后端服务
start_backend() {
    print_info "启动后端服务..."
    
    cd backend
    source venv/bin/activate
    
    # 在后台启动后端
    python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 检查后端是否启动成功
    if kill -0 $BACKEND_PID 2>/dev/null; then
        print_success "后端服务启动成功 (PID: $BACKEND_PID)"
        echo $BACKEND_PID > .backend.pid
        return 0
    else
        print_error "后端服务启动失败"
        return 1
    fi
}

# 启动前端服务
start_frontend() {
    print_info "启动前端服务..."
    
    cd frontend
    
    # 在后台启动前端
    npm start &
    FRONTEND_PID=$!
    
    cd ..
    
    # 等待前端启动
    sleep 3
    
    # 检查前端是否启动成功
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        print_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > .frontend.pid
        return 0
    else
        print_error "前端服务启动失败"
        return 1
    fi
}

# 停止服务
stop_services() {
    print_info "停止服务..."
    
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            print_success "后端服务已停止"
        fi
        rm -f .backend.pid
    fi
    
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            print_success "前端服务已停止"
        fi
        rm -f .frontend.pid
    fi
}

# 信号处理
cleanup() {
    echo
    print_info "收到停止信号，正在清理..."
    stop_services
    exit 0
}

# 设置信号处理器
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    print_header "🚀 EduBrain 设计平台 - 开发环境启动"
    
    # 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    # 检查项目文件
    if ! check_project_files; then
        exit 1
    fi
    
    # 安装依赖
    if ! install_backend_deps; then
        exit 1
    fi
    
    if ! install_frontend_deps; then
        exit 1
    fi
    
    # 启动服务
    if ! start_backend; then
        exit 1
    fi
    
    if ! start_frontend; then
        stop_services
        exit 1
    fi
    
    print_header "✨ 开发环境启动完成！"
    echo
    print_message "📋 服务地址:" "$CYAN"
    print_message "  🔗 前端应用: http://localhost:3000" "$GREEN"
    print_message "  🔗 后端 API: http://localhost:8000" "$GREEN"
    print_message "  🔗 API 文档: http://localhost:8000/docs" "$GREEN"
    echo
    print_message "💡 按 Ctrl+C 停止所有服务" "$YELLOW"
    echo "========================================"
    
    # 等待用户中断
    while true; do
        sleep 1
    done
}

# 运行主函数
main
