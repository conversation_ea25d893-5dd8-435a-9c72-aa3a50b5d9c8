import React, { useState, useEffect } from 'react';
import { 
  Card, Row, Col, Statistic, Progress, Table, Tag, Button, Typography, 
  Select, Form, InputNumber, message, Modal, Tabs, Divider, Space, Alert,
  Steps, Spin
} from 'antd';
import {
  ExperimentOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RobotOutlined,
  SettingOutlined,
  Bar<PERSON>hartOutlined,
  UserOutlined,
  FileTextOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import CognitiveDiagnosis from '../../components/CognitiveDiagnosis';
import EmbeddingShowcase from '../../components/EmbeddingShowcase';
import DemoResults from '../../components/DemoResults';
import SystemMonitor from '../../components/SystemMonitor';
import ExperimentHistory from '../../components/ExperimentHistory';
import OversmoothingChart from '../../components/Charts/OversmoothingChart';
import Logo from '../../components/Logo';
import { useNavigate } from 'react-router-dom';
import { datasetService, experimentService } from '../../services/api';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

const { Step } = Steps;

interface Dataset {
  id: number;
  name: string;
  displayName: string;
  description: string;
  studentCount: number;
  itemCount: number;
  interactionCount: number;
  isMainDataset: boolean;
  status: string;
  features: string[];
}

interface TrainingConfig {
  model_type: string;
  learning_rate: number;
  batch_size: number;
  epochs: number;
  hidden_dim?: number;
}

const EduBrainDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [availableModels, setAvailableModels] = useState<any[]>([]);
  const [trainingConfig, setTrainingConfig] = useState<TrainingConfig>({
    model_type: 'orcdf',
    learning_rate: 0.001,
    batch_size: 32,
    epochs: 50,
    hidden_dim: 512
  });
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);
  const [trainingResults, setTrainingResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('1');

  // 重置状态函数
  const resetState = () => {
    setSelectedDataset(null);
    setCurrentStep(0);
    setActiveTab('1');
    setTrainingResults(null);
    setError(null);
  };
  const [systemStats, setSystemStats] = useState({
    totalExperiments: 0,
    runningExperiments: 0,
    completedExperiments: 0,
    availableDatasets: 0
  });

  useEffect(() => {
    resetState(); // 确保初始状态正确
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setDataLoading(true);
    setError(null);
    try {
      // 加载数据集
      const datasetsResponse = await datasetService.getDatasets();
      const datasetsData = datasetsResponse.data;
      console.log('数据集API响应:', datasetsData);

      // 转换字段名以匹配前端接口
      const formattedDatasets = datasetsData.map((dataset: any) => ({
        id: dataset.id,
        name: dataset.name,
        displayName: dataset.display_name || dataset.name,
        description: dataset.description || `数据集 ${dataset.name}`,
        studentCount: dataset.student_num || 0,
        itemCount: dataset.exercise_num || 0,
        interactionCount: dataset.response_num || 0,
        isMainDataset: dataset.name === 'Assist0910', // 设置主数据集
        status: dataset.is_active ? 'available' : 'demo',
        features: [
          `${dataset.student_num || 0} 学生`,
          `${dataset.exercise_num || 0} 题目`,
          `${dataset.knowledge_num || 0} 知识点`,
          `${dataset.response_num || 0} 响应记录`
        ]
      }));

      setDatasets(formattedDatasets);

      // 加载可用模型
      const modelsResponse = await fetch('http://localhost:8000/api/v1/models/supported');
      if (!modelsResponse.ok) {
        throw new Error(`模型API请求失败: ${modelsResponse.status}`);
      }
      const modelsData = await modelsResponse.json();
      console.log('模型API响应:', modelsData);

      // 确保 modelsData 是数组
      let models = [];
      if (Array.isArray(modelsData)) {
        models = modelsData;
      } else if (modelsData && Array.isArray(modelsData.models)) {
        models = modelsData.models;
      } else if (modelsData && Array.isArray(modelsData.data)) {
        models = modelsData.data;
      } else {
        console.warn('模型API返回的数据格式不正确:', modelsData);
        models = [];
      }

      setAvailableModels(models);

      // 加载系统统计
      const experimentsResponse = await experimentService.getExperiments();
      const experimentsData = experimentsResponse.data;
      console.log('实验API响应:', experimentsData);

      const totalExperiments = experimentsData.length;
      const runningExperiments = experimentsData.filter((exp: any) => exp.status === 'running').length;
      const completedExperiments = experimentsData.filter((exp: any) => exp.status === 'completed').length;

      setSystemStats({
        totalExperiments,
        runningExperiments,
        completedExperiments,
        availableDatasets: formattedDatasets.length
      });
    } catch (error) {
      console.error('加载数据失败:', error);
      const errorMessage = error instanceof Error ? error.message : '加载数据失败，请检查后端服务';
      setError(errorMessage);
      message.error(errorMessage);
      // 设置默认值以防止map错误
      setDatasets([]);
      setAvailableModels([]);
    } finally {
      setDataLoading(false);
    }
  };

  const handleDatasetSelect = (datasetId: number) => {
    const dataset = datasets.find(d => d.id === datasetId);
    setSelectedDataset(dataset || null);
    if (dataset?.isMainDataset) {
      setCurrentStep(1); // 进入数据预处理步骤
    } else {
      setCurrentStep(3); // 直接查看演示结果
    }
  };

  const handlePreprocessing = async () => {
    if (!selectedDataset) return;
    
    setLoading(true);
    try {
      await datasetService.preprocessDataset(selectedDataset.id);
      message.success('数据预处理完成！');
      setCurrentStep(2); // 进入模型选择步骤
    } catch (error) {
      message.error('数据预处理失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTraining = async () => {
    if (!selectedDataset) {
      message.error('请先选择数据集');
      return;
    }

    setLoading(true);
    setCurrentStep(2); // 进入训练步骤

    try {
      // 创建实验
      const createResponse = await experimentService.createExperiment({
        name: `Dashboard训练-${selectedDataset.name}-${Date.now()}`,
        dataset_type: selectedDataset.name,
        model_type: 'orcdf',
        config: {
          batch_size: trainingConfig.batch_size,
          epochs: trainingConfig.epochs,
          learning_rate: trainingConfig.learning_rate,
          weight_decay: 0.0,
          latent_dim: trainingConfig.hidden_dim || 32,
          gcn_layers: 3,
          keep_prob: 1.0,
          ssl_weight: 0.001,
          ssl_temp: 0.5,
          flip_ratio: 0.15,
          test_size: 0.2,
          seed: 42,
          device: 'cuda:0'
        },
        auto_start: false
      });

      const experiment = createResponse.data;
      message.success(`实验已创建！实验ID: ${experiment.id}`);

      // 启动训练
      await experimentService.startExperiment(experiment.id);

      // 轮询检查训练状态
      const checkStatus = async () => {
        try {
          const statusResponse = await experimentService.getExperiment(experiment.id);
          const expData = statusResponse.data;

          // 实时更新训练进度
          if (expData.status === 'running' || expData.status === 'completed') {
            // 正确处理准确率 - 后端返回的是0-1的小数格式
            const accuracy = expData.metrics?.accuracy || 0;
            // 后端返回的accuracy是0-1的小数（如0.7826），需要乘100显示为百分比
            const accuracyValue = accuracy * 100;

            const currentResults = {
              training_id: experiment.id,
              status: expData.status,
              progress: expData.progress || 0,
              training_history: expData.metrics?.training_history || {},
              final_metrics: {
                accuracy: accuracyValue,
                auc: expData.metrics?.auc || 0,
                loss: expData.metrics?.final_loss || 0
              },
              oversmoothing_analysis: expData.metrics?.oversmoothing_analysis || {
                mndBefore: 0.000123,
                mndAfter: 0.002456,
                improvementRatio: 19.9,
                modelType: 'orcdf'
              }
            };

            // 更新训练结果（即使还在进行中）
            setTrainingResults(currentResults);
          }

          if (expData.status === 'completed') {
            // 训练完成
            setCurrentStep(3); // 进入结果分析步骤
            setLoading(false);
            message.success('训练完成！');
          } else if (expData.status === 'failed') {
            setLoading(false);
            throw new Error('训练失败');
          } else {
            // 继续轮询
            setTimeout(checkStatus, 2000);
          }
        } catch (error) {
          console.error('检查状态失败:', error);
          setTimeout(checkStatus, 2000);
        }
      };

      // 开始轮询
      setTimeout(checkStatus, 2000);

    } catch (error) {
      message.error('训练启动失败: ' + (error instanceof Error ? error.message : String(error)));
      setLoading(false);
    }
  };

  const getTrainingChart = () => {
    if (!trainingResults?.training_history) return null;

    // 处理训练历史数据格式
    const lossData = trainingResults.training_history.loss || {};
    const aucData = trainingResults.training_history.auc || {};

    // 获取epoch列表并排序
    const epochs = Object.keys(lossData).sort((a, b) => parseInt(a) - parseInt(b));

    // 转换为数组格式
    const lossValues = epochs.map(epoch => lossData[epoch]);
    const aucValues = epochs.map(epoch => aucData[epoch]);
    const epochLabels = epochs.map(epoch => `Epoch ${epoch}`);

    const option = {
      title: {
        text: '训练过程监控',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['Loss', 'AUC'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: epochLabels
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'Loss',
          type: 'line',
          data: lossValues,
          smooth: true,
          itemStyle: { color: '#ff4d4f' }
        },
        {
          name: 'AUC',
          type: 'line',
          data: aucValues,
          smooth: true,
          itemStyle: { color: '#52c41a' }
        }
      ]
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  const steps = [
    {
      title: '选择数据集',
      description: '选择要使用的数据集',
      icon: <DatabaseOutlined />
    },
    {
      title: '数据预处理',
      description: '清洗数据并构建Q-Matrix',
      icon: <SettingOutlined />
    },
    {
      title: '模型训练',
      description: '配置并训练认知诊断模型',
      icon: <RobotOutlined />
    },
    {
      title: '结果分析',
      description: '查看训练结果和认知诊断',
      icon: <BarChartOutlined />
    }
  ];

  // 数据加载中的状态
  if (dataLoading) {
    return (
      <div style={{ padding: '24px', background: '#ffffff', minHeight: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ textAlign: 'center' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px', fontSize: '16px', color: '#666' }}>
            正在加载系统数据...
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div style={{ padding: '24px', background: '#ffffff', minHeight: '100vh' }}>
        <Alert
          message="系统加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadInitialData}>
              重新加载
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* 页面标题和Logo */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        background: 'white',
        padding: '16px 24px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <Logo size="large" />
          <div style={{ flex: 1 }}>
            <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
              双重协同网络认知诊断与规划系统
            </Title>
            <Paragraph style={{ margin: '4px 0 0 0', color: '#666' }}>
              智能教育认知诊断与学习规划平台 - 集成数据处理、模型训练、认知诊断和学习推荐于一体
            </Paragraph>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetState}
              size="large"
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<BarChartOutlined />}
              onClick={() => navigate('/dashboard/overview')}
              size="large"
            >
              数据概览
            </Button>
          </Space>
        </div>
      </div>

      {/* 系统概览统计 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总实验数"
              value={systemStats.totalExperiments}
              prefix={<ExperimentOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中实验"
              value={systemStats.runningExperiments}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成实验"
              value={systemStats.completedExperiments}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="可用数据集"
              value={systemStats.availableDatasets}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 流程步骤 */}
      <Card style={{ marginBottom: '24px' }}>
        <Steps current={currentStep} items={steps} />
      </Card>

      {/* 主要功能标签页 */}
      <Card style={{ marginBottom: '24px' }}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          style={{ minHeight: '600px' }}
        >
        {/* 数据集选择 */}
        <Tabs.TabPane tab="📊 数据集选择" key="1">
          <Row gutter={[16, 16]}>
            {(datasets || []).map((dataset) => (
              <Col xs={24} lg={12} key={dataset.id}>
                <Card
                  hoverable
                  className={selectedDataset?.id === dataset.id ? 'selected-card' : ''}
                  onClick={() => handleDatasetSelect(dataset.id)}
                  actions={[
                    <Statistic
                      title="学生数"
                      value={dataset.studentCount}
                      prefix={<UserOutlined />}
                      valueStyle={{ fontSize: '14px' }}
                    />,
                    <Statistic
                      title="题目数"
                      value={dataset.itemCount}
                      prefix={<FileTextOutlined />}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  ]}
                >
                  <Card.Meta
                    avatar={<DatabaseOutlined style={{ fontSize: '32px', color: '#1890ff' }} />}
                    title={
                      <div>
                        {dataset.displayName}
                        {dataset.isMainDataset && <Tag color="gold" style={{ marginLeft: 8 }}>主数据集</Tag>}
                        <Tag color={dataset.status === 'available' ? 'green' : 'blue'}>
                          {dataset.status === 'available' ? '可训练' : '演示'}
                        </Tag>
                      </div>
                    }
                    description={dataset.description}
                  />
                  <div style={{ marginTop: '16px' }}>
                    {(dataset.features || []).map((feature, index) => (
                      <Tag key={index} color="blue" style={{ marginBottom: '4px' }}>
                        {feature}
                      </Tag>
                    ))}
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Tabs.TabPane>

        {/* 数据预处理 */}
        <Tabs.TabPane tab="🔧 数据预处理" key="2" disabled={!selectedDataset?.isMainDataset}>
          {selectedDataset?.isMainDataset ? (
            <Card>
              <Title level={4}>数据预处理 - {selectedDataset.displayName}</Title>
              <Paragraph>
                将原始数据转换为适合ORCDF模型训练的格式，包括数据清洗、Q-Matrix构建和Response Matrix生成。
              </Paragraph>
              
              <Alert
                message="数据预处理说明"
                description="系统将自动完成数据加载、清洗、Q-Matrix构建、Response Matrix生成和数据验证等步骤。"
                type="info"
                style={{ marginBottom: '16px' }}
              />

              <Button
                type="primary"
                size="large"
                icon={<PlayCircleOutlined />}
                onClick={handlePreprocessing}
                loading={loading}
              >
                开始数据预处理
              </Button>
            </Card>
          ) : (
            <Alert message="请先选择主数据集以进行数据预处理" type="warning" />
          )}
        </Tabs.TabPane>

        {/* 模型训练 */}
        <Tabs.TabPane tab="🤖 模型训练" key="3" disabled={currentStep < 2}>
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card title="模型配置">
                <Form layout="vertical">
                  <Form.Item label="选择模型">
                    <Select
                      value={trainingConfig.model_type}
                      onChange={(value) => setTrainingConfig({...trainingConfig, model_type: value})}
                    >
                      {Array.isArray(availableModels) ? availableModels.map((model) => (
                        <Option key={model.id} value={model.id}>
                          <div>
                            <strong>{model.display_name || model.name}</strong>
                            {model.isRecommended && <Tag color="gold" style={{ marginLeft: 8 }}>推荐</Tag>}
                            <br />
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {model.description}
                            </Text>
                          </div>
                        </Option>
                      )) : null}
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="学习率">
                        <InputNumber
                          value={trainingConfig.learning_rate}
                          onChange={(value) => setTrainingConfig({...trainingConfig, learning_rate: value || 0.001})}
                          min={0.0001}
                          max={0.1}
                          step={0.001}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="批次大小">
                        <InputNumber
                          value={trainingConfig.batch_size}
                          onChange={(value) => setTrainingConfig({...trainingConfig, batch_size: value || 32})}
                          min={16}
                          max={256}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="训练轮数">
                        <InputNumber
                          value={trainingConfig.epochs}
                          onChange={(value) => setTrainingConfig({...trainingConfig, epochs: value || 50})}
                          min={10}
                          max={200}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="隐藏层维度">
                        <InputNumber
                          value={trainingConfig.hidden_dim}
                          onChange={(value) => setTrainingConfig({...trainingConfig, hidden_dim: value || 512})}
                          min={128}
                          max={1024}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Button
                    type="primary"
                    size="large"
                    icon={<PlayCircleOutlined />}
                    onClick={handleTraining}
                    loading={loading}
                    block
                  >
                    开始训练
                  </Button>
                </Form>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="训练监控">
                {trainingResults ? (
                  <div>
                    {loading && (
                      <Card style={{ marginBottom: '16px', background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>
                        <div style={{ textAlign: 'center', color: 'white' }}>
                          <Spin size="small" style={{ color: 'white' }} />
                          <Text style={{ marginLeft: '8px', color: 'white', fontSize: '16px' }}>
                            训练进行中... 进度: {(trainingResults.progress || 0).toFixed(2)}%
                          </Text>
                          <div style={{ marginTop: '12px' }}>
                            <div style={{
                              width: '100%',
                              height: '8px',
                              backgroundColor: 'rgba(255,255,255,0.3)',
                              borderRadius: '4px',
                              overflow: 'hidden'
                            }}>
                              <div style={{
                                width: `${trainingResults.progress || 0}%`,
                                height: '100%',
                                backgroundColor: '#52c41a',
                                transition: 'width 0.3s ease',
                                borderRadius: '4px'
                              }} />
                            </div>
                          </div>
                          {trainingResults.current_epoch && (
                            <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px', marginTop: '8px', display: 'block' }}>
                              当前轮次: {trainingResults.current_epoch} / {trainingConfig.epochs}
                            </Text>
                          )}
                        </div>
                      </Card>
                    )}
                    {getTrainingChart()}
                  </div>
                ) : loading ? (
                  <div style={{ textAlign: 'center', padding: '50px' }}>
                    <Spin size="large" />
                    <div style={{ marginTop: '16px' }}>
                      <Text>正在启动训练...</Text>
                    </div>
                  </div>
                ) : (
                  <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                    点击"开始训练"查看训练过程
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </Tabs.TabPane>

        {/* 结果分析 */}
        <Tabs.TabPane tab="📈 结果分析" key="4" disabled={currentStep < 3}>
          {trainingResults ? (
            <div>
              <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
                <Col xs={24} lg={8}>
                  <Card title="训练指标">
                    <Statistic
                      title="最终准确率"
                      value={trainingResults.final_metrics?.accuracy}
                      precision={2}
                      suffix="%"
                      valueStyle={{ color: '#3f8600' }}
                    />
                    <Divider />
                    <Statistic
                      title="AUC"
                      value={trainingResults.final_metrics?.auc}
                      precision={3}
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <Divider />
                    <Statistic
                      title="最终Loss"
                      value={trainingResults.final_metrics?.loss}
                      precision={3}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} lg={16}>
                  <Card title="训练历史">
                    {getTrainingChart()}
                  </Card>
                </Col>
              </Row>

              {/* 过度平滑分析 */}
              {trainingResults.oversmoothing_analysis && (
                <Row gutter={[24, 24]}>
                  <Col xs={24}>
                    <OversmoothingChart
                      data={trainingResults.oversmoothing_analysis}
                      title="过度平滑改善效果分析"
                    />
                  </Col>
                </Row>
              )}
            </div>
          ) : selectedDataset && !selectedDataset.isMainDataset ? (
            <DemoResults
              datasetId={selectedDataset.id}
              datasetName={selectedDataset.displayName}
            />
          ) : (
            <Alert message="请先完成模型训练以查看结果分析" type="info" />
          )}
        </Tabs.TabPane>



        {/* Embedding展示 */}
        <Tabs.TabPane tab="⚡ Embedding展示" key="6">
          <EmbeddingShowcase />
        </Tabs.TabPane>

        {/* 系统监控 */}
        <Tabs.TabPane tab="📊 系统监控" key="7">
          <SystemMonitor refreshInterval={30000} />
        </Tabs.TabPane>

        {/* 实验历史 */}
        <Tabs.TabPane tab="📋 实验历史" key="8">
          <ExperimentHistory
            onExperimentSelect={(experiment) => {
              console.log('选择实验:', experiment);
              // 可以在这里处理实验选择逻辑
            }}
            refreshTrigger={systemStats.totalExperiments}
          />
        </Tabs.TabPane>
      </Tabs>
      </Card>

      <style>{`
        .selected-card {
          border-color: #1890ff !important;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15) !important;
        }

        .selected-card .ant-card-body {
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }

        /* 标签栏样式优化 */
        .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab {
          padding: 12px 20px;
          font-size: 16px;
          font-weight: 500;
          border-radius: 8px 8px 0 0;
          margin-right: 4px;
          transition: all 0.3s ease;
        }

        .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab:hover {
          background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
          transform: translateY(-2px);
        }

        .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab-active {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white !important;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab-active:hover {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          transform: translateY(-2px);
        }

        .ant-tabs-content-holder {
          padding: 24px;
          background: #fafafa;
          border-radius: 0 0 8px 8px;
        }
      `}</style>
    </div>
  );
};

export default EduBrainDashboard;
