import React from 'react';
import ReactECharts from 'echarts-for-react';
import { Card, Row, Col, Statistic, Alert } from 'antd';

interface OversmoothingChartProps {
  data: {
    mndBefore: number;
    mndAfter: number;
    improvementRatio: number;
    modelType: string;
  };
  title?: string;
  height?: string;
}

const OversmoothingChart: React.FC<OversmoothingChartProps> = ({
  data,
  title = '过度平滑改善效果',
  height = '300px',
}) => {
  const option = {
    title: {
      text: title,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}: ${param.value.toFixed(6)}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['训练前', '训练后'],
      name: '训练阶段',
      nameLocation: 'middle',
      nameGap: 30,
    },
    yAxis: {
      type: 'value',
      name: 'MND值',
      axisLabel: {
        formatter: '{value}',
      },
    },
    series: [
      {
        name: 'MND值',
        type: 'bar',
        data: [data.mndBefore, data.mndAfter],
        itemStyle: {
          color: (params: any) => {
            const colors = ['#ff7f0e', '#2ca02c'];
            return colors[params.dataIndex];
          },
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  const getImprovementColor = (ratio: number) => {
    if (ratio >= 20) return '#52c41a'; // 绿色 - 显著改善
    if (ratio >= 10) return '#faad14'; // 黄色 - 中等改善
    return '#ff4d4f'; // 红色 - 轻微改善
  };

  const getImprovementLevel = (ratio: number) => {
    if (ratio >= 20) return '显著改善';
    if (ratio >= 10) return '中等改善';
    if (ratio >= 5) return '轻微改善';
    return '改善有限';
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="训练前MND值"
              value={data.mndBefore}
              precision={6}
              valueStyle={{ color: '#ff7f0e' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="训练后MND值"
              value={data.mndAfter}
              precision={6}
              valueStyle={{ color: '#2ca02c' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="改善倍数"
              value={data.improvementRatio}
              suffix="倍"
              precision={1}
              valueStyle={{ color: getImprovementColor(data.improvementRatio) }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="改善程度"
              value={getImprovementLevel(data.improvementRatio)}
              valueStyle={{ color: getImprovementColor(data.improvementRatio) }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="MND值对比图表" style={{ marginBottom: 16 }}>
        <ReactECharts option={option} style={{ height }} />
      </Card>

      <Alert
        message="分析说明"
        description={
          <div>
            <p>
              <strong>MND (Mean Normalized Difference)</strong> 是衡量学生能力区分度的重要指标：
            </p>
            <ul>
              <li>MND值越大，表示学生之间的能力差异越明显，过度平滑问题越轻</li>
              <li>MND值越小，表示学生能力过于相似，存在严重的过度平滑问题</li>
              <li>
                使用 <strong>{data.modelType.toUpperCase()}</strong> 模型后，
                学生能力区分度提升了 <strong>{data.improvementRatio.toFixed(1)}倍</strong>
              </li>
            </ul>
          </div>
        }
        type="info"
        showIcon
      />
    </div>
  );
};

export default OversmoothingChart;
