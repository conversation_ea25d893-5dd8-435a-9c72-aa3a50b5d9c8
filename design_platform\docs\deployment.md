# ORCDF设计平台部署指南

## 系统要求

### 硬件要求
- **CPU**: 4核心以上
- **内存**: 8GB以上 (推荐16GB)
- **存储**: 50GB以上可用空间
- **GPU**: 可选，用于加速模型训练 (推荐NVIDIA GPU with CUDA支持)

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+), macOS, Windows 10+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd design_platform
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

### 3. 数据准备
```bash
# 创建数据目录
mkdir -p data/datasets data/models data/results

# 复制ORCDF数据集 (如果可用)
cp -r ../datasets/* data/datasets/
```

### 4. 启动服务
```bash
# 开发环境
docker-compose up -d

# 生产环境
docker-compose --profile production up -d
```

### 5. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:8000
# API文档: http://localhost:8000/docs
```

## 详细配置

### 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# 数据库配置
DATABASE_URL=****************************************************/orcdf_platform

# Redis配置
REDIS_URL=redis://redis:6379/0

# Celery配置
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# JWT配置
SECRET_KEY=your-super-secret-key-change-this-in-production

# CORS配置
BACKEND_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 文件存储
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600  # 100MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 前端配置
REACT_APP_API_URL=http://localhost:8000
```

### 数据库初始化

```bash
# 进入后端容器
docker-compose exec backend bash

# 运行数据库迁移
python -c "
from app.core.database import engine, Base
Base.metadata.create_all(bind=engine)
print('数据库初始化完成')
"

# 同步数据集
python -c "
from app.services.dataset_service import DatasetService
from app.core.database import SessionLocal
db = SessionLocal()
service = DatasetService(db)
count = service.sync_datasets_from_filesystem()
print(f'同步了 {count} 个数据集')
db.close()
"
```

### SSL证书配置 (生产环境)

```bash
# 创建SSL证书目录
mkdir -p docker/nginx/ssl

# 生成自签名证书 (仅用于测试)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout docker/nginx/ssl/key.pem \
  -out docker/nginx/ssl/cert.pem \
  -subj "/C=CN/ST=Shanghai/L=Shanghai/O=ORCDF/CN=localhost"

# 或者使用Let's Encrypt证书 (生产环境推荐)
# certbot certonly --webroot -w /var/www/html -d your-domain.com
```

## 服务管理

### 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d backend frontend

# 生产环境启动
docker-compose --profile production up -d
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend

# 查看最近100行日志
docker-compose logs --tail=100 backend
```

### 扩容服务
```bash
# 扩容Celery Worker
docker-compose up -d --scale celery_worker=3

# 扩容后端服务
docker-compose up -d --scale backend=2
```

## 监控与维护

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8000/health

# 检查前端服务
curl http://localhost:3000

# 检查数据库连接
docker-compose exec postgres pg_isready -U orcdf_user
```

### 备份与恢复

#### 数据库备份
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U orcdf_user orcdf_platform > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U orcdf_user orcdf_platform < backup.sql
```

#### 文件备份
```bash
# 备份上传文件和模型
tar -czf data_backup.tar.gz data/

# 恢复文件
tar -xzf data_backup.tar.gz
```

### 日志管理
```bash
# 清理Docker日志
docker system prune -f

# 设置日志轮转
# 在docker-compose.yml中添加logging配置
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

### 性能优化

#### 数据库优化
```sql
-- 创建索引
CREATE INDEX idx_experiments_status ON experiments(status);
CREATE INDEX idx_experiments_created_at ON experiments(created_at);
CREATE INDEX idx_datasets_name ON datasets(name);

-- 分析表统计信息
ANALYZE experiments;
ANALYZE datasets;
```

#### Redis优化
```bash
# 在redis.conf中设置
maxmemory 2gb
maxmemory-policy allkeys-lru
```

## 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# 修改docker-compose.yml中的端口映射
ports:
  - "3001:3000"  # 前端
  - "8001:8000"  # 后端
```

#### 2. 数据库连接失败
```bash
# 检查数据库服务状态
docker-compose ps postgres

# 查看数据库日志
docker-compose logs postgres

# 重启数据库服务
docker-compose restart postgres
```

#### 3. 内存不足
```bash
# 检查内存使用
docker stats

# 限制容器内存使用
# 在docker-compose.yml中添加
deploy:
  resources:
    limits:
      memory: 2G
```

#### 4. 磁盘空间不足
```bash
# 清理Docker镜像和容器
docker system prune -a

# 清理日志文件
find /var/lib/docker/containers/ -name "*.log" -exec truncate -s 0 {} \;
```

### 调试模式

#### 启用调试模式
```bash
# 设置环境变量
export DEBUG=true
export LOG_LEVEL=DEBUG

# 重启服务
docker-compose restart backend
```

#### 进入容器调试
```bash
# 进入后端容器
docker-compose exec backend bash

# 进入前端容器
docker-compose exec frontend sh

# 进入数据库容器
docker-compose exec postgres psql -U orcdf_user orcdf_platform
```

## 安全配置

### 防火墙设置
```bash
# Ubuntu/Debian
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 5432/tcp  # 禁止外部访问数据库
ufw deny 6379/tcp  # 禁止外部访问Redis

# CentOS/RHEL
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --reload
```

### 定期更新
```bash
# 更新Docker镜像
docker-compose pull

# 重新构建并启动
docker-compose up -d --build

# 更新系统包
apt update && apt upgrade -y  # Ubuntu/Debian
yum update -y                 # CentOS/RHEL
```

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查GitHub Issues页面
3. 联系技术支持团队

---

**注意**: 在生产环境中，请确保：
- 使用强密码和安全的SECRET_KEY
- 启用HTTPS和SSL证书
- 定期备份数据
- 监控系统资源使用情况
- 及时更新安全补丁
