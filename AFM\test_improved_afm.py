#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的AFM智能体
"""

import numpy as np
from optimized_simulator import RealAFMAgent, ImprovedAFMAgent, OptimizedLearningEnvironment as LearningEnvironment

def test_decision_differences():
    """测试改进前后的决策差异"""
    print("=== 测试改进前后的决策差异 ===")
    
    # 创建环境
    env = LearningEnvironment()
    
    # 创建不同的智能体
    original_agent = RealAFMAgent(env.action_space_size, user_id=0)
    zpd_agent = ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="zpd")
    adaptive_agent = ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="adaptive")
    multi_agent = ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="multi_factor")
    
    # 测试不同的学习状态
    test_states = [
        np.array([0.1, 0.2, 0.3, 0.4, 0.5]),  # 递增状态
        np.array([0.5, 0.4, 0.3, 0.2, 0.1]),  # 递减状态
        np.array([0.9, 0.1, 0.9, 0.1, 0.9]),  # 交替状态
        np.array([0.2, 0.2, 0.2, 0.2, 0.2]),  # 均匀低状态
        np.array([0.8, 0.8, 0.8, 0.8, 0.8]),  # 均匀高状态
    ]
    
    state_names = ["递增", "递减", "交替", "均匀低", "均匀高"]
    
    for i, (state, name) in enumerate(zip(test_states, state_names)):
        print(f"\n📊 测试状态{i+1} ({name}): {state}")
        
        # 获取AFM预测
        afm_predictions = []
        for j in range(5):
            pred = original_agent.predict_learning_effect(j)
            afm_predictions.append(pred)
        print(f"AFM预测: {[f'{p:.3f}' for p in afm_predictions]}")
        
        # 获取各智能体的决策
        original_action = original_agent.get_action(state)
        zpd_action = zpd_agent.get_action(state)
        adaptive_action = adaptive_agent.get_action(state)
        multi_action = multi_agent.get_action(state)
        
        print(f"决策对比:")
        print(f"  原始AFM: 选择动作{original_action}")
        print(f"  ZPD策略: 选择动作{zpd_action}")
        print(f"  自适应: 选择动作{adaptive_action}")
        print(f"  多因素: 选择动作{multi_action}")
        
        # 分析决策差异
        actions = [original_action, zpd_action, adaptive_action, multi_action]
        if len(set(actions)) == 1:
            print(f"  → 所有策略选择相同")
        else:
            print(f"  → 策略间存在差异，体现了改进效果")

def analyze_learning_value_function():
    """分析学习价值函数的效果"""
    print(f"\n=== 分析学习价值函数 ===")
    
    agent = ImprovedAFMAgent(5, user_id=0, strategy="zpd")
    
    # 测试不同答题概率对应的学习价值
    answer_probs = np.linspace(0, 1, 21)
    learning_values = []
    
    for prob in answer_probs:
        value = agent._calculate_learning_value(prob)
        learning_values.append(value)
    
    print(f"答题概率 → 学习价值映射:")
    for prob, value in zip(answer_probs[::4], learning_values[::4]):
        print(f"  {prob:.2f} → {value:.3f}")
    
    # 找到最大学习价值对应的概率
    max_idx = np.argmax(learning_values)
    optimal_prob = answer_probs[max_idx]
    max_value = learning_values[max_idx]
    
    print(f"\n🎯 最优学习区间:")
    print(f"  答题概率 {optimal_prob:.2f} 时学习价值最高 ({max_value:.3f})")
    print(f"  这符合ZPD理论：适中难度最适合学习")

def compare_recommendation_logic():
    """比较推荐逻辑的差异"""
    print(f"\n=== 比较推荐逻辑差异 ===")
    
    # 模拟5个知识点的答题概率
    answer_probs = [0.95, 0.85, 0.50, 0.30, 0.10]
    knowledge_names = ["基础加法", "简单乘法", "分数运算", "代数方程", "微积分"]
    
    print(f"知识点答题概率:")
    for i, (name, prob) in enumerate(zip(knowledge_names, answer_probs)):
        print(f"  {i}. {name}: {prob:.2f}")
    
    # 原始推荐逻辑（按答题概率排序）
    original_ranking = sorted(enumerate(answer_probs), key=lambda x: x[1], reverse=True)
    
    # 改进推荐逻辑（按学习价值排序）
    agent = ImprovedAFMAgent(5, user_id=0, strategy="zpd")
    learning_values = []
    for prob in answer_probs:
        value = agent._calculate_learning_value(prob)
        learning_values.append(value)
    
    improved_ranking = sorted(enumerate(learning_values), key=lambda x: x[1], reverse=True)
    
    print(f"\n📋 推荐排序对比:")
    print(f"原始逻辑（按答题能力）:")
    for rank, (idx, prob) in enumerate(original_ranking, 1):
        print(f"  {rank}. {knowledge_names[idx]} (答题概率: {prob:.2f})")
    
    print(f"\n改进逻辑（按学习价值）:")
    for rank, (idx, value) in enumerate(improved_ranking, 1):
        print(f"  {rank}. {knowledge_names[idx]} (学习价值: {value:.3f}, 答题概率: {answer_probs[idx]:.2f})")
    
    print(f"\n🎯 关键差异:")
    print(f"  原始逻辑优先推荐已掌握的内容（基础加法、简单乘法）")
    print(f"  改进逻辑优先推荐适中难度的内容（分数运算）")

def run_mini_simulation():
    """运行小规模仿真对比"""
    print(f"\n=== 小规模仿真对比 ===")
    
    env = LearningEnvironment()
    
    agents = {
        '原始AFM': RealAFMAgent(env.action_space_size, user_id=0),
        'ZPD策略': ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="zpd"),
        '多因素策略': ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="multi_factor"),
    }
    
    results = {}
    num_episodes = 10
    
    for agent_name, agent in agents.items():
        episode_scores = []
        
        for episode in range(num_episodes):
            observation = env.reset()
            agent.reset()
            total_reward = 0
            
            for step in range(15):  # 限制步数
                action = agent.get_action(observation)
                observation, reward, done, info = env.step(action)
                total_reward += reward
                
                if done:
                    break
            
            final_score = np.mean(observation)
            episode_scores.append(final_score)
        
        results[agent_name] = {
            'mean_score': np.mean(episode_scores),
            'std_score': np.std(episode_scores),
            'scores': episode_scores
        }
    
    print(f"仿真结果 ({num_episodes}回合):")
    for agent_name, result in results.items():
        print(f"  {agent_name}: {result['mean_score']:.4f} ± {result['std_score']:.4f}")
    
    # 找出最佳策略
    best_agent = max(results.keys(), key=lambda x: results[x]['mean_score'])
    print(f"\n🏆 最佳策略: {best_agent}")

def main():
    """主测试函数"""
    print("🔍 测试改进后的AFM智能体")
    print("=" * 60)
    
    # 测试决策差异
    test_decision_differences()
    
    # 分析学习价值函数
    analyze_learning_value_function()
    
    # 比较推荐逻辑
    compare_recommendation_logic()
    
    # 运行小规模仿真
    run_mini_simulation()
    
    print(f"\n" + "=" * 60)
    print(f"🎯 改进总结:")
    print(f"1. 修正了AFM分数的使用逻辑：从'答题能力'转为'学习价值'")
    print(f"2. 基于ZPD理论设计学习价值函数：适中难度价值最高")
    print(f"3. 提供多种改进策略：ZPD、自适应、多因素")
    print(f"4. 改进后的智能体更符合教育学原理")

if __name__ == "__main__":
    main()
