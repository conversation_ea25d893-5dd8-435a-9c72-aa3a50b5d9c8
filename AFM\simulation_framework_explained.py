#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体仿真框架详解：原理、实现与评分机制
不涉及具体的AFM模型，专注于仿真框架本身
"""

import numpy as np
import random
from typing import Dict, List, Tuple
import json
from datetime import datetime

class LearningEnvironment:
    """
    学习环境仿真器
    
    核心概念：
    1. 状态空间：用户对各知识点的掌握程度 [0,1]
    2. 动作空间：选择学习哪个知识点
    3. 奖励函数：基于学习效果的即时奖励
    4. 状态转移：学习后掌握程度的变化
    """
    
    def __init__(self, 
                 num_knowledge_points: int = 5,
                 learning_rate: float = 0.3,
                 forgetting_rate: float = 0.02,
                 success_threshold: float = 0.6,
                 min_success_kps: int = 3,
                 max_steps: int = 30):
        """
        初始化学习环境
        
        参数说明：
        - num_knowledge_points: 知识点数量
        - learning_rate: 学习率，控制每次学习的提升幅度
        - forgetting_rate: 遗忘率，控制未学习知识点的衰减
        - success_threshold: 成功阈值，知识点掌握度超过此值算成功
        - min_success_kps: 最少成功知识点数，达到此数量算完成任务
        - max_steps: 最大学习步数
        """
        self.num_kps = num_knowledge_points
        self.action_space_size = num_knowledge_points
        self.learning_rate = learning_rate
        self.forgetting_rate = forgetting_rate
        self.success_threshold = success_threshold
        self.min_success_kps = min_success_kps
        self.max_steps = max_steps
        
        # 知识点难度：影响学习效果
        self.difficulty = np.random.uniform(0.2, 0.8, num_knowledge_points)
        
        # 依赖关系矩阵：某些知识点需要先掌握其他知识点
        self.dependency_matrix = self._generate_dependency_matrix()
        
        # 当前状态
        self.current_state = None
        self.step_count = 0
        
        print(f"🏫 学习环境初始化完成")
        print(f"  知识点数量: {self.num_kps}")
        print(f"  知识点难度: {[f'{d:.3f}' for d in self.difficulty]}")
        print(f"  成功标准: {self.min_success_kps}个知识点达到{self.success_threshold}")
        print(f"  学习参数: 学习率={self.learning_rate}, 遗忘率={self.forgetting_rate}")
    
    def _generate_dependency_matrix(self) -> np.ndarray:
        """
        生成知识点依赖关系矩阵
        
        dependency_matrix[i][j] = 学习知识点i对掌握知识点j的依赖程度
        值越大表示依赖越强，0表示无依赖
        """
        matrix = np.zeros((self.num_kps, self.num_kps))
        
        # 简单的线性依赖：后面的知识点依赖前面的
        for i in range(1, self.num_kps):
            for j in range(i):
                # 距离越近依赖越强
                dependency_strength = 0.3 * np.exp(-(i-j-1) * 0.5)
                matrix[i][j] = dependency_strength
        
        return matrix
    
    def reset(self) -> np.ndarray:
        """
        重置环境，返回初始状态
        
        返回：
        - observation: 初始掌握程度向量 [0,1]^n
        """
        # 初始掌握程度：随机但偏低
        self.current_state = np.random.uniform(0.1, 0.4, self.num_kps)
        self.step_count = 0
        
        return self.current_state.copy()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """
        执行一个学习动作
        
        参数：
        - action: 选择学习的知识点索引
        
        返回：
        - observation: 新的掌握程度向量
        - reward: 即时奖励
        - done: 是否完成任务
        - info: 额外信息
        """
        if action < 0 or action >= self.num_kps:
            raise ValueError(f"无效动作: {action}")
        
        # 1. 计算学习效果
        learning_effect = self._calculate_learning_effect(action)
        
        # 2. 更新状态
        old_state = self.current_state.copy()
        self._update_state(action, learning_effect)
        
        # 3. 计算奖励
        reward = self._calculate_reward(action, old_state, self.current_state)
        
        # 4. 检查是否完成
        done = self._check_completion()
        
        # 5. 更新步数
        self.step_count += 1
        if self.step_count >= self.max_steps:
            done = True
        
        # 6. 准备信息
        info = {
            'step': self.step_count,
            'action': action,
            'learning_effect': learning_effect,
            'success_kps': self._count_success_kps(),
            'completion_rate': self._get_completion_rate()
        }
        
        return self.current_state.copy(), reward, done, info
    
    def _calculate_learning_effect(self, action: int) -> float:
        """
        计算学习效果
        
        学习效果受以下因素影响：
        1. 知识点难度：越难学习效果越低
        2. 当前掌握程度：掌握程度越高，边际效应递减
        3. 依赖关系：依赖的知识点掌握不足会降低学习效果
        """
        # 基础学习效果
        base_effect = self.learning_rate
        
        # 难度调整：难度越高，学习效果越低
        difficulty_factor = 1.0 - self.difficulty[action] * 0.5
        
        # 当前掌握程度调整：边际效应递减
        current_mastery = self.current_state[action]
        mastery_factor = 1.0 - current_mastery * 0.7
        
        # 依赖关系调整
        dependency_factor = self._calculate_dependency_factor(action)
        
        # 综合学习效果
        learning_effect = base_effect * difficulty_factor * mastery_factor * dependency_factor
        
        # 添加随机性
        noise = np.random.normal(0, 0.05)
        learning_effect = max(0, learning_effect + noise)
        
        return learning_effect
    
    def _calculate_dependency_factor(self, action: int) -> float:
        """
        计算依赖因子
        
        如果依赖的知识点掌握不足，会降低学习效果
        """
        dependencies = self.dependency_matrix[action]
        dependency_factor = 1.0
        
        for i, dep_strength in enumerate(dependencies):
            if dep_strength > 0:
                # 依赖知识点的掌握程度
                prerequisite_mastery = self.current_state[i]
                # 如果依赖知识点掌握不足，降低学习效果
                if prerequisite_mastery < 0.5:
                    penalty = dep_strength * (0.5 - prerequisite_mastery)
                    dependency_factor -= penalty
        
        return max(0.1, dependency_factor)  # 最低保留10%的学习效果
    
    def _update_state(self, action: int, learning_effect: float):
        """
        更新掌握程度状态
        
        1. 学习的知识点：掌握程度提升
        2. 其他知识点：由于遗忘，掌握程度略微下降
        """
        # 学习的知识点：掌握程度提升
        self.current_state[action] = min(1.0, 
            self.current_state[action] + learning_effect)
        
        # 其他知识点：遗忘效应
        for i in range(self.num_kps):
            if i != action:
                forgetting = self.forgetting_rate * self.current_state[i]
                self.current_state[i] = max(0.0, 
                    self.current_state[i] - forgetting)
    
    def _calculate_reward(self, action: int, old_state: np.ndarray, 
                         new_state: np.ndarray) -> float:
        """
        计算即时奖励
        
        奖励设计原则：
        1. 基础奖励：掌握程度的提升
        2. 成就奖励：达到成功阈值的知识点
        3. 效率奖励：鼓励高效学习
        4. 完成奖励：完成整体任务
        """
        reward = 0.0
        
        # 1. 基础奖励：掌握程度提升
        improvement = new_state[action] - old_state[action]
        reward += improvement * 2.0  # 放大提升的价值
        
        # 2. 成就奖励：知识点达到成功阈值
        if (old_state[action] < self.success_threshold and 
            new_state[action] >= self.success_threshold):
            reward += 1.0  # 突破阈值奖励
        
        # 3. 效率奖励：基于当前整体进度
        overall_progress = np.mean(new_state)
        reward += overall_progress * 0.5
        
        # 4. 完成奖励：达成最终目标
        if self._check_completion():
            reward += 5.0  # 完成任务大奖励
        
        # 5. 惩罚：过度学习已掌握的知识点
        if old_state[action] > 0.8:
            reward -= 0.2  # 轻微惩罚
        
        return reward
    
    def _check_completion(self) -> bool:
        """检查是否完成学习任务"""
        success_count = self._count_success_kps()
        return success_count >= self.min_success_kps
    
    def _count_success_kps(self) -> int:
        """统计成功掌握的知识点数量"""
        return np.sum(self.current_state >= self.success_threshold)
    
    def _get_completion_rate(self) -> float:
        """获取任务完成率"""
        success_count = self._count_success_kps()
        return success_count / self.min_success_kps
    
    def get_state_info(self) -> Dict:
        """获取当前状态的详细信息"""
        return {
            'current_state': self.current_state.tolist(),
            'step_count': self.step_count,
            'success_kps': self._count_success_kps(),
            'completion_rate': self._get_completion_rate(),
            'is_completed': self._check_completion()
        }

class BaseAgent:
    """智能体基类"""
    
    def __init__(self, action_space_size: int):
        self.action_space_size = action_space_size
    
    def reset(self):
        """重置智能体状态"""
        pass
    
    def get_action(self, observation: np.ndarray) -> int:
        """根据观察选择动作"""
        raise NotImplementedError
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """更新智能体（用于学习型智能体）"""
        pass

class RandomAgent(BaseAgent):
    """随机智能体：随机选择动作"""
    
    def get_action(self, observation: np.ndarray) -> int:
        return random.randint(0, self.action_space_size - 1)

class GreedyAgent(BaseAgent):
    """贪心智能体：总是选择掌握程度最低的知识点"""
    
    def get_action(self, observation: np.ndarray) -> int:
        return np.argmin(observation)

class SmartGreedyAgent(BaseAgent):
    """智能贪心智能体：考虑依赖关系的贪心策略"""
    
    def __init__(self, action_space_size: int, dependency_matrix: np.ndarray):
        super().__init__(action_space_size)
        self.dependency_matrix = dependency_matrix
    
    def get_action(self, observation: np.ndarray) -> int:
        scores = []
        
        for action in range(self.action_space_size):
            # 基础需求：1 - 当前掌握程度
            need = 1.0 - observation[action]
            
            # 依赖满足度
            dependencies = self.dependency_matrix[action]
            dependency_satisfaction = 1.0
            
            for i, dep_strength in enumerate(dependencies):
                if dep_strength > 0:
                    if observation[i] < 0.5:  # 依赖未满足
                        dependency_satisfaction *= (1.0 - dep_strength)
            
            # 综合得分
            score = need * dependency_satisfaction
            scores.append(score)
        
        return np.argmax(scores)

def evaluate_agent(agent: BaseAgent, env: LearningEnvironment, 
                  num_episodes: int = 50) -> Dict:
    """
    评估智能体性能
    
    评估指标：
    1. 平均奖励：反映学习效率
    2. 平均最终得分：反映最终掌握程度
    3. 成功率：完成任务的比例
    4. 平均步数：反映学习速度
    """
    total_rewards = []
    final_scores = []
    success_count = 0
    step_counts = []
    
    for episode in range(num_episodes):
        observation = env.reset()
        agent.reset()
        
        total_reward = 0
        step_count = 0
        
        while True:
            action = agent.get_action(observation)
            next_observation, reward, done, info = env.step(action)
            
            agent.update(observation, action, reward, next_observation, done)
            
            total_reward += reward
            step_count += 1
            observation = next_observation
            
            if done:
                break
        
        total_rewards.append(total_reward)
        final_scores.append(np.mean(observation))
        step_counts.append(step_count)
        
        if env._check_completion():
            success_count += 1
    
    return {
        'avg_reward': np.mean(total_rewards),
        'avg_final_score': np.mean(final_scores),
        'success_rate': success_count / num_episodes,
        'avg_steps': np.mean(step_counts),
        'std_reward': np.std(total_rewards),
        'std_final_score': np.std(final_scores)
    }

def run_simulation_demo():
    """运行仿真演示"""
    print("🎮 智能体学习仿真演示")
    print("=" * 60)
    
    # 创建环境
    env = LearningEnvironment(
        num_knowledge_points=5,
        learning_rate=0.3,
        forgetting_rate=0.02,
        success_threshold=0.6,
        min_success_kps=3,
        max_steps=30
    )
    
    # 创建智能体
    agents = {
        'Random': RandomAgent(env.action_space_size),
        'Greedy': GreedyAgent(env.action_space_size),
        'Smart_Greedy': SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
    }
    
    # 评估所有智能体
    results = {}
    for name, agent in agents.items():
        print(f"\n评估 {name} 智能体...")
        result = evaluate_agent(agent, env, num_episodes=20)
        results[name] = result
        
        print(f"  平均奖励: {result['avg_reward']:.4f}")
        print(f"  平均最终得分: {result['avg_final_score']:.4f}")
        print(f"  成功率: {result['success_rate']:.2%}")
        print(f"  平均步数: {result['avg_steps']:.1f}")
    
    # 性能排名
    print(f"\n🏆 性能排名（按最终得分）:")
    sorted_results = sorted(results.items(), 
                          key=lambda x: x[1]['avg_final_score'], 
                          reverse=True)
    
    for i, (name, result) in enumerate(sorted_results, 1):
        print(f"  {i}. {name}: {result['avg_final_score']:.4f}")
    
    return results

if __name__ == "__main__":
    results = run_simulation_demo()
