# AFM路径规划系统

基于Attentional Factorization Machines的个性化学习路径推荐系统。

## 📁 文件说明

### 🔧 核心模型文件
- `AFM.py` - AFM模型定义和实现
- `test4.py` - AFM推荐算法和数据处理函数
- `config.py` - 模型配置参数

### 📊 数据文件
- `usr_emb_np.pkl` - 用户嵌入向量 (16818, 1, 102)
- `itm_emb_np.pkl` - 知识点嵌入向量 (16818, 1, 102)
- `trn_mat.pkl` - 训练数据矩阵
- `val_mat.pkl` - 验证数据矩阵
- `tst_mat.pkl` - 测试数据矩阵
- `training.log` - 模型训练日志

### 🎯 演示和仿真
- `afm_explanation_demo.py` - **主要演示脚本**，展示AFM在路径规划中的完整应用
- `optimized_simulator.py` - 优化的学习仿真环境

### 📚 文档
- `AFM_EduSim_Integration_Summary.md` - 技术实现总结和分析报告
- `README.md` - 本文件

### 📁 其他
- `data/` - 原始训练数据样本

## 🚀 快速开始

### 运行AFM路径规划演示
```bash
python afm_explanation_demo.py
```

这个脚本会：
1. 展示AFM的核心组件
2. 创建个性化用户档案
3. 使用AFM模型进行学习路径规划
4. 通过仿真环境验证效果
5. 与基线方法进行对比

### 运行仿真环境测试
```bash
python optimized_simulator.py
```

## 🎯 AFM在路径规划中的体现

### 1. 🧠 个性化用户建模
- 将用户特征（能力、学习速度、薄弱领域）编码为102维嵌入向量
- 每个用户都有独特的特征表示

### 2. 📚 知识点表示学习
- 每个知识点都有对应的102维嵌入向量
- 表示知识点的难度、类型、依赖关系等特征

### 3. 🤖 智能预测模型
- AFM模型预测用户对特定知识点的学习效果
- 考虑用户特征与知识点特征的交互
- 注意力机制突出重要的特征组合

### 4. 🎯 动态路径规划
- 根据AFM预测结果选择最适合的学习内容
- 结合当前掌握程度进行动态调整
- 实现真正的个性化学习路径

### 5. 📈 效果验证
- 通过仿真环境验证AFM指导的有效性
- 与随机策略对比展示改进效果

## 📊 核心算法

### AFM预测过程
1. **线性部分**：一阶特征的线性组合
2. **特征交互**：用户特征与知识点特征的二阶交互
3. **注意力机制**：动态加权重要的特征组合
4. **最终预测**：综合线性部分和交互部分的结果

### 路径规划策略
```python
# 综合AFM预测和当前掌握程度
combined_score = afm_prediction * 0.7 + mastery_need * 0.3
best_action = argmax(combined_score)
```

## 🔍 技术特点

- ✅ **真实AFM模型**：使用完整的AFM架构进行预测
- ✅ **个性化建模**：基于用户嵌入的个性化推荐
- ✅ **动态调整**：根据学习进度实时调整策略
- ✅ **效果验证**：通过仿真环境量化评估效果
- ✅ **可扩展性**：支持更多知识点和用户特征

## 📈 实验结果

通过仿真验证，AFM指导的学习路径相比随机策略有显著改进：
- 个性化程度更高
- 学习效果更好
- 适应性更强

## 🛠️ 依赖环境

- Python 3.7+
- PyTorch
- NumPy
- Pickle

## 📝 使用说明

1. 确保所有数据文件（*.pkl）存在
2. 运行 `afm_explanation_demo.py` 查看完整演示
3. 查看 `AFM_EduSim_Integration_Summary.md` 了解技术细节

## 🎉 项目成果

✅ 成功将AFM集成到学习路径规划中
✅ 实现了基于机器学习的个性化推荐
✅ 通过仿真验证了方法的有效性
✅ 提供了完整的评估和比较框架

---

**注意**：本项目展示了AFM在教育场景中的创新应用，为个性化学习路径规划提供了可行的技术方案。
