# 更新前端API配置脚本
# 使用方法: .\update-api-config.ps1 -BackendUrl "https://your-backend-url.ngrok.io"

param(
    [Parameter(Mandatory=$true)]
    [string]$BackendUrl
)

Write-Host "🔧 更新前端API配置..." -ForegroundColor Green
Write-Host "后端地址: $BackendUrl" -ForegroundColor Yellow

# 验证URL格式
if (-not ($BackendUrl -match "^https?://")) {
    Write-Host "❌ 无效的URL格式，请使用完整的URL (如: https://abc123.ngrok.io)" -ForegroundColor Red
    exit 1
}

# 移除末尾的斜杠
$BackendUrl = $BackendUrl.TrimEnd('/')

# 定义需要更新的文件路径
$configFiles = @(
    "../frontend/src/services/api.ts",
    "../frontend/src/components/EmbeddingShowcase/index.tsx",
    "../frontend/src/pages/Dashboard/TrainingWorkflow.tsx",
    "../frontend/src/pages/Dashboard/EduBrainDashboard.tsx"
)

# 备份原始文件
Write-Host "📁 创建配置文件备份..." -ForegroundColor Blue
$backupDir = "../frontend/config-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        $fileName = Split-Path $file -Leaf
        Copy-Item $file "$backupDir/$fileName" -Force
        Write-Host "✅ 备份: $fileName" -ForegroundColor Green
    }
}

# 更新API配置
Write-Host "🔄 更新API地址..." -ForegroundColor Blue

# 更新 api.ts 文件
$apiFile = "../frontend/src/services/api.ts"
if (Test-Path $apiFile) {
    $content = Get-Content $apiFile -Raw
    $newContent = $content -replace "http://localhost:8000", $BackendUrl
    Set-Content $apiFile $newContent -Encoding UTF8
    Write-Host "✅ 更新: api.ts" -ForegroundColor Green
}

# 更新其他组件文件中的API地址
foreach ($file in $configFiles[1..($configFiles.Length-1)]) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        $newContent = $content -replace "http://localhost:8000", $BackendUrl
        Set-Content $file $newContent -Encoding UTF8
        $fileName = Split-Path $file -Leaf
        Write-Host "✅ 更新: $fileName" -ForegroundColor Green
    }
}

# 创建环境变量文件
Write-Host "📝 创建环境配置文件..." -ForegroundColor Blue
$envContent = @"
# 内网穿透配置
REACT_APP_API_URL=$BackendUrl/api/v1
REACT_APP_BACKEND_URL=$BackendUrl

# 生成时间: $(Get-Date)
# 备份目录: $backupDir
"@

Set-Content "../frontend/.env.local" $envContent -Encoding UTF8
Write-Host "✅ 创建: .env.local" -ForegroundColor Green

# 同时在localStorage中设置后端地址（用于前端动态检测）
Write-Host "📝 配置前端localStorage..." -ForegroundColor Blue
$jsScript = @"
// 自动配置脚本
localStorage.setItem('BACKEND_URL', '$BackendUrl');
console.log('✅ 后端地址已配置:', '$BackendUrl');
"@

Set-Content "../frontend/public/auto-config.js" $jsScript -Encoding UTF8
Write-Host "✅ 创建: auto-config.js" -ForegroundColor Green

Write-Host ""
Write-Host "🎉 配置更新完成!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步操作:" -ForegroundColor Magenta
Write-Host "1. 重启前端服务: npm start" -ForegroundColor White
Write-Host "2. 等待前端重新编译完成" -ForegroundColor White
Write-Host "3. 使用前端的公网地址分享给组员" -ForegroundColor White
Write-Host ""
Write-Host "🔄 恢复配置:" -ForegroundColor Yellow
Write-Host "如需恢复原始配置，请运行:" -ForegroundColor White
Write-Host ".\restore-config.ps1 -BackupDir '$backupDir'" -ForegroundColor Cyan
Write-Host ""
Write-Host "📁 备份位置: $backupDir" -ForegroundColor Gray
