"""
马尔可夫决策过程(MDP)实现

将学习路径规划问题形式化为MDP，提供完整的数学框架。
"""

import numpy as np
from typing import Dict, List, Tuple, Callable, Optional
from dataclasses import dataclass
from .base import BaseCognitiveModel


@dataclass
class MDPState:
    """MDP状态表示"""
    knowledge_mastery: np.ndarray  # 知识掌握程度向量
    time_step: int                 # 时间步
    metadata: Dict                 # 额外元数据
    
    def __hash__(self):
        """使状态可哈希，用于Q表等"""
        # 离散化连续状态以便哈希
        discretized = (self.knowledge_mastery * 10).astype(int)
        return hash((tuple(discretized), self.time_step))
    
    def __eq__(self, other):
        """状态相等性比较"""
        if not isinstance(other, MDPState):
            return False
        return (np.allclose(self.knowledge_mastery, other.knowledge_mastery) and 
                self.time_step == other.time_step)


@dataclass 
class MDPTransition:
    """MDP状态转移"""
    state: MDPState
    action: int
    reward: float
    next_state: MDPState
    done: bool
    info: Dict


class MarkovDecisionProcess:
    """
    马尔可夫决策过程实现
    
    形式化定义：MDP = ⟨S, A, P, R, γ⟩
    - S: 状态空间
    - A: 动作空间  
    - P: 状态转移概率
    - R: 奖励函数
    - γ: 折扣因子
    """
    
    def __init__(self, 
                 num_knowledge_points: int,
                 discount_factor: float = 0.95,
                 max_steps: int = 50):
        """
        初始化MDP
        
        Args:
            num_knowledge_points: 知识点数量
            discount_factor: 折扣因子γ
            max_steps: 最大步数
        """
        self.num_knowledge_points = num_knowledge_points
        self.discount_factor = discount_factor
        self.max_steps = max_steps
        
        # 状态和动作空间
        self.state_space_size = num_knowledge_points
        self.action_space_size = num_knowledge_points
        
        # 转移历史
        self.transition_history: List[MDPTransition] = []
        
    def get_state_space(self) -> np.ndarray:
        """
        获取状态空间
        
        Returns:
            状态空间的离散化表示
        """
        # 对于连续状态空间，返回采样点
        return np.linspace(0, 1, 11)  # [0, 0.1, 0.2, ..., 1.0]
    
    def get_action_space(self) -> List[int]:
        """
        获取动作空间
        
        Returns:
            可选动作列表
        """
        return list(range(self.action_space_size))
    
    def transition_probability(self, 
                             state: MDPState, 
                             action: int, 
                             next_state: MDPState) -> float:
        """
        计算状态转移概率 P(s'|s,a)
        
        Args:
            state: 当前状态
            action: 执行的动作
            next_state: 下一状态
            
        Returns:
            转移概率
        """
        # 由于学习过程的随机性，这里简化为高斯分布
        current_mastery = state.knowledge_mastery[action]
        next_mastery = next_state.knowledge_mastery[action]
        
        # 期望的掌握程度提升
        expected_improvement = self._calculate_expected_improvement(state, action)
        expected_next_mastery = min(1.0, current_mastery + expected_improvement)
        
        # 高斯分布的转移概率
        variance = 0.01  # 转移的不确定性
        prob = np.exp(-0.5 * ((next_mastery - expected_next_mastery) ** 2) / variance)
        prob /= np.sqrt(2 * np.pi * variance)
        
        return prob
    
    def _calculate_expected_improvement(self, state: MDPState, action: int) -> float:
        """计算期望的掌握程度提升"""
        # 这里使用简化的学习效果模型
        base_learning_rate = 0.3
        current_mastery = state.knowledge_mastery[action]
        
        # 边际效应递减
        mastery_factor = 1.0 - current_mastery * 0.7
        
        return base_learning_rate * mastery_factor
    
    def reward_function(self, 
                       state: MDPState, 
                       action: int, 
                       next_state: MDPState) -> float:
        """
        奖励函数 R(s,a,s')
        
        Args:
            state: 当前状态
            action: 执行的动作
            next_state: 下一状态
            
        Returns:
            奖励值
        """
        reward = 0.0
        
        # 1. 基础奖励：掌握程度提升
        improvement = (next_state.knowledge_mastery[action] - 
                      state.knowledge_mastery[action])
        reward += improvement * 2.0
        
        # 2. 成就奖励：突破阈值
        threshold = 0.6
        if (state.knowledge_mastery[action] < threshold and 
            next_state.knowledge_mastery[action] >= threshold):
            reward += 1.0
        
        # 3. 效率奖励：整体进度
        overall_progress = np.mean(next_state.knowledge_mastery)
        reward += overall_progress * 0.5
        
        # 4. 完成奖励：达成目标
        success_count = np.sum(next_state.knowledge_mastery >= threshold)
        if success_count >= 3:  # 假设需要掌握3个知识点
            reward += 5.0
        
        # 5. 效率惩罚：过度学习
        if state.knowledge_mastery[action] > 0.8:
            reward -= 0.2
        
        return reward
    
    def is_terminal_state(self, state: MDPState) -> bool:
        """
        判断是否为终止状态
        
        Args:
            state: 状态
            
        Returns:
            是否为终止状态
        """
        # 达到最大步数
        if state.time_step >= self.max_steps:
            return True
        
        # 达成学习目标
        success_count = np.sum(state.knowledge_mastery >= 0.6)
        if success_count >= 3:
            return True
        
        return False
    
    def get_valid_actions(self, state: MDPState) -> List[int]:
        """
        获取状态下的有效动作
        
        Args:
            state: 当前状态
            
        Returns:
            有效动作列表
        """
        # 在学习场景中，通常所有知识点都可以学习
        return self.get_action_space()
    
    def calculate_state_value(self, 
                            state: MDPState, 
                            policy: Callable[[MDPState], int],
                            horizon: int = 100) -> float:
        """
        计算状态价值函数 V^π(s)
        
        Args:
            state: 状态
            policy: 策略函数
            horizon: 计算horizon
            
        Returns:
            状态价值
        """
        if horizon <= 0 or self.is_terminal_state(state):
            return 0.0
        
        action = policy(state)
        
        # 简化：假设确定性转移到期望下一状态
        next_state = self._get_expected_next_state(state, action)
        immediate_reward = self.reward_function(state, action, next_state)
        
        future_value = self.calculate_state_value(next_state, policy, horizon - 1)
        
        return immediate_reward + self.discount_factor * future_value
    
    def calculate_action_value(self, 
                             state: MDPState, 
                             action: int,
                             policy: Callable[[MDPState], int],
                             horizon: int = 100) -> float:
        """
        计算动作价值函数 Q^π(s,a)
        
        Args:
            state: 状态
            action: 动作
            policy: 策略函数
            horizon: 计算horizon
            
        Returns:
            动作价值
        """
        if horizon <= 0 or self.is_terminal_state(state):
            return 0.0
        
        next_state = self._get_expected_next_state(state, action)
        immediate_reward = self.reward_function(state, action, next_state)
        
        future_value = self.calculate_state_value(next_state, policy, horizon - 1)
        
        return immediate_reward + self.discount_factor * future_value
    
    def _get_expected_next_state(self, state: MDPState, action: int) -> MDPState:
        """获取期望的下一状态"""
        new_mastery = state.knowledge_mastery.copy()
        
        # 学习的知识点提升
        improvement = self._calculate_expected_improvement(state, action)
        new_mastery[action] = min(1.0, new_mastery[action] + improvement)
        
        # 其他知识点遗忘
        forgetting_rate = 0.02
        for i in range(len(new_mastery)):
            if i != action:
                new_mastery[i] = max(0.0, new_mastery[i] * (1 - forgetting_rate))
        
        return MDPState(
            knowledge_mastery=new_mastery,
            time_step=state.time_step + 1,
            metadata=state.metadata.copy()
        )
    
    def add_transition(self, transition: MDPTransition):
        """添加状态转移到历史记录"""
        self.transition_history.append(transition)
    
    def get_transition_statistics(self) -> Dict[str, float]:
        """获取转移统计信息"""
        if not self.transition_history:
            return {}
        
        rewards = [t.reward for t in self.transition_history]
        
        return {
            'total_transitions': len(self.transition_history),
            'average_reward': np.mean(rewards),
            'total_reward': np.sum(rewards),
            'reward_std': np.std(rewards),
            'max_reward': np.max(rewards),
            'min_reward': np.min(rewards)
        }
    
    def reset_history(self):
        """重置转移历史"""
        self.transition_history = []
    
    def value_iteration(self, 
                       theta: float = 1e-6, 
                       max_iterations: int = 1000) -> Dict[MDPState, float]:
        """
        值迭代算法求解最优价值函数
        
        Args:
            theta: 收敛阈值
            max_iterations: 最大迭代次数
            
        Returns:
            最优价值函数
        """
        # 由于状态空间连续，这里提供框架，实际实现需要状态离散化
        # 或使用函数近似方法
        
        # 初始化价值函数
        V = {}
        
        # 生成状态样本
        state_samples = self._generate_state_samples(1000)
        
        for state in state_samples:
            V[state] = 0.0
        
        for iteration in range(max_iterations):
            delta = 0.0
            
            for state in state_samples:
                if self.is_terminal_state(state):
                    continue
                
                v = V[state]
                
                # 计算所有动作的价值
                action_values = []
                for action in self.get_valid_actions(state):
                    next_state = self._get_expected_next_state(state, action)
                    reward = self.reward_function(state, action, next_state)
                    
                    # 查找最接近的下一状态的价值
                    next_value = self._get_closest_state_value(next_state, V)
                    
                    action_value = reward + self.discount_factor * next_value
                    action_values.append(action_value)
                
                V[state] = max(action_values) if action_values else 0.0
                delta = max(delta, abs(v - V[state]))
            
            if delta < theta:
                print(f"值迭代在第{iteration+1}次迭代后收敛")
                break
        
        return V
    
    def _generate_state_samples(self, num_samples: int) -> List[MDPState]:
        """生成状态样本"""
        samples = []
        for _ in range(num_samples):
            mastery = np.random.uniform(0, 1, self.num_knowledge_points)
            time_step = np.random.randint(0, self.max_steps)
            state = MDPState(mastery, time_step, {})
            samples.append(state)
        return samples
    
    def _get_closest_state_value(self, target_state: MDPState, 
                                value_function: Dict[MDPState, float]) -> float:
        """获取最接近状态的价值"""
        if target_state in value_function:
            return value_function[target_state]
        
        # 找到最相似的状态
        min_distance = float('inf')
        closest_value = 0.0
        
        for state, value in value_function.items():
            distance = np.linalg.norm(target_state.knowledge_mastery - 
                                    state.knowledge_mastery)
            if distance < min_distance:
                min_distance = distance
                closest_value = value
        
        return closest_value
