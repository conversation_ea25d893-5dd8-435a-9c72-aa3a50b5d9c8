@echo off
chcp 65001 >nul
title EduBrain 设计平台 - 开发环境

echo.
echo ========================================
echo 🚀 EduBrain 设计平台 - 开发环境启动
echo ========================================
echo.

:: 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或未添加到 PATH
    echo 请安装 Python 3.8+ 并添加到系统 PATH
    pause
    exit /b 1
)

:: 检查 Node.js 是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或未添加到 PATH
    echo 请安装 Node.js 16+ 并添加到系统 PATH
    pause
    exit /b 1
)

:: 检查后端依赖
echo 🔍 检查后端依赖...
if not exist "backend\requirements.txt" (
    echo ❌ 未找到 backend\requirements.txt
    pause
    exit /b 1
)

:: 检查前端依赖
echo 🔍 检查前端依赖...
if not exist "frontend\package.json" (
    echo ❌ 未找到 frontend\package.json
    pause
    exit /b 1
)

if not exist "frontend\node_modules" (
    echo 📦 安装前端依赖...
    cd frontend
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo ✅ 前端依赖安装完成
)

:: 启动后端服务
echo.
echo 🚀 启动后端服务...
start "EduBrain 后端服务" cmd /k "cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

:: 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 启动前端服务
echo.
echo 🎨 启动前端服务...
start "EduBrain 前端服务" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo ✨ 开发环境启动完成！
echo ========================================
echo.
echo 📋 服务地址:
echo   🔗 前端应用: http://localhost:3000
echo   🔗 后端 API: http://localhost:8000
echo   🔗 API 文档: http://localhost:8000/docs
echo.
echo 💡 提示:
echo   - 两个服务窗口已打开，请保持运行
echo   - 按 Ctrl+C 可在各自窗口中停止服务
echo   - 修改代码后服务会自动重启
echo   - 如需停止所有服务，请关闭对应的命令行窗口
echo.
echo 🎉 开始开发吧！
echo.
pause
