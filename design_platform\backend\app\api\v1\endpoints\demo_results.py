"""
演示结果API端点
"""
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

router = APIRouter()


class ModelComparison(BaseModel):
    model_name: str
    accuracy: float
    auc: float
    rmse: float
    training_time: str
    is_best: bool


class SampleDiagnosis(BaseModel):
    student_id: str
    overall_ability: float
    strong_areas: List[str]
    weak_areas: List[str]
    recommendation: str


class DemoResultsResponse(BaseModel):
    dataset_id: int
    dataset_name: str
    models_comparison: List[ModelComparison]
    sample_diagnoses: List[SampleDiagnosis]
    visualization_charts: Dict[str, Any]


@router.get("/{dataset_id}", response_model=DemoResultsResponse)
async def get_demo_results(dataset_id: int):
    """获取演示结果数据"""
    try:
        # 根据数据集ID生成相应的演示数据
        if dataset_id == 1:  # Assist0910
            dataset_name = "ASSISTments 2009-2010"
            models_comparison = [
                ModelComparison(
                    model_name="EduBrain",
                    accuracy=0.7892,
                    auc=0.8456,
                    rmse=0.3421,
                    training_time="12m 34s",
                    is_best=True
                ),
                ModelComparison(
                    model_name="NCDM",
                    accuracy=0.7654,
                    auc=0.8234,
                    rmse=0.3567,
                    training_time="8m 45s",
                    is_best=False
                ),
                ModelComparison(
                    model_name="KANCD",
                    accuracy=0.7432,
                    auc=0.8012,
                    rmse=0.3789,
                    training_time="15m 23s",
                    is_best=False
                ),
                ModelComparison(
                    model_name="IRT",
                    accuracy=0.7123,
                    auc=0.7789,
                    rmse=0.4012,
                    training_time="3m 12s",
                    is_best=False
                )
            ]
            
            sample_diagnoses = [
                SampleDiagnosis(
                    student_id="Student_001",
                    overall_ability=0.82,
                    strong_areas=["代数运算", "几何推理"],
                    weak_areas=["概率统计", "函数分析"],
                    recommendation="建议加强概率统计的基础练习，重点关注条件概率和贝叶斯定理的应用。"
                ),
                SampleDiagnosis(
                    student_id="Student_002",
                    overall_ability=0.65,
                    strong_areas=["基础运算"],
                    weak_areas=["代数运算", "几何推理", "函数分析"],
                    recommendation="需要系统性地复习代数基础，建议从一元一次方程开始逐步提升。"
                ),
                SampleDiagnosis(
                    student_id="Student_003",
                    overall_ability=0.91,
                    strong_areas=["代数运算", "几何推理", "函数分析"],
                    weak_areas=["概率统计"],
                    recommendation="整体表现优秀，可以尝试更高难度的综合应用题目。"
                ),
                SampleDiagnosis(
                    student_id="Student_004",
                    overall_ability=0.58,
                    strong_areas=["基础运算"],
                    weak_areas=["代数运算", "几何推理"],
                    recommendation="建议重点加强几何基础概念的理解，多做图形识别和性质判断练习。"
                )
            ]
            
        elif dataset_id == 2:  # Assist17
            dataset_name = "ASSISTments 2017"
            models_comparison = [
                ModelComparison(
                    model_name="ORCDF",
                    accuracy=0.8123,
                    auc=0.8678,
                    rmse=0.3234,
                    training_time="15m 42s",
                    is_best=True
                ),
                ModelComparison(
                    model_name="NCDM",
                    accuracy=0.7891,
                    auc=0.8445,
                    rmse=0.3456,
                    training_time="11m 23s",
                    is_best=False
                ),
                ModelComparison(
                    model_name="KANCD",
                    accuracy=0.7678,
                    auc=0.8234,
                    rmse=0.3678,
                    training_time="18m 56s",
                    is_best=False
                )
            ]
            
            sample_diagnoses = [
                SampleDiagnosis(
                    student_id="Student_A01",
                    overall_ability=0.78,
                    strong_areas=["数据分析", "统计推断"],
                    weak_areas=["微积分基础"],
                    recommendation="在数据分析方面表现出色，建议补强微积分基础以支持更高级的统计方法。"
                ),
                SampleDiagnosis(
                    student_id="Student_A02",
                    overall_ability=0.69,
                    strong_areas=["基础代数"],
                    weak_areas=["数据分析", "统计推断"],
                    recommendation="需要加强统计思维的培养，建议从描述性统计开始系统学习。"
                )
            ]
            
        else:  # 其他数据集的默认数据
            dataset_name = f"Dataset_{dataset_id}"
            models_comparison = [
                ModelComparison(
                    model_name="ORCDF",
                    accuracy=0.7756,
                    auc=0.8345,
                    rmse=0.3456,
                    training_time="10m 15s",
                    is_best=True
                ),
                ModelComparison(
                    model_name="NCDM",
                    accuracy=0.7534,
                    auc=0.8123,
                    rmse=0.3678,
                    training_time="7m 30s",
                    is_best=False
                )
            ]
            
            sample_diagnoses = [
                SampleDiagnosis(
                    student_id="Demo_Student_01",
                    overall_ability=0.75,
                    strong_areas=["基础概念"],
                    weak_areas=["应用题"],
                    recommendation="基础扎实，建议多练习实际应用场景的题目。"
                )
            ]

        # 生成可视化图表数据
        visualization_charts = {
            "performance_radar": {
                "categories": ["准确率", "AUC", "稳定性", "效率", "可解释性"],
                "edubrain": [0.89, 0.92, 0.87, 0.78, 0.85],
                "ncdm": [0.82, 0.85, 0.79, 0.88, 0.72],
                "kancd": [0.78, 0.81, 0.83, 0.65, 0.79]
            },
            "training_curves": {
                "epochs": list(range(1, 51)),
                "edubrain_loss": [0.8 - i * 0.01 for i in range(50)],
                "ncdm_loss": [0.85 - i * 0.009 for i in range(50)],
                "kancd_loss": [0.9 - i * 0.008 for i in range(50)]
            }
        }

        return DemoResultsResponse(
            dataset_id=dataset_id,
            dataset_name=dataset_name,
            models_comparison=models_comparison,
            sample_diagnoses=sample_diagnoses,
            visualization_charts=visualization_charts
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取演示结果失败: {str(e)}"
        )


@router.get("/", response_model=List[DemoResultsResponse])
async def get_all_demo_results():
    """获取所有数据集的演示结果"""
    try:
        results = []
        for dataset_id in [1, 2, 3, 4]:
            result = await get_demo_results(dataset_id)
            results.append(result)
        return results
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有演示结果失败: {str(e)}"
        )
