/**
 * API配置工具 - 支持内网穿透
 * 自动检测当前访问方式并配置相应的API地址
 */

export interface ApiConfig {
  baseUrl: string;
  isLocalhost: boolean;
  isTunnel: boolean;
  tunnelType?: string;
}

/**
 * 检测内网穿透类型
 */
const detectTunnelType = (host: string): string | null => {
  if (host.includes('ngrok.io') || host.includes('ngrok-free.app')) {
    return 'ngrok';
  }
  if (host.includes('cpolar.top') || host.includes('cpolar.cn')) {
    return 'cpolar';
  }
  if (host.includes('localtunnel.me')) {
    return 'localtunnel';
  }
  if (host.includes('serveo.net')) {
    return 'serveo';
  }
  if (host.includes('localhost.run')) {
    return 'localhost.run';
  }
  return null;
};

/**
 * 动态获取API配置
 */
export const getApiConfig = (): ApiConfig => {
  const currentHost = window.location.host;
  const currentProtocol = window.location.protocol;
  const tunnelType = detectTunnelType(currentHost);
  
  // 最高优先级：localStorage覆盖配置（用于调试）
  const overrideUrl = localStorage.getItem('OVERRIDE_API_URL');
  if (overrideUrl) {
    return {
      baseUrl: overrideUrl,
      isLocalhost: overrideUrl.includes('localhost'),
      isTunnel: !overrideUrl.includes('localhost')
    };
  }
  
  // 优先使用环境变量配置
  if (process.env.REACT_APP_API_URL) {
    return {
      baseUrl: process.env.REACT_APP_API_URL,
      isLocalhost: process.env.REACT_APP_API_URL.includes('localhost'),
      isTunnel: false
    };
  }
  
  // 如果是内网穿透访问
  if (tunnelType) {
    let apiUrl = '';
    
    switch (tunnelType) {
      case 'ngrok':
        // ngrok通常使用不同的子域名或端口
        // 假设后端使用8000端口，前端使用3000端口
        if (currentHost.includes(':3000')) {
          apiUrl = `${currentProtocol}//${currentHost.replace(':3000', ':8000')}`;
        } else {
          // 如果是子域名方式，需要替换子域名
          // 例如: frontend-xxx.ngrok.io -> backend-xxx.ngrok.io
          apiUrl = `${currentProtocol}//${currentHost.replace('frontend-', 'backend-')}`;
        }
        break;
        
      case 'cpolar':
        // cpolar通常使用不同的子域名
        if (currentHost.includes(':3000')) {
          apiUrl = `${currentProtocol}//${currentHost.replace(':3000', ':8000')}`;
        } else {
          // 假设后端域名格式: api-xxx.cpolar.top
          apiUrl = `${currentProtocol}//${currentHost.replace('app-', 'api-')}`;
        }
        break;
        
      default:
        // 其他内网穿透服务，尝试端口替换
        if (currentHost.includes(':3000')) {
          apiUrl = `${currentProtocol}//${currentHost.replace(':3000', ':8000')}`;
        } else {
          // 默认使用相同域名，假设后端在8000端口
          apiUrl = `${currentProtocol}//${currentHost}`;
        }
    }
    
    return {
      baseUrl: apiUrl,
      isLocalhost: false,
      isTunnel: true,
      tunnelType
    };
  }
  
  // 本地开发环境
  const isLocalhost = currentHost.includes('localhost') || currentHost.includes('127.0.0.1');
  
  return {
    baseUrl: 'http://localhost:8000',
    isLocalhost,
    isTunnel: false
  };
};

/**
 * 获取API基础URL
 */
export const getApiBaseUrl = (): string => {
  return getApiConfig().baseUrl;
};

/**
 * 创建完整的API URL
 */
export const createApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

/**
 * 检查API连接状态
 */
export const checkApiConnection = async (): Promise<boolean> => {
  try {
    const baseUrl = getApiBaseUrl();
    const response = await fetch(`${baseUrl}/health`, {
      method: 'GET',
      timeout: 5000
    } as any);
    return response.ok;
  } catch (error) {
    console.warn('API连接检查失败:', error);
    return false;
  }
};

/**
 * 获取推荐的API配置信息（用于调试）
 */
export const getApiDebugInfo = (): Record<string, any> => {
  const config = getApiConfig();
  return {
    ...config,
    currentUrl: window.location.href,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString()
  };
};

// 在开发环境下打印API配置信息
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 API配置信息:', getApiDebugInfo());
}
