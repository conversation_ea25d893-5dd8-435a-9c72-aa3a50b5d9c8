#!/usr/bin/env python3
import sqlite3
import json

def check_datasets():
    try:
        # 连接数据库
        conn = sqlite3.connect('design_platform/backend/app.db')
        cursor = conn.cursor()
        
        # 查询数据集表
        cursor.execute("SELECT * FROM datasets")
        datasets = cursor.fetchall()
        
        # 获取列名
        cursor.execute("PRAGMA table_info(datasets)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print("数据集表结构:")
        print(columns)
        print("\n数据集数据:")
        
        for dataset in datasets:
            dataset_dict = dict(zip(columns, dataset))
            print(json.dumps(dataset_dict, indent=2, ensure_ascii=False))
            print("-" * 50)
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库失败: {e}")

if __name__ == "__main__":
    check_datasets()
