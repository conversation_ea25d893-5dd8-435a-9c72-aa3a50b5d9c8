"""
数据适配层
将认知诊断结果转换为AFM模型所需的特征格式
"""

import numpy as np
from typing import Dict, List, Any, Optional
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class KnowledgePoint:
    """知识点数据类"""
    id: str
    name: str
    difficulty: float
    prerequisites: List[str]
    category: str

@dataclass
class UserProfile:
    """用户画像数据类"""
    user_id: str
    overall_ability: float
    knowledge_mastery: Dict[str, float]
    learning_style: str
    preferred_difficulty: str

class CognitiveDiagnosisAdapter:
    """认知诊断数据适配器"""
    
    def __init__(self):
        # 知识点映射表
        self.knowledge_point_mapping = {
            "代数运算": 0,
            "几何推理": 1, 
            "函数理解": 2,
            "概率统计": 3,
            "逻辑推理": 4,
            # 可以根据实际知识点扩展
        }
        
        # 反向映射
        self.id_to_knowledge = {v: k for k, v in self.knowledge_point_mapping.items()}
        
        # 知识点元数据
        self.knowledge_metadata = {
            0: {"name": "代数运算", "difficulty": 0.3, "category": "基础数学", "prerequisites": []},
            1: {"name": "几何推理", "difficulty": 0.5, "category": "空间思维", "prerequisites": [0]},
            2: {"name": "函数理解", "difficulty": 0.7, "category": "抽象思维", "prerequisites": [0]},
            3: {"name": "概率统计", "difficulty": 0.6, "category": "数据分析", "prerequisites": [0]},
            4: {"name": "逻辑推理", "difficulty": 0.8, "category": "逻辑思维", "prerequisites": [0, 1]},
        }

    def convert_diagnosis_to_user_profile(self, diagnosis_data: Dict) -> UserProfile:
        """将认知诊断结果转换为用户画像"""
        try:
            user_id = diagnosis_data.get('student_id', 'unknown')
            overall_ability = diagnosis_data.get('overall_ability', 0.5)
            
            # 提取知识点掌握情况
            knowledge_diagnosis = diagnosis_data.get('knowledge_diagnosis', {})
            knowledge_mastery = {}
            
            for kc, data in knowledge_diagnosis.items():
                mastery_prob = data.get('mastery_probability', 0.5)
                knowledge_mastery[kc] = mastery_prob
            
            # 根据整体能力推断学习风格
            if overall_ability > 0.7:
                learning_style = "advanced"
                preferred_difficulty = "hard"
            elif overall_ability > 0.5:
                learning_style = "intermediate"
                preferred_difficulty = "medium"
            else:
                learning_style = "beginner"
                preferred_difficulty = "easy"
            
            return UserProfile(
                user_id=user_id,
                overall_ability=overall_ability,
                knowledge_mastery=knowledge_mastery,
                learning_style=learning_style,
                preferred_difficulty=preferred_difficulty
            )
            
        except Exception as e:
            logger.error(f"Failed to convert diagnosis data: {e}")
            # 返回默认用户画像
            return UserProfile(
                user_id="unknown",
                overall_ability=0.5,
                knowledge_mastery={},
                learning_style="intermediate",
                preferred_difficulty="medium"
            )

    def create_afm_features(self, user_profile: UserProfile, target_knowledge_id: int) -> np.ndarray:
        """为AFM模型创建特征向量"""
        try:
            # 用户特征向量
            user_features = []
            
            # 基础能力特征
            user_features.append(user_profile.overall_ability)
            
            # 学习风格编码
            style_encoding = {
                "beginner": [1, 0, 0],
                "intermediate": [0, 1, 0], 
                "advanced": [0, 0, 1]
            }
            user_features.extend(style_encoding.get(user_profile.learning_style, [0, 1, 0]))
            
            # 知识点掌握情况特征
            mastery_features = []
            for kc_name, kc_id in self.knowledge_point_mapping.items():
                mastery_prob = user_profile.knowledge_mastery.get(kc_name, 0.5)
                mastery_features.append(mastery_prob)
            
            # 补齐到固定维度(假设102维)
            target_dim = 102
            current_dim = len(user_features) + len(mastery_features)
            
            if current_dim < target_dim:
                padding = [0.5] * (target_dim - current_dim)
                user_features.extend(mastery_features + padding)
            else:
                user_features.extend(mastery_features[:target_dim - len(user_features)])
            
            # 目标知识点特征向量
            knowledge_features = self._create_knowledge_features(target_knowledge_id)
            
            # 组合特征
            combined_features = np.array([user_features, knowledge_features], dtype=np.float32)
            
            return combined_features
            
        except Exception as e:
            logger.error(f"Failed to create AFM features: {e}")
            # 返回默认特征
            return np.full((2, 102), 0.5, dtype=np.float32)

    def _create_knowledge_features(self, knowledge_id: int) -> List[float]:
        """创建知识点特征向量"""
        features = []
        
        # 获取知识点元数据
        metadata = self.knowledge_metadata.get(knowledge_id, {})
        
        # 基础特征
        features.append(metadata.get('difficulty', 0.5))
        
        # 类别编码
        category = metadata.get('category', 'unknown')
        category_encoding = {
            '基础数学': [1, 0, 0, 0, 0],
            '空间思维': [0, 1, 0, 0, 0],
            '抽象思维': [0, 0, 1, 0, 0],
            '数据分析': [0, 0, 0, 1, 0],
            '逻辑思维': [0, 0, 0, 0, 1]
        }
        features.extend(category_encoding.get(category, [0, 0, 0, 0, 1]))
        
        # 前置知识点依赖
        prerequisites = metadata.get('prerequisites', [])
        prereq_features = [0] * len(self.knowledge_point_mapping)
        for prereq_id in prerequisites:
            if prereq_id < len(prereq_features):
                prereq_features[prereq_id] = 1
        features.extend(prereq_features)
        
        # 补齐到102维
        target_dim = 102
        if len(features) < target_dim:
            features.extend([0.0] * (target_dim - len(features)))
        elif len(features) > target_dim:
            features = features[:target_dim]
        
        return features

    def filter_recommendations_by_prerequisites(self, 
                                              recommendations: List[Dict], 
                                              user_profile: UserProfile,
                                              mastery_threshold: float = 0.6) -> List[Dict]:
        """根据前置知识点过滤推荐结果"""
        filtered_recommendations = []
        
        for rec in recommendations:
            knowledge_id = int(rec.get('knowledge_point_id', 0))
            metadata = self.knowledge_metadata.get(knowledge_id, {})
            prerequisites = metadata.get('prerequisites', [])
            
            # 检查前置知识点是否掌握
            prerequisites_met = True
            for prereq_id in prerequisites:
                prereq_name = self.id_to_knowledge.get(prereq_id, '')
                mastery_level = user_profile.knowledge_mastery.get(prereq_name, 0.0)
                
                if mastery_level < mastery_threshold:
                    prerequisites_met = False
                    break
            
            if prerequisites_met:
                # 添加知识点名称和元数据
                rec['knowledge_point_name'] = metadata.get('name', f'Knowledge_{knowledge_id}')
                rec['category'] = metadata.get('category', 'unknown')
                rec['prerequisites_met'] = True
                filtered_recommendations.append(rec)
            else:
                # 标记为前置条件不满足
                rec['prerequisites_met'] = False
                rec['reason'] = '前置知识点掌握不足'
        
        return filtered_recommendations

    def generate_learning_sequence(self, 
                                 recommendations: List[Dict], 
                                 user_profile: UserProfile) -> List[Dict]:
        """生成合理的学习序列"""
        # 按照前置关系和难度排序
        def sort_key(rec):
            knowledge_id = int(rec.get('knowledge_point_id', 0))
            metadata = self.knowledge_metadata.get(knowledge_id, {})
            difficulty = metadata.get('difficulty', 0.5)
            prerequisites_count = len(metadata.get('prerequisites', []))
            confidence = rec.get('confidence_score', 0.5)
            
            # 优先级：前置知识点少 > 置信度高 > 难度适中
            return (prerequisites_count, -confidence, abs(difficulty - 0.5))
        
        # 过滤并排序
        filtered_recs = self.filter_recommendations_by_prerequisites(recommendations, user_profile)
        sorted_recs = sorted(filtered_recs, key=sort_key)
        
        # 添加序号和学习建议
        for i, rec in enumerate(sorted_recs):
            rec['sequence_order'] = i + 1
            rec['learning_suggestion'] = self._generate_learning_suggestion(rec, user_profile)
        
        return sorted_recs

    def _generate_learning_suggestion(self, recommendation: Dict, user_profile: UserProfile) -> str:
        """生成学习建议"""
        confidence = recommendation.get('confidence_score', 0.5)
        difficulty = recommendation.get('difficulty_level', 'medium')
        
        if confidence > 0.8:
            return f"您在这个知识点上表现出色，建议快速复习巩固"
        elif confidence > 0.6:
            return f"这个知识点比较适合您当前水平，建议重点学习"
        else:
            return f"这个知识点有一定挑战性，建议分步骤深入学习"

    def get_knowledge_point_info(self, knowledge_id: int) -> Dict:
        """获取知识点详细信息"""
        metadata = self.knowledge_metadata.get(knowledge_id, {})
        return {
            'id': knowledge_id,
            'name': metadata.get('name', f'Knowledge_{knowledge_id}'),
            'difficulty': metadata.get('difficulty', 0.5),
            'category': metadata.get('category', 'unknown'),
            'prerequisites': [self.id_to_knowledge.get(pid, f'Knowledge_{pid}') 
                            for pid in metadata.get('prerequisites', [])],
            'description': f"这是关于{metadata.get('name', '未知知识点')}的学习内容"
        }

# 全局适配器实例
diagnosis_adapter = CognitiveDiagnosisAdapter()
