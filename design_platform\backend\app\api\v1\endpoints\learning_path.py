"""
学习路径推荐API路由
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import logging
import traceback
import datetime

# 导入EduSim评估服务
from app.services.edusim_evaluation_service import EduSimEvaluationService

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 初始化EduSim评估服务
edusim_service = EduSimEvaluationService()

# Pydantic模型定义
class KnowledgeDiagnosis(BaseModel):
    mastery_probability: float
    mastery_level: str

class DiagnosisData(BaseModel):
    student_id: str
    overall_ability: float
    knowledge_diagnosis: Dict[str, KnowledgeDiagnosis]

class Preferences(BaseModel):
    max_items: Optional[int] = 10
    difficulty_preference: Optional[str] = "medium"
    time_constraint: Optional[int] = 120

class RecommendationRequest(BaseModel):
    user_id: str
    diagnosis_data: DiagnosisData
    preferences: Optional[Preferences] = None

class ModelLoadRequest(BaseModel):
    model_path: Optional[str] = "models/afm_model.pth"
    embeddings_path: Optional[str] = "models/knowledge_embeddings.pkl"

class EvaluationRequest(BaseModel):
    learning_path: Dict[str, Any]
    env_type: Optional[str] = "TMS"
    max_episodes: Optional[int] = 100
    max_steps: Optional[int] = 10

class PathComparisonRequest(BaseModel):
    paths: List[Dict[str, Any]]
    env_type: Optional[str] = "TMS"
    max_episodes: Optional[int] = 50

@router.post("/recommend")
async def recommend_learning_path(request: RecommendationRequest):
    """
    为用户推荐学习路径
    """
    try:
        user_id = request.user_id
        diagnosis_data = request.diagnosis_data.dict()
        preferences = request.preferences.dict() if request.preferences else {}

        # 模拟推荐结果（因为AFM服务可能不可用）
        mock_recommendations = [
            {
                'knowledge_point_id': '1',
                'knowledge_point_name': '几何推理',
                'confidence_score': 0.75,
                'difficulty_level': 'medium',
                'estimated_duration': 25,
                'category': '空间思维',
                'prerequisites_met': True,
                'sequence_order': 1,
                'learning_suggestion': '这个知识点比较适合您当前水平，建议重点学习'
            },
            {
                'knowledge_point_id': '3',
                'knowledge_point_name': '概率统计', 
                'confidence_score': 0.68,
                'difficulty_level': 'medium',
                'estimated_duration': 30,
                'category': '数据分析',
                'prerequisites_met': True,
                'sequence_order': 2,
                'learning_suggestion': '这个知识点比较适合您当前水平，建议重点学习'
            },
            {
                'knowledge_point_id': '4',
                'knowledge_point_name': '逻辑推理',
                'confidence_score': 0.62,
                'difficulty_level': 'hard',
                'estimated_duration': 35,
                'category': '逻辑思维',
                'prerequisites_met': False,
                'sequence_order': 3,
                'learning_suggestion': '这个知识点有一定挑战性，建议分步骤深入学习'
            }
        ]

        # 构造响应
        response_data = {
            'success': True,
            'data': {
                'user_id': user_id,
                'learning_path': {
                    'total_duration': 90,
                    'overall_difficulty': 'medium',
                    'sequence': mock_recommendations,
                    'created_at': datetime.datetime.now().isoformat()
                },
                'user_profile': {
                    'overall_ability': diagnosis_data.get('overall_ability', 0.65),
                    'learning_style': 'intermediate',
                    'preferred_difficulty': 'medium'
                },
                'metadata': {
                    'total_recommendations': len(mock_recommendations),
                    'avg_confidence': 0.68,
                    'model_info': {
                        'model_loaded': False,
                        'embeddings_loaded': False,
                        'mode': 'simulation'
                    }
                }
            }
        }
        
        logger.info(f"Generated learning path for user {user_id} with {len(mock_recommendations)} recommendations")
        return response_data

    except Exception as e:
        logger.error(f"Error in recommend_learning_path: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f'服务器内部错误: {str(e)}'
        )

@router.post("/evaluate")
async def evaluate_learning_path(request: EvaluationRequest):
    """
    使用EduSim评估学习路径效果
    """
    try:
        if not edusim_service.is_available():
            raise HTTPException(
                status_code=503,
                detail='EduSim仿真环境不可用，请检查安装'
            )

        # 执行评估
        evaluation_result = edusim_service.evaluate_learning_path(
            learning_path=request.learning_path,
            env_type=request.env_type,
            max_episodes=request.max_episodes,
            max_steps=request.max_steps
        )

        if not evaluation_result.get('success'):
            raise HTTPException(
                status_code=500,
                detail=f"评估失败: {evaluation_result.get('error', '未知错误')}"
            )

        return {
            'success': True,
            'data': evaluation_result,
            'message': '学习路径评估完成'
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Learning path evaluation failed: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f'学习路径评估失败: {str(e)}'
        )

@router.get("/knowledge-point/{knowledge_id}")
async def get_knowledge_point_info(knowledge_id: int):
    """获取知识点详细信息"""
    try:
        # 模拟知识点信息
        knowledge_info = {
            'id': knowledge_id,
            'name': f'Knowledge_{knowledge_id}',
            'difficulty': 0.5,
            'category': 'unknown',
            'prerequisites': [],
            'description': f'这是关于Knowledge_{knowledge_id}的学习内容'
        }
        
        return {
            'success': True,
            'data': knowledge_info
        }
        
    except Exception as e:
        logger.error(f"Error getting knowledge point info: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'获取知识点信息失败: {str(e)}'
        )

@router.get("/model/status")
async def get_model_status():
    """获取AFM模型状态"""
    try:
        model_info = {
            'model_loaded': False,
            'embeddings_loaded': False,
            'embedding_dim': 102,
            'device': 'cpu',
            'knowledge_points_count': 5
        }
        
        return {
            'success': True,
            'data': model_info
        }
        
    except Exception as e:
        logger.error(f"Error getting model status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'获取模型状态失败: {str(e)}'
        )

@router.post("/model/load")
async def load_model(request: ModelLoadRequest):
    """加载AFM模型"""
    try:
        # 模拟模型加载
        model_info = {
            'model_loaded': False,
            'embeddings_loaded': False,
            'embedding_dim': 102,
            'device': 'cpu',
            'knowledge_points_count': 5
        }
        
        return {
            'success': True,
            'message': '模型加载模拟完成（实际模型未加载）',
            'data': model_info
        }
        
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'模型加载失败: {str(e)}'
        )

@router.post("/simulate")
async def simulate_recommendation(user_id: str = "test_user"):
    """模拟推荐（用于测试）"""
    try:
        # 模拟推荐结果
        mock_recommendations = [
            {
                'knowledge_point_id': '1',
                'knowledge_point_name': '几何推理',
                'confidence_score': 0.75,
                'difficulty_level': 'medium',
                'estimated_duration': 25,
                'category': '空间思维',
                'prerequisites_met': True,
                'sequence_order': 1,
                'learning_suggestion': '这个知识点比较适合您当前水平，建议重点学习'
            },
            {
                'knowledge_point_id': '3',
                'knowledge_point_name': '概率统计', 
                'confidence_score': 0.68,
                'difficulty_level': 'medium',
                'estimated_duration': 30,
                'category': '数据分析',
                'prerequisites_met': True,
                'sequence_order': 2,
                'learning_suggestion': '这个知识点比较适合您当前水平，建议重点学习'
            },
            {
                'knowledge_point_id': '4',
                'knowledge_point_name': '逻辑推理',
                'confidence_score': 0.62,
                'difficulty_level': 'hard',
                'estimated_duration': 35,
                'category': '逻辑思维',
                'prerequisites_met': False,
                'sequence_order': 3,
                'learning_suggestion': '这个知识点有一定挑战性，建议分步骤深入学习'
            }
        ]
        
        response_data = {
            'success': True,
            'data': {
                'user_id': user_id,
                'learning_path': {
                    'total_duration': 90,
                    'overall_difficulty': 'medium',
                    'sequence': mock_recommendations,
                    'created_at': datetime.datetime.now().isoformat()
                },
                'user_profile': {
                    'overall_ability': 0.65,
                    'learning_style': 'intermediate',
                    'preferred_difficulty': 'medium'
                },
                'metadata': {
                    'total_recommendations': len(mock_recommendations),
                    'avg_confidence': 0.68,
                    'model_info': {
                        'model_loaded': False,
                        'embeddings_loaded': False,
                        'mode': 'simulation'
                    }
                }
            }
        }
        
        return response_data
        
    except Exception as e:
        logger.error(f"Error in simulate_recommendation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'模拟推荐失败: {str(e)}'
        )

@router.get("/health")
async def health_check():
    """健康检查"""
    return {
        'success': True,
        'message': 'Learning Path Service is running',
        'timestamp': datetime.datetime.now().isoformat()
    }
