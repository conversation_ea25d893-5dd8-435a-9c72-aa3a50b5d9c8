"""
模型管理API端点
"""
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, status

router = APIRouter()


@router.get("/supported")
async def get_supported_models():
    """获取支持的模型列表"""
    models = [
        {
            "id": "orcdf",
            "name": "ORCDF",
            "display_name": "EduBrain抗过平滑特征丰富方法",
            "description": "EduBrain抗过平滑特征丰富方法通过响应图和响应感知图卷积网络来缓解认知诊断中的过度平滑问题，提供更丰富的特征表示",
            "category": "graph",
            "paper_url": "https://github.com/ECNU-ILOG/ORCDF",
            "parameters": [
                {
                    "name": "gcn_layers",
                    "display_name": "GCN层数",
                    "type": "int",
                    "default": 3,
                    "min": 1,
                    "max": 5,
                    "description": "响应感知图卷积网络的层数"
                },
                {
                    "name": "ssl_weight",
                    "display_name": "SSL权重",
                    "type": "float",
                    "default": 0.001,
                    "min": 0.0001,
                    "max": 0.01,
                    "description": "自监督学习损失的权重"
                },
                {
                    "name": "ssl_temp",
                    "display_name": "SSL温度",
                    "type": "float",
                    "default": 0.5,
                    "min": 0.1,
                    "max": 5.0,
                    "description": "自监督学习的温度参数"
                },
                {
                    "name": "flip_ratio",
                    "display_name": "翻转比例",
                    "type": "float",
                    "default": 0.15,
                    "min": 0.01,
                    "max": 0.5,
                    "description": "数据增强中的标签翻转比例"
                }
            ]
        },
        {
            "id": "ncdm",
            "name": "NCDM",
            "display_name": "NCDM - 神经认知诊断模型",
            "description": "基于神经网络的认知诊断模型，能够学习学生的知识状态",
            "category": "neural",
            "paper_url": "https://www.ijcai.org/proceedings/2019/0740.pdf",
            "parameters": [
                {
                    "name": "prednet_len1",
                    "display_name": "预测网络第一层维度",
                    "type": "int",
                    "default": 128,
                    "min": 64,
                    "max": 512,
                    "description": "预测网络第一个隐藏层的维度"
                },
                {
                    "name": "prednet_len2",
                    "display_name": "预测网络第二层维度",
                    "type": "int",
                    "default": 64,
                    "min": 32,
                    "max": 256,
                    "description": "预测网络第二个隐藏层的维度"
                }
            ]
        },
        {
            "id": "kancd",
            "name": "KANCD",
            "display_name": "KANCD - 知识感知神经认知诊断",
            "description": "结合知识图谱的神经认知诊断模型",
            "category": "neural",
            "paper_url": "https://dl.acm.org/doi/10.1145/3459637.3482216",
            "parameters": [
                {
                    "name": "hidden_dim",
                    "display_name": "隐藏层维度",
                    "type": "int",
                    "default": 64,
                    "min": 32,
                    "max": 256,
                    "description": "隐藏层的维度"
                }
            ]
        },
        {
            "id": "kscd",
            "name": "KSCD",
            "display_name": "KSCD - 知识结构认知诊断",
            "description": "考虑知识结构的认知诊断模型",
            "category": "neural",
            "paper_url": "",
            "parameters": []
        },
        {
            "id": "cdmfkc",
            "name": "CDMFKC",
            "display_name": "CDMFKC - 模糊知识概念认知诊断",
            "description": "处理模糊知识概念的认知诊断模型",
            "category": "neural",
            "paper_url": "",
            "parameters": []
        },
        {
            "id": "mirt",
            "name": "MIRT",
            "display_name": "MIRT - 多维项目反应理论",
            "description": "经典的多维项目反应理论模型",
            "category": "classic",
            "paper_url": "",
            "parameters": [
                {
                    "name": "dimensions",
                    "display_name": "维度数",
                    "type": "int",
                    "default": 1,
                    "min": 1,
                    "max": 10,
                    "description": "能力的维度数"
                }
            ]
        }
    ]
    
    return {"models": models}


@router.get("/{model_id}/info")
async def get_model_info(model_id: str):
    """获取特定模型的详细信息"""
    models_info = {
        "edubrain": {
            "id": "edubrain",
            "name": "EduBrain",
            "display_name": "过度平滑抗性认知诊断框架",
            "description": "EduBrain是一个创新的认知诊断框架，专门设计用于解决现有认知诊断模型中的过度平滑问题。",
            "detailed_description": """
            EduBrain (Educational Brain) 通过以下创新点解决过度平滑问题：
            
            1. **响应图构建**: 构建包含学生、题目和知识点的三部图，边类型包括学生-题目的响应信号
            2. **响应感知图卷积**: 设计专门的图卷积网络来捕获响应信号中的关键信息
            3. **自监督学习**: 通过对比学习增强学生表示的区分度
            4. **数据增强**: 通过标签翻转等技术提高模型的鲁棒性
            
            实验表明，EduBrain不仅能显著缓解过度平滑问题，还能提升模型的预测性能和可解释性。
            """,
            "advantages": [
                "有效缓解过度平滑问题",
                "提升学生能力诊断的区分度",
                "增强模型的可解释性",
                "适用于多种基础认知诊断模型"
            ],
            "use_cases": [
                "在线教育系统的学生能力诊断",
                "自适应测试系统",
                "个性化学习推荐",
                "教学效果评估"
            ],
            "metrics": [
                "AUC (Area Under Curve)",
                "ACC (Accuracy)",
                "DOA (Degree of Agreement)",
                "MND (Mean Normalized Difference)"
            ]
        },
        "ncdm": {
            "id": "ncdm",
            "name": "NCDM",
            "display_name": "神经认知诊断模型",
            "description": "基于神经网络的认知诊断模型，通过深度学习技术学习学生的知识状态。",
            "detailed_description": """
            NCDM (Neural Cognitive Diagnosis Model) 是一个基于神经网络的认知诊断模型：
            
            1. **学生嵌入**: 学习每个学生在各个知识点上的掌握程度
            2. **题目建模**: 建模题目的难度和区分度
            3. **交互函数**: 通过神经网络学习学生能力和题目特征的交互
            4. **预测网络**: 多层感知机预测学生答题的正确概率
            """,
            "advantages": [
                "端到端的神经网络架构",
                "能够捕获复杂的非线性关系",
                "参数学习灵活",
                "预测性能良好"
            ]
        }
    }
    
    if model_id not in models_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"模型 {model_id} 不存在"
        )
    
    return models_info[model_id]


@router.get("/{model_id}/default-config")
async def get_model_default_config(model_id: str):
    """获取模型的默认配置"""
    default_configs = {
        "orcdf": {
            "batch_size": 256,
            "epochs": 12,
            "learning_rate": 0.004,
            "weight_decay": 0.0,
            "latent_dim": 32,
            "gcn_layers": 3,
            "keep_prob": 1.0,
            "ssl_weight": 0.001,
            "ssl_temp": 0.5,
            "flip_ratio": 0.15,
            "test_size": 0.2,
            "seed": 42,
            "device": "cuda:0"
        },
        "ncdm": {
            "batch_size": 256,
            "epochs": 10,
            "learning_rate": 0.001,
            "weight_decay": 0.0,
            "latent_dim": 32,
            "prednet_len1": 128,
            "prednet_len2": 64,
            "test_size": 0.2,
            "seed": 42,
            "device": "cuda:0"
        },
        "kancd": {
            "batch_size": 256,
            "epochs": 8,
            "learning_rate": 0.001,
            "weight_decay": 0.0,
            "latent_dim": 32,
            "hidden_dim": 64,
            "test_size": 0.2,
            "seed": 42,
            "device": "cuda:0"
        }
    }
    
    if model_id not in default_configs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"模型 {model_id} 的默认配置不存在"
        )
    
    return {"config": default_configs[model_id]}


@router.get("/categories")
async def get_model_categories():
    """获取模型分类"""
    categories = [
        {
            "id": "graph",
            "name": "图神经网络模型",
            "description": "基于图神经网络的认知诊断模型",
            "models": ["orcdf"]
        },
        {
            "id": "neural",
            "name": "神经网络模型",
            "description": "基于传统神经网络的认知诊断模型",
            "models": ["ncdm", "kancd", "kscd", "cdmfkc"]
        },
        {
            "id": "classic",
            "name": "经典模型",
            "description": "基于统计学习的经典认知诊断模型",
            "models": ["mirt", "irt"]
        }
    ]
    
    return {"categories": categories}


@router.get("/available")
async def get_available_models():
    """获取可用的模型列表（别名端点，向后兼容）"""
    return await get_supported_models()
