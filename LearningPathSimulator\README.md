# Learning Path Simulator

一个基于认知科学理论的学习路径规划算法评估仿真平台。

## 项目概述

本项目实现了一个完整的学习路径规划模拟器，用于客观评估不同个性化学习算法的性能。模拟器基于坚实的认知科学理论和数学建模，能够准确模拟真实的学习过程。

## 核心特性

- 🧠 **认知科学基础**：基于Ebbinghaus遗忘曲线、Vygotsky ZPD理论等
- 📊 **严格数学建模**：完整的马尔可夫决策过程建模
- 🎯 **多目标评估**：学习效率、效果、稳定性全面评估
- 🔄 **高度可扩展**：支持任意路径规划算法接入
- 📈 **统计分析**：自动化的显著性检验和效应量分析
- 🚀 **高性能**：支持大规模并行仿真

## 项目结构

```
LearningPathSimulator/
├── core/                   # 核心模块
│   ├── __init__.py
│   ├── base.py            # 基础类定义
│   ├── mdp.py             # 马尔可夫决策过程
│   └── cognitive_models.py # 认知科学模型
├── environments/           # 仿真环境
│   ├── __init__.py
│   ├── learning_env.py    # 学习环境
│   └── advanced_env.py    # 高级环境特性
├── agents/                # 智能体实现
│   ├── __init__.py
│   ├── base_agent.py      # 智能体基类
│   ├── rule_based.py      # 基于规则的智能体
│   ├── ml_based.py        # 机器学习智能体
│   └── rl_agents.py       # 强化学习智能体
├── evaluation/            # 评估系统
│   ├── __init__.py
│   ├── metrics.py         # 评估指标
│   ├── statistics.py      # 统计分析
│   └── visualization.py   # 可视化
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── data_utils.py      # 数据处理
│   └── parallel.py        # 并行计算
├── examples/              # 使用示例
├── tests/                 # 单元测试
├── docs/                  # 文档
├── data/                  # 数据文件
└── models/                # 预训练模型
```

## 快速开始

### 安装依赖

```bash
pip install numpy scipy matplotlib pandas scikit-learn torch
```

### 基本使用

```python
from core.learning_env import LearningEnvironment
from agents.rule_based import GreedyAgent
from evaluation.evaluator import evaluate_agent

# 创建环境
env = LearningEnvironment(
    num_knowledge_points=5,
    learning_rate=0.3,
    success_threshold=0.6
)

# 创建智能体
agent = GreedyAgent(env.action_space_size)

# 评估性能
results = evaluate_agent(agent, env, episodes=100)
print(f"平均得分: {results['avg_final_score']:.3f}")
```

## 核心算法

### 学习效果建模

```python
learning_effect = (
    base_learning_rate * 
    difficulty_factor * 
    mastery_factor * 
    dependency_factor * 
    random_noise
)
```

### 多目标奖励函数

```python
total_reward = (
    base_reward +           # 学习进步奖励
    achievement_reward +    # 里程碑奖励
    efficiency_reward +     # 效率奖励
    completion_reward +     # 完成奖励
    penalty                 # 效率惩罚
)
```

## 支持的算法类型

- **基于规则**：随机、贪心、智能贪心
- **机器学习**：AFM、协同过滤、知识图谱
- **强化学习**：Q-Learning、DQN、PPO

## 评估指标

- **学习效率**：平均奖励、学习步数、时间效率
- **学习效果**：最终掌握程度、目标达成率、知识保持率
- **稳定性**：性能方差、收敛速度、鲁棒性

## 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系项目维护者。
