"""
认证相关的数据模型
"""
from typing import Optional
from pydantic import BaseModel, EmailStr


class LoginRequest(BaseModel):
    """登录请求"""
    username: str
    password: str


class UserResponse(BaseModel):
    """用户响应"""
    id: int
    username: str
    email: str
    full_name: str
    is_active: bool
    role: str


class LoginResponse(BaseModel):
    """登录响应"""
    success: bool
    message: str
    token: str
    token_type: str
    user: UserResponse


class TokenData(BaseModel):
    """Token数据"""
    username: Optional[str] = None


class UserCreate(BaseModel):
    """创建用户"""
    username: str
    email: EmailStr
    full_name: str
    password: str
    role: str = "demo"


class UserUpdate(BaseModel):
    """更新用户"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None


class PasswordChange(BaseModel):
    """修改密码"""
    old_password: str
    new_password: str
