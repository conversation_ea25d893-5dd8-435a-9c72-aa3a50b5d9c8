"""
API测试
"""
import pytest
import json
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.core.database import get_db, Base
from app.models.experiment import Dataset, Experiment

# 创建测试数据库
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

@pytest.fixture(scope="module")
def setup_database():
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

def test_root_endpoint():
    """测试根路径"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data

def test_health_check():
    """测试健康检查"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"

def test_get_datasets(setup_database):
    """测试获取数据集列表"""
    response = client.get("/api/v1/datasets/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_create_dataset(setup_database):
    """测试创建数据集"""
    dataset_data = {
        "name": "test_dataset",
        "display_name": "测试数据集",
        "description": "这是一个测试数据集",
        "is_active": True,
        "is_demo": False
    }
    
    response = client.post("/api/v1/datasets/", json=dataset_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "test_dataset"
    assert data["display_name"] == "测试数据集"

def test_get_supported_models():
    """测试获取支持的模型列表"""
    response = client.get("/api/v1/models/supported")
    assert response.status_code == 200
    data = response.json()
    assert "models" in data
    assert isinstance(data["models"], list)
    assert len(data["models"]) > 0

def test_get_model_info():
    """测试获取模型信息"""
    response = client.get("/api/v1/models/orcdf/info")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "orcdf"
    assert data["name"] == "ORCDF"

def test_get_model_default_config():
    """测试获取模型默认配置"""
    response = client.get("/api/v1/models/orcdf/default-config")
    assert response.status_code == 200
    data = response.json()
    assert "config" in data
    assert "batch_size" in data["config"]
    assert "epochs" in data["config"]

def test_create_experiment(setup_database):
    """测试创建实验"""
    # 先创建一个数据集
    dataset_data = {
        "name": "Assist0910",
        "display_name": "ASSISTments 2009-2010",
        "description": "测试数据集",
        "is_active": True,
        "is_demo": True
    }
    client.post("/api/v1/datasets/", json=dataset_data)
    
    # 创建实验
    experiment_data = {
        "name": "测试实验",
        "description": "这是一个测试实验",
        "dataset_type": "Assist0910",
        "model_type": "orcdf",
        "config": {
            "batch_size": 256,
            "epochs": 5,
            "learning_rate": 0.001,
            "weight_decay": 0.0,
            "latent_dim": 32,
            "gcn_layers": 3,
            "keep_prob": 1.0,
            "ssl_weight": 0.001,
            "ssl_temp": 0.5,
            "flip_ratio": 0.15,
            "test_size": 0.2,
            "seed": 42,
            "device": "cpu"
        },
        "auto_start": False
    }
    
    response = client.post("/api/v1/experiments/", json=experiment_data)
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "测试实验"
    assert data["model_type"] == "orcdf"
    assert data["dataset_type"] == "Assist0910"
    assert data["status"] == "pending"

def test_get_experiments(setup_database):
    """测试获取实验列表"""
    response = client.get("/api/v1/experiments/")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

def test_invalid_model_type():
    """测试无效的模型类型"""
    response = client.get("/api/v1/models/invalid_model/info")
    assert response.status_code == 404

def test_invalid_experiment_config(setup_database):
    """测试无效的实验配置"""
    experiment_data = {
        "name": "无效实验",
        "description": "测试无效配置",
        "dataset_type": "Assist0910",
        "model_type": "orcdf",
        "config": {
            "batch_size": -1,  # 无效的批次大小
            "epochs": 0,       # 无效的训练轮数
            "learning_rate": -0.001,  # 无效的学习率
            "test_size": 1.5,  # 无效的测试集比例
        },
        "auto_start": False
    }
    
    response = client.post("/api/v1/experiments/", json=experiment_data)
    assert response.status_code == 400

if __name__ == "__main__":
    pytest.main([__file__])
