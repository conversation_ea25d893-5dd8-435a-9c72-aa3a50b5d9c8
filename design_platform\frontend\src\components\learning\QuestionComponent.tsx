import React from 'react';
import { Card, Radio, Space, Tag, Alert, Row, Col } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Question } from '../../types/learning';

interface QuestionComponentProps {
  question: Question;
  questionNumber: number;
  selectedAnswer?: string;
  onAnswerChange: (answer: string) => void;
  showResult: boolean;
}

const QuestionComponent: React.FC<QuestionComponentProps> = ({
  question,
  questionNumber,
  selectedAnswer,
  onAnswerChange,
  showResult
}) => {
  const isCorrect = selectedAnswer === question.correctAnswer;
  
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'green';
      case 'medium': return 'orange';
      case 'hard': return 'red';
      default: return 'blue';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '简单';
      case 'medium': return '中等';
      case 'hard': return '困难';
      default: return '未知';
    }
  };

  return (
    <Card
      style={{
        marginBottom: '24px',
        borderRadius: '12px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        border: showResult ? (isCorrect ? '2px solid #52c41a' : '2px solid #ff4d4f') : '1px solid #e8e8e8',
        overflow: 'hidden'
      }}
    >
      {/* 题目头部 */}
      <div style={{
        background: showResult
          ? (isCorrect ? 'linear-gradient(135deg, #52c41a 0%, #73d13d 100%)' : 'linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%)')
          : 'linear-gradient(135deg, #1890ff 0%, #36cfc9 100%)',
        padding: '16px 20px',
        marginBottom: '20px'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: 'rgba(255,255,255,0.2)',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px',
              fontSize: '16px',
              fontWeight: 'bold'
            }}>
              {questionNumber}
            </div>
            <span style={{ fontSize: '18px', fontWeight: 'bold', color: 'white' }}>
              第 {questionNumber} 题
            </span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Tag
              color={getDifficultyColor(question.difficulty)}
              style={{ margin: 0, fontSize: '12px' }}
            >
              {getDifficultyText(question.difficulty)}
            </Tag>
            <Tag color="blue" style={{ margin: 0, fontSize: '12px' }}>
              {question.points}分
            </Tag>
            {showResult && (
              <Tag
                color={isCorrect ? 'success' : 'error'}
                icon={isCorrect ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                style={{ margin: 0, fontSize: '12px' }}
              >
                {isCorrect ? '正确' : '错误'}
              </Tag>
            )}
          </div>
        </div>
      </div>

      {/* 题目内容 */}
      <div style={{ padding: '0 20px', marginBottom: '20px' }}>
        <div style={{
          fontSize: '16px',
          lineHeight: '1.8',
          color: '#333',
          background: '#fafafa',
          padding: '16px',
          borderRadius: '8px',
          borderLeft: '4px solid #1890ff'
        }}>
          {question.question}
        </div>
      </div>

      {/* 选项区域 */}
      <div style={{ padding: '0 20px', marginBottom: '20px' }}>
        {question.type === 'multiple-choice' && (
          <Radio.Group
            value={selectedAnswer}
            onChange={(e) => onAnswerChange(e.target.value)}
            disabled={showResult}
            style={{ width: '100%' }}
          >
            <Row gutter={[12, 12]}>
              {question.options?.map((option, index) => {
                const isThisCorrect = option === question.correctAnswer;
                const isThisSelected = option === selectedAnswer;

                let radioStyle: React.CSSProperties = {
                  padding: '12px 16px',
                  borderRadius: '8px',
                  border: '2px solid #e8e8e8',
                  width: '100%',
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  transition: 'all 0.3s ease',
                  cursor: showResult ? 'default' : 'pointer',
                  minHeight: '60px'
                };

                if (showResult) {
                  if (isThisCorrect) {
                    radioStyle.backgroundColor = '#f6ffed';
                    radioStyle.borderColor = '#52c41a';
                    radioStyle.boxShadow = '0 2px 8px rgba(82, 196, 26, 0.2)';
                  } else if (isThisSelected && !isThisCorrect) {
                    radioStyle.backgroundColor = '#fff2f0';
                    radioStyle.borderColor = '#ff4d4f';
                    radioStyle.boxShadow = '0 2px 8px rgba(255, 77, 79, 0.2)';
                  }
                } else {
                  if (isThisSelected) {
                    radioStyle.borderColor = '#1890ff';
                    radioStyle.backgroundColor = '#f0f9ff';
                    radioStyle.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.2)';
                  }
                }

                // 根据选项数量决定列数
                const colSpan = question.options && question.options.length <= 2 ? 12 :
                               question.options && question.options.length <= 4 ? 12 : 8;

                return (
                  <Col span={colSpan} key={index}>
                    <Radio
                      value={option}
                      style={radioStyle}
                    >
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        width: '100%'
                      }}>
                        <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                          <span style={{
                            display: 'inline-block',
                            width: '24px',
                            height: '24px',
                            borderRadius: '50%',
                            background: isThisSelected && !showResult ? '#1890ff' :
                                       showResult && isThisCorrect ? '#52c41a' :
                                       showResult && isThisSelected && !isThisCorrect ? '#ff4d4f' : '#d9d9d9',
                            color: 'white',
                            textAlign: 'center',
                            lineHeight: '24px',
                            fontSize: '12px',
                            fontWeight: 'bold',
                            marginRight: '8px',
                            flexShrink: 0
                          }}>
                            {String.fromCharCode(65 + index)}
                          </span>
                          <span style={{
                            fontSize: '14px',
                            color: '#333',
                            wordBreak: 'break-word',
                            lineHeight: '1.4'
                          }}>
                            {option}
                          </span>
                        </div>
                        <div style={{ marginLeft: '8px', flexShrink: 0 }}>
                          {showResult && isThisCorrect && (
                            <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
                          )}
                          {showResult && isThisSelected && !isThisCorrect && (
                            <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
                          )}
                        </div>
                      </div>
                    </Radio>
                  </Col>
                );
              })}
            </Row>
          </Radio.Group>
        )}
      </div>

      {/* 答案解析区域 */}
      {showResult && question.explanation && (
        <div style={{ padding: '0 20px 20px 20px' }}>
          <div style={{
            background: isCorrect ? 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)' : 'linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%)',
            border: `2px solid ${isCorrect ? '#52c41a' : '#ff4d4f'}`,
            borderRadius: '8px',
            padding: '16px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '12px',
              fontSize: '16px',
              fontWeight: 'bold',
              color: isCorrect ? '#52c41a' : '#ff4d4f'
            }}>
              {isCorrect ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
              <span style={{ marginLeft: '8px' }}>答案解析</span>
            </div>
            <div style={{
              fontSize: '15px',
              lineHeight: '1.6',
              color: '#333'
            }}>
              {question.explanation}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default QuestionComponent;
