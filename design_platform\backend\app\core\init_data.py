"""
初始化模拟数据
"""
from sqlalchemy.orm import Session
from datetime import datetime, timedelta

from app.core.database import SessionLocal
from app.models.experiment import (
    Experiment, Dataset, ExperimentStatus,
    ModelType
)


def create_sample_datasets(db: Session):
    """创建示例数据集"""
    datasets = [
        {
            "name": "Assist0910",
            "display_name": "ASSISTments 2009-2010",
            "description": "ASSISTments 2009-2010 数据集，包含学生在线学习系统的交互记录",
            "data_path": "/app/data/datasets/Assist0910",
            "config_path": "/app/data/datasets/Assist0910/config.json",
            "student_num": 4163,
            "exercise_num": 17751,
            "knowledge_num": 123,
            "response_num": 325637,
            "is_active": True,
            "is_demo": True
        },
        {
            "name": "Assist17",
            "display_name": "ASSISTments 2017",
            "description": "ASSISTments 2017 数据集，更新版本的在线学习数据",
            "data_path": "/app/data/datasets/Assist17",
            "config_path": "/app/data/datasets/Assist17/config.json",
            "student_num": 1709,
            "exercise_num": 3162,
            "knowledge_num": 102,
            "response_num": 942816,
            "is_active": True,
            "is_demo": False
        },
        {
            "name": "Junyi",
            "display_name": "Junyi Academy",
            "description": "Junyi Academy 数学学习数据集，包含学生数学练习记录",
            "data_path": "/app/data/datasets/Junyi",
            "config_path": "/app/data/datasets/Junyi/config.json",
            "student_num": 10000,
            "exercise_num": 835,
            "knowledge_num": 39,
            "response_num": 25925,
            "is_active": True,
            "is_demo": False
        },
        {
            "name": "NeurIPS2020",
            "display_name": "NeurIPS 2020 Education Challenge",
            "description": "NeurIPS 2020 教育挑战赛数据集，包含层次化知识结构",
            "data_path": "/app/data/datasets/NeurIPS2020",
            "config_path": "/app/data/datasets/NeurIPS2020/config.json",
            "student_num": 948,
            "exercise_num": 948,
            "knowledge_num": 388,
            "response_num": 1382727,
            "is_active": True,
            "is_demo": False
        }
    ]
    
    for dataset_data in datasets:
        # 检查是否已存在
        existing = db.query(Dataset).filter(
            Dataset.name == dataset_data["name"]
        ).first()
        
        if not existing:
            dataset = Dataset(**dataset_data)
            db.add(dataset)
    
    db.commit()


def create_sample_experiments(db: Session):
    """创建示例实验"""
    datasets = db.query(Dataset).all()
    if not datasets:
        return
    
    experiments = [
        {
            "name": "ORCDF vs NCDM 对比实验",
            "description": "在Assist0910数据集上对比ORCDF和NCDM模型性能",
            "dataset_id": datasets[0].id,
            "dataset_type": "Assist0910",
            "model_type": ModelType.ORCDF,
            "status": ExperimentStatus.COMPLETED,
            "progress": 100.0,
            "metrics": {
                "accuracy": 0.7845,
                "auc": 0.8234,
                "rmse": 0.4123
            },
            "config": {
                "learning_rate": 0.001,
                "batch_size": 256,
                "epochs": 100,
                "hidden_dim": 512
            },
            "created_at": datetime.now() - timedelta(days=2),
            "completed_at": datetime.now() - timedelta(days=2, hours=2)
        },
        {
            "name": "KANCD 基准测试",
            "description": "KANCD模型在多个数据集上的基准测试",
            "dataset_id": datasets[1].id,
            "dataset_type": "Assist17",
            "model_type": ModelType.KANCD,
            "status": ExperimentStatus.COMPLETED,
            "progress": 100.0,
            "metrics": {
                "accuracy": 0.7623,
                "auc": 0.8156,
                "rmse": 0.4287
            },
            "config": {
                "learning_rate": 0.002,
                "batch_size": 128,
                "epochs": 80
            },
            "created_at": datetime.now() - timedelta(days=1),
            "completed_at": datetime.now() - timedelta(days=1, hours=1)
        },
        {
            "name": "过度平滑分析实验",
            "description": "分析ORCDF模型在处理过度平滑问题上的效果",
            "dataset_id": datasets[2].id,
            "dataset_type": "Junyi",
            "model_type": ModelType.ORCDF,
            "status": ExperimentStatus.RUNNING,
            "progress": 65.0,
            "config": {
                "learning_rate": 0.0015,
                "batch_size": 512,
                "epochs": 120,
                "regularization": 0.01
            },
            "created_at": datetime.now() - timedelta(hours=6),
            "started_at": datetime.now() - timedelta(hours=6)
        },
        {
            "name": "超参数调优实验",
            "description": "对ORCDF模型进行网格搜索超参数调优",
            "dataset_id": datasets[0].id,
            "dataset_type": "Assist0910",
            "model_type": ModelType.ORCDF,
            "status": ExperimentStatus.RUNNING,
            "progress": 23.0,
            "config": {
                "learning_rate_range": [0.001, 0.01],
                "batch_size_options": [128, 256, 512],
                "epochs": 50
            },
            "created_at": datetime.now() - timedelta(hours=2),
            "started_at": datetime.now() - timedelta(hours=2)
        },
        {
            "name": "NCDM 复现实验",
            "description": "复现NCDM论文中的实验结果",
            "dataset_id": datasets[3].id,
            "dataset_type": "NeurIPS2020",
            "model_type": ModelType.NCDM,
            "status": ExperimentStatus.PENDING,
            "progress": 0.0,
            "config": {
                "learning_rate": 0.001,
                "batch_size": 256,
                "epochs": 100
            },
            "created_at": datetime.now() - timedelta(minutes=30)
        }
    ]
    
    for exp_data in experiments:
        # 检查是否已存在
        existing = db.query(Experiment).filter(
            Experiment.name == exp_data["name"]
        ).first()
        
        if not existing:
            experiment = Experiment(**exp_data)
            db.add(experiment)
    
    db.commit()


def init_sample_data():
    """初始化所有示例数据"""
    db = SessionLocal()
    try:
        print("正在创建示例数据集...")
        create_sample_datasets(db)
        
        print("正在创建示例实验...")
        create_sample_experiments(db)
        
        print("示例数据创建完成！")
        
    except Exception as e:
        print(f"创建示例数据时出错: {e}")
        db.rollback()
    finally:
        db.close()


if __name__ == "__main__":
    init_sample_data()
