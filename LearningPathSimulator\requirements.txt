# Learning Path Simulator Dependencies

# Core scientific computing
numpy>=1.21.0
scipy>=1.7.0

# Data manipulation and analysis
pandas>=1.3.0

# Machine learning (optional, for advanced agents)
scikit-learn>=1.0.0

# Deep learning (optional, for neural network agents)
torch>=1.9.0

# Visualization
matplotlib>=3.4.0
seaborn>=0.11.0

# Progress bars and utilities
tqdm>=4.62.0

# Configuration and logging
pyyaml>=5.4.0

# Testing (development)
pytest>=6.2.0
pytest-cov>=2.12.0

# Documentation (development)
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# Code quality (development)
flake8>=3.9.0
black>=21.6.0
isort>=5.9.0

# Type checking (development)
mypy>=0.910
