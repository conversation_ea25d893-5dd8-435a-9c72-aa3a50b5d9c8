"""
实验管理服务
"""
import os
import json
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.experiment import Experiment, ExperimentStatus
from app.schemas.experiment import ExperimentCreate, ExperimentUpdate, ExperimentProgress
from app.core.config import settings


class ExperimentService:
    """实验服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_experiments(
        self, 
        skip: int = 0, 
        limit: int = 100, 
        status_filter: Optional[ExperimentStatus] = None
    ) -> List[Experiment]:
        """获取实验列表"""
        query = self.db.query(Experiment)
        
        if status_filter:
            query = query.filter(Experiment.status == status_filter)
        
        return query.order_by(Experiment.created_at.desc()).offset(skip).limit(limit).all()
    
    def get_experiment(self, experiment_id: int) -> Optional[Experiment]:
        """获取单个实验"""
        return self.db.query(Experiment).filter(Experiment.id == experiment_id).first()
    
    def create_experiment(self, experiment: ExperimentCreate) -> Experiment:
        """创建实验"""
        # 验证数据集和模型类型
        self._validate_experiment_config(experiment)

        # 根据dataset_type查找对应的dataset_id
        from app.services.dataset_service import DatasetService
        dataset_service = DatasetService(self.db)
        dataset = dataset_service.get_dataset_by_name(experiment.dataset_type.value)
        if not dataset:
            # 列出所有可用的数据集
            all_datasets = dataset_service.get_datasets()
            available_names = [d.name for d in all_datasets]
            raise ValueError(f"数据集 {experiment.dataset_type.value} 不存在，可用的数据集: {available_names}")

        db_experiment = Experiment(
            name=experiment.name,
            description=experiment.description,
            dataset_id=dataset.id,
            dataset_type=experiment.dataset_type,
            model_type=experiment.model_type,
            config=experiment.config.dict() if experiment.config else {},
            status=ExperimentStatus.PENDING
        )
        
        self.db.add(db_experiment)
        self.db.commit()
        self.db.refresh(db_experiment)
        
        # 创建实验目录
        self._create_experiment_directory(db_experiment.id)
        
        return db_experiment
    
    def update_experiment(
        self, 
        experiment_id: int, 
        experiment_update: ExperimentUpdate
    ) -> Optional[Experiment]:
        """更新实验"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return None
        
        update_data = experiment_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field == 'config' and value:
                # 合并配置
                current_config = db_experiment.config or {}
                current_config.update(value.dict() if hasattr(value, 'dict') else value)
                setattr(db_experiment, field, current_config)
            else:
                setattr(db_experiment, field, value)
        
        self.db.commit()
        self.db.refresh(db_experiment)
        return db_experiment
    
    def delete_experiment(self, experiment_id: int) -> bool:
        """删除实验"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return False
        
        # 如果实验正在运行，先停止
        if db_experiment.status == ExperimentStatus.RUNNING:
            self.update_experiment_status(experiment_id, ExperimentStatus.CANCELLED)
        
        # 删除实验文件
        self._cleanup_experiment_files(experiment_id)
        
        self.db.delete(db_experiment)
        self.db.commit()
        return True
    
    def update_experiment_status(
        self, 
        experiment_id: int, 
        status: ExperimentStatus,
        task_id: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """更新实验状态"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return False
        
        db_experiment.status = status
        if task_id:
            db_experiment.task_id = task_id
        if error_message:
            db_experiment.error_message = error_message
        
        # 更新时间戳
        if status == ExperimentStatus.RUNNING:
            db_experiment.started_at = datetime.utcnow()
        elif status in [ExperimentStatus.COMPLETED, ExperimentStatus.FAILED, ExperimentStatus.CANCELLED]:
            db_experiment.completed_at = datetime.utcnow()
        
        self.db.commit()
        return True

    def update_experiment_progress(self, experiment_id: int, progress: float) -> bool:
        """更新实验进度"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return False

        db_experiment.progress = progress
        self.db.commit()
        return True

    def update_experiment_results(self, experiment_id: int, results: Dict[str, Any]) -> bool:
        """更新实验结果"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return False

        db_experiment.results = results
        self.db.commit()
        return True

    def update_experiment_progress(
        self, 
        experiment_id: int, 
        progress: float,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新实验进度"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return False
        
        db_experiment.progress = progress
        
        # 更新元数据
        if metadata:
            current_config = db_experiment.config or {}
            current_config.update(metadata)
            db_experiment.config = current_config
        
        self.db.commit()
        return True
    
    def update_experiment_results(
        self, 
        experiment_id: int, 
        results: Dict[str, Any]
    ) -> bool:
        """更新实验结果"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return False
        
        db_experiment.results = results
        
        # 提取指标
        if 'metrics' in results:
            db_experiment.metrics = results['metrics']
        
        # 更新模型路径
        if 'model_path' in results:
            db_experiment.model_path = results['model_path']
        
        self.db.commit()
        return True
    
    def get_experiment_progress(self, experiment_id: int) -> Optional[Dict[str, Any]]:
        """获取实验进度信息"""
        db_experiment = self.get_experiment(experiment_id)
        if not db_experiment:
            return None

        progress_info = {
            'experiment_id': experiment_id,
            'status': db_experiment.status,
            'progress': db_experiment.progress,
            'last_updated': db_experiment.created_at.isoformat()
        }

        # 从metrics中提取进度相关信息
        if db_experiment.metrics:
            metrics = db_experiment.metrics
            if 'current_epoch' in metrics:
                progress_info['current_epoch'] = metrics['current_epoch']
            if 'total_epochs' in metrics:
                progress_info['total_epochs'] = metrics['total_epochs']
            if 'current_loss' in metrics:
                progress_info['current_loss'] = metrics['current_loss']
            if 'current_auc' in metrics:
                progress_info['best_metric'] = metrics['current_auc']

            # 如果有训练历史，提取最新的数据
            if 'training_history' in metrics:
                history = metrics['training_history']
                if 'loss' in history and history['loss']:
                    latest_epoch = max(history['loss'].keys(), key=int)
                    progress_info['current_loss'] = history['loss'][latest_epoch]
                if 'auc' in history and history['auc']:
                    latest_epoch = max(history['auc'].keys(), key=int)
                    progress_info['best_metric'] = history['auc'][latest_epoch]

        return progress_info
    
    def get_experiment_logs(self, experiment_id: int, lines: int = 100) -> List[str]:
        """获取实验日志"""
        log_file = os.path.join(settings.RESULTS_DIR, f"experiment_{experiment_id}", "training.log")
        
        if not os.path.exists(log_file):
            return []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                return all_lines[-lines:] if len(all_lines) > lines else all_lines
        except Exception as e:
            return [f"读取日志文件失败: {str(e)}"]
    
    def _validate_experiment_config(self, experiment: ExperimentCreate):
        """验证实验配置"""
        # 验证数据集类型
        valid_datasets = ['Assist0910', 'Assist17', 'Junyi', 'NeurIPS2020']
        if experiment.dataset_type not in valid_datasets:
            raise ValueError(f"不支持的数据集类型: {experiment.dataset_type}")
        
        # 验证模型类型
        valid_models = ['orcdf', 'ncdm', 'kancd', 'kscd', 'cdmfkc', 'mirt']
        if experiment.model_type not in valid_models:
            raise ValueError(f"不支持的模型类型: {experiment.model_type}")
        
        # 验证配置参数
        if experiment.config:
            config = experiment.config
            if config.epochs <= 0:
                raise ValueError("训练轮数必须大于0")
            if not 0 < config.test_size < 1:
                raise ValueError("测试集比例必须在0和1之间")
            if config.learning_rate <= 0:
                raise ValueError("学习率必须大于0")
    
    def _create_experiment_directory(self, experiment_id: int):
        """创建实验目录"""
        experiment_dir = os.path.join(settings.RESULTS_DIR, f"experiment_{experiment_id}")
        os.makedirs(experiment_dir, exist_ok=True)
        
        # 创建子目录
        subdirs = ['models', 'logs', 'plots', 'data']
        for subdir in subdirs:
            os.makedirs(os.path.join(experiment_dir, subdir), exist_ok=True)
    
    def _cleanup_experiment_files(self, experiment_id: int):
        """清理实验文件"""
        experiment_dir = os.path.join(settings.RESULTS_DIR, f"experiment_{experiment_id}")
        if os.path.exists(experiment_dir):
            import shutil
            try:
                shutil.rmtree(experiment_dir)
            except Exception as e:
                print(f"清理实验文件失败: {e}")
    
    def get_experiments_by_dataset(self, dataset_type: str) -> List[Experiment]:
        """根据数据集类型获取实验"""
        return self.db.query(Experiment).filter(
            Experiment.dataset_type == dataset_type
        ).order_by(Experiment.created_at.desc()).all()
    
    def get_experiments_by_model(self, model_type: str) -> List[Experiment]:
        """根据模型类型获取实验"""
        return self.db.query(Experiment).filter(
            Experiment.model_type == model_type
        ).order_by(Experiment.created_at.desc()).all()
    
    def get_completed_experiments(self) -> List[Experiment]:
        """获取已完成的实验"""
        return self.db.query(Experiment).filter(
            Experiment.status == ExperimentStatus.COMPLETED
        ).order_by(Experiment.completed_at.desc()).all()
    
    def get_experiment_statistics(self) -> Dict[str, Any]:
        """获取实验统计信息"""
        total_experiments = self.db.query(Experiment).count()
        
        status_counts = {}
        for status in ExperimentStatus:
            count = self.db.query(Experiment).filter(Experiment.status == status).count()
            status_counts[status.value] = count
        
        model_counts = {}
        model_results = self.db.query(
            Experiment.model_type, 
            self.db.func.count(Experiment.id)
        ).group_by(Experiment.model_type).all()
        
        for model_type, count in model_results:
            model_counts[model_type] = count
        
        dataset_counts = {}
        dataset_results = self.db.query(
            Experiment.dataset_type, 
            self.db.func.count(Experiment.id)
        ).group_by(Experiment.dataset_type).all()
        
        for dataset_type, count in dataset_results:
            dataset_counts[dataset_type] = count
        
        return {
            'total_experiments': total_experiments,
            'status_distribution': status_counts,
            'model_distribution': model_counts,
            'dataset_distribution': dataset_counts
        }
