#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析数据含义：题目级别 vs 知识点级别
"""

import numpy as np
import pickle
from scipy.sparse import vstack

def analyze_data_granularity():
    """分析数据的粒度级别"""
    print("=== 深度分析数据粒度 ===")
    
    try:
        # 加载所有数据
        with open('trn_mat.pkl', 'rb') as f:
            trn_mat = pickle.load(f)
        with open('val_mat.pkl', 'rb') as f:
            val_mat = pickle.load(f)
        with open('tst_mat.pkl', 'rb') as f:
            tst_mat = pickle.load(f)
        
        # 合并数据
        full_data = vstack([trn_mat, val_mat, tst_mat]).tocsr()
        
        print(f"完整数据集:")
        print(f"  总交互记录: {full_data.shape[0]:,}")
        print(f"  特征维度: {full_data.shape[1]:,}")
        
        # 分析用户和物品的数量关系
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings = np.array(pickle.load(f))
        with open('itm_emb_np.pkl', 'rb') as f:
            item_embeddings = np.array(pickle.load(f))
        
        print(f"\n嵌入数据:")
        print(f"  用户嵌入数量: {len(user_embeddings):,}")
        print(f"  物品嵌入数量: {len(item_embeddings):,}")
        
        # 关键分析：数据量的关系
        print(f"\n🔍 关键观察:")
        print(f"  交互记录数 ({full_data.shape[0]:,}) >> 用户数 ({len(user_embeddings):,})")
        print(f"  平均每个用户: {full_data.shape[0] / len(user_embeddings):.1f} 条交互记录")
        
        if full_data.shape[0] > len(user_embeddings) * 10:
            print(f"  → 这表明每个用户有多次交互，更可能是题目级别的数据")
        else:
            print(f"  → 这表明每个用户交互较少，更可能是知识点级别的数据")
        
        return full_data, user_embeddings, item_embeddings
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None, None, None

def analyze_interaction_patterns():
    """分析交互模式"""
    print(f"\n=== 分析交互模式 ===")
    
    try:
        # 加载数据
        with open('trn_mat.pkl', 'rb') as f:
            trn_mat = pickle.load(f)
        
        # 提取标签
        labels = np.array((trn_mat.getcol(-1) != 0).toarray(), dtype=np.float32).flatten()
        
        print(f"标签分析:")
        print(f"  正样本数: {np.sum(labels):,}")
        print(f"  负样本数: {len(labels) - np.sum(labels):,}")
        print(f"  正样本比例: {np.mean(labels):.6f}")
        
        # 分析这个比例的含义
        pos_rate = np.mean(labels)
        print(f"\n🤔 正样本比例的含义:")
        if pos_rate < 0.001:
            print(f"  极低正样本比例 ({pos_rate:.6f}) 可能表示:")
            print(f"  - 高难度题目的答题数据")
            print(f"  - 挑战性学习任务的完成数据")
            print(f"  - 稀有事件的记录数据")
        elif pos_rate < 0.1:
            print(f"  低正样本比例 ({pos_rate:.6f}) 可能表示:")
            print(f"  - 一般难度题目的答题数据")
            print(f"  - 学习任务的完成数据")
        else:
            print(f"  较高正样本比例 ({pos_rate:.6f}) 可能表示:")
            print(f"  - 简单题目的答题数据")
            print(f"  - 学习行为的参与数据")
        
        return labels
        
    except Exception as e:
        print(f"❌ 交互模式分析失败: {e}")
        return None

def compare_with_typical_datasets():
    """与典型数据集进行对比"""
    print(f"\n=== 与典型教育数据集对比 ===")
    
    print(f"📚 典型教育数据集的特征:")
    
    print(f"\n1. 题目级别数据集 (如ASSISTments):")
    print(f"   - 用户数: 几千到几万")
    print(f"   - 题目数: 几万到几十万")
    print(f"   - 交互数: 几十万到几百万")
    print(f"   - 正样本比例: 60%-80% (大部分题目能答对)")
    print(f"   - 特点: 每个用户答很多题目")
    
    print(f"\n2. 知识点级别数据集:")
    print(f"   - 用户数: 几千到几万")
    print(f"   - 知识点数: 几百到几千")
    print(f"   - 交互数: 与用户数相当或略多")
    print(f"   - 正样本比例: 20%-60% (掌握有难度)")
    print(f"   - 特点: 每个用户学习有限的知识点")
    
    print(f"\n3. 学习任务级别数据集:")
    print(f"   - 用户数: 几千到几万")
    print(f"   - 任务数: 几千到几万")
    print(f"   - 交互数: 几万到几十万")
    print(f"   - 正样本比例: 10%-40% (任务有挑战性)")
    print(f"   - 特点: 每个用户完成多个任务")
    
    print(f"\n🔍 当前数据集特征:")
    print(f"   - 用户数: 16,818")
    print(f"   - 物品数: 16,818")
    print(f"   - 交互数: 1,040,580")
    print(f"   - 正样本比例: 0.0112%")
    print(f"   - 平均每用户交互: 61.9次")
    
    print(f"\n🎯 对比分析:")
    print(f"   ✅ 交互数远大于用户数 → 符合题目级别特征")
    print(f"   ❌ 正样本比例极低 → 不符合典型题目级别")
    print(f"   ❌ 物品数等于用户数 → 不符合典型题目级别")
    print(f"   🤔 这可能是一个特殊的数据集...")

def infer_final_meaning():
    """推断最终含义"""
    print(f"\n=== 推断最终含义 ===")
    
    print(f"🔍 综合所有证据:")
    
    print(f"\n证据1: 数据规模")
    print(f"  - 100万+交互记录，1.6万用户")
    print(f"  - 平均每用户62次交互")
    print(f"  → 支持题目级别解释")
    
    print(f"\n证据2: 正样本比例")
    print(f"  - 仅0.0112%的正样本")
    print(f"  - 极低的成功率")
    print(f"  → 可能是高难度内容或特殊评估标准")
    
    print(f"\n证据3: 用户-物品对应关系")
    print(f"  - 用户数 = 物品数 = 16,818")
    print(f"  - 这种1:1关系很特殊")
    print(f"  → 可能每个用户对应一个专属的学习内容集合")
    
    print(f"\n证据4: AFM预测结果")
    print(f"  - 预测值集中在0.47-0.50之间")
    print(f"  - 个性化程度有限")
    print(f"  → 可能反映整体能力水平而非具体题目")
    
    print(f"\n🎯 最可能的解释:")
    print(f"AFM分数的含义是：")
    print(f"'用户在其个人学习轨迹中，成功完成特定学习内容的概率'")
    
    print(f"\n具体可能是：")
    print(f"1. 【最可能】用户答对与某知识点相关的题目的概率")
    print(f"   - 每个用户有自己的题目序列")
    print(f"   - AFM预测用户在当前状态下答对下一题的概率")
    print(f"   - 极低正样本比例反映题目的高难度")
    
    print(f"\n2. 【可能】用户成功掌握某知识点的概率")
    print(f"   - 每个用户有自己的知识点学习路径")
    print(f"   - AFM预测用户掌握特定知识点的概率")
    print(f"   - 低正样本比例反映掌握的困难性")
    
    print(f"\n3. 【不太可能】用户完成学习任务的概率")
    print(f"   - 数据规模太大，不太可能是任务级别")

def main():
    """主分析函数"""
    print("🔍 深度分析AFM分数含义：题目级别 vs 知识点级别")
    print("=" * 70)
    
    # 分析数据粒度
    full_data, user_emb, item_emb = analyze_data_granularity()
    
    # 分析交互模式
    labels = analyze_interaction_patterns()
    
    # 与典型数据集对比
    compare_with_typical_datasets()
    
    # 推断最终含义
    infer_final_meaning()
    
    print(f"\n" + "=" * 70)
    print(f"🎯 最终结论:")
    print(f"AFM分数最可能表示：")
    print(f"'用户答对与特定知识点相关的题目的概率'")
    print(f"")
    print(f"这意味着：")
    print(f"- 不是答对某个具体题目的概率")
    print(f"- 而是答对某类知识点题目的概率")
    print(f"- 反映了用户对该知识点的掌握程度")
    print(f"- 可以用于评估学习效果和指导学习路径")

if __name__ == "__main__":
    main()
