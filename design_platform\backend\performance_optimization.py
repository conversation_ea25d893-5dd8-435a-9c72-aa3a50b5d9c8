#!/usr/bin/env python3
"""
性能优化模块
"""
import time
import functools
import asyncio
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class PerformanceOptimizer:
    """性能优化器"""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = {}
        self.default_ttl = 300  # 5分钟
    
    def cache_result(self, ttl: int = None):
        """缓存装饰器"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
                
                # 检查缓存
                if cache_key in self.cache:
                    cache_time = self.cache_ttl.get(cache_key, 0)
                    if time.time() - cache_time < (ttl or self.default_ttl):
                        logger.info(f"缓存命中: {func.__name__}")
                        return self.cache[cache_key]
                
                # 执行函数
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 存储结果
                self.cache[cache_key] = result
                self.cache_ttl[cache_key] = time.time()
                
                logger.info(f"函数执行: {func.__name__} - {execution_time:.3f}s")
                return result
            
            return wrapper
        return decorator
    
    def async_cache_result(self, ttl: int = None):
        """异步缓存装饰器"""
        def decorator(func):
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
                
                # 检查缓存
                if cache_key in self.cache:
                    cache_time = self.cache_ttl.get(cache_key, 0)
                    if time.time() - cache_time < (ttl or self.default_ttl):
                        logger.info(f"异步缓存命中: {func.__name__}")
                        return self.cache[cache_key]
                
                # 执行函数
                start_time = time.time()
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 存储结果
                self.cache[cache_key] = result
                self.cache_ttl[cache_key] = time.time()
                
                logger.info(f"异步函数执行: {func.__name__} - {execution_time:.3f}s")
                return result
            
            return wrapper
        return decorator
    
    def clear_cache(self, pattern: str = None):
        """清除缓存"""
        if pattern:
            keys_to_remove = [k for k in self.cache.keys() if pattern in k]
            for key in keys_to_remove:
                del self.cache[key]
                if key in self.cache_ttl:
                    del self.cache_ttl[key]
            logger.info(f"清除缓存: {len(keys_to_remove)} 个条目 (模式: {pattern})")
        else:
            self.cache.clear()
            self.cache_ttl.clear()
            logger.info("清除所有缓存")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'total_entries': len(self.cache),
            'cache_size_mb': sum(len(str(v)) for v in self.cache.values()) / 1024 / 1024,
            'oldest_entry': min(self.cache_ttl.values()) if self.cache_ttl else None,
            'newest_entry': max(self.cache_ttl.values()) if self.cache_ttl else None
        }

# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()

def optimize_database_queries():
    """优化数据库查询"""
    optimizations = []
    
    # 1. 添加索引建议
    index_suggestions = [
        "CREATE INDEX IF NOT EXISTS idx_datasets_name ON datasets(name);",
        "CREATE INDEX IF NOT EXISTS idx_datasets_is_active ON datasets(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_experiments_dataset_id ON experiments(dataset_id);",
        "CREATE INDEX IF NOT EXISTS idx_experiments_status ON experiments(status);",
        "CREATE INDEX IF NOT EXISTS idx_experiments_created_at ON experiments(created_at);",
    ]
    
    optimizations.extend(index_suggestions)
    
    # 2. 查询优化建议
    query_optimizations = [
        "使用 SELECT 指定字段而不是 SELECT *",
        "使用 LIMIT 限制结果集大小",
        "使用 JOIN 而不是子查询（在适当的情况下）",
        "使用批量操作而不是循环单个操作",
        "使用连接池减少连接开销"
    ]
    
    return {
        'index_suggestions': index_suggestions,
        'query_optimizations': query_optimizations,
        'total_optimizations': len(optimizations) + len(query_optimizations)
    }

def optimize_data_processing():
    """优化数据处理"""
    optimizations = []
    
    # 1. 数据加载优化
    data_loading_tips = [
        "使用 pandas.read_csv 的 chunksize 参数处理大文件",
        "使用 numpy 数组而不是 Python 列表进行数值计算",
        "使用 joblib 或 multiprocessing 进行并行处理",
        "预先分配数组大小避免动态扩展",
        "使用内存映射文件处理超大数据集"
    ]
    
    # 2. 算法优化
    algorithm_tips = [
        "使用向量化操作替代循环",
        "缓存重复计算的结果",
        "使用稀疏矩阵处理稀疏数据",
        "使用适当的数据类型（如 float32 而不是 float64）",
        "实现增量学习避免重新训练整个模型"
    ]
    
    return {
        'data_loading_tips': data_loading_tips,
        'algorithm_tips': algorithm_tips,
        'total_tips': len(data_loading_tips) + len(algorithm_tips)
    }

def optimize_api_responses():
    """优化API响应"""
    optimizations = []
    
    # 1. 响应优化
    response_tips = [
        "使用分页减少单次响应数据量",
        "实现数据压缩（gzip）",
        "使用异步处理长时间运行的任务",
        "实现结果缓存避免重复计算",
        "使用流式响应处理大数据"
    ]
    
    # 2. 序列化优化
    serialization_tips = [
        "使用 orjson 替代标准 json 库",
        "预先转换数据类型避免序列化错误",
        "使用 Pydantic 模型确保数据一致性",
        "实现自定义序列化器处理复杂对象",
        "避免序列化大型数组，使用引用或分块"
    ]
    
    return {
        'response_tips': response_tips,
        'serialization_tips': serialization_tips,
        'total_tips': len(response_tips) + len(serialization_tips)
    }

def generate_performance_report():
    """生成性能优化报告"""
    db_opts = optimize_database_queries()
    data_opts = optimize_data_processing()
    api_opts = optimize_api_responses()
    cache_stats = performance_optimizer.get_cache_stats()
    
    report = {
        'timestamp': time.time(),
        'database_optimizations': db_opts,
        'data_processing_optimizations': data_opts,
        'api_optimizations': api_opts,
        'cache_statistics': cache_stats,
        'summary': {
            'total_database_optimizations': db_opts['total_optimizations'],
            'total_data_processing_tips': data_opts['total_tips'],
            'total_api_tips': api_opts['total_tips'],
            'cache_entries': cache_stats['total_entries']
        }
    }
    
    return report

if __name__ == "__main__":
    print("生成性能优化报告...")
    
    report = generate_performance_report()
    
    print(f"\n=== 性能优化报告 ===")
    print(f"数据库优化建议: {report['summary']['total_database_optimizations']} 项")
    print(f"数据处理优化建议: {report['summary']['total_data_processing_tips']} 项")
    print(f"API优化建议: {report['summary']['total_api_tips']} 项")
    print(f"当前缓存条目: {report['summary']['cache_entries']} 个")
    
    print(f"\n=== 数据库索引建议 ===")
    for suggestion in report['database_optimizations']['index_suggestions']:
        print(f"  {suggestion}")
    
    print(f"\n=== 数据处理优化 ===")
    for tip in report['data_processing_optimizations']['data_loading_tips'][:3]:
        print(f"  • {tip}")
    
    print(f"\n=== API响应优化 ===")
    for tip in report['api_optimizations']['response_tips'][:3]:
        print(f"  • {tip}")
    
    print("\n性能优化报告生成完成！")
