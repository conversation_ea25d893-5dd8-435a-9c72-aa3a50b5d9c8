#!/usr/bin/env python3
"""
检查数据库中的准确率数据
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'design_platform', 'backend'))

from app.core.database import SessionLocal
from app.models.experiment import Experiment
import json

def check_database_accuracy():
    """检查数据库中的准确率数据"""
    
    db = SessionLocal()
    
    try:
        # 获取所有实验
        experiments = db.query(Experiment).all()
        
        print(f"🔍 数据库中有 {len(experiments)} 个实验")
        
        for exp in experiments:
            print(f"\n实验 {exp.id}: {exp.name}")
            print(f"  状态: {exp.status}")
            
            if exp.metrics:
                metrics = exp.metrics
                accuracy = metrics.get('accuracy')
                auc = metrics.get('auc')
                rmse = metrics.get('rmse')
                
                print(f"  数据库中的metrics:")
                print(f"    accuracy: {accuracy} (类型: {type(accuracy)})")
                print(f"    auc: {auc} (类型: {type(auc)})")
                print(f"    rmse: {rmse} (类型: {type(rmse)})")
                
                if accuracy is not None:
                    if 0 <= accuracy <= 1:
                        print(f"    ✅ accuracy格式正确 (0-1): {accuracy}")
                        print(f"    📊 应显示为: {accuracy * 100:.1f}%")
                    else:
                        print(f"    ❌ accuracy格式错误: {accuracy}")
            else:
                print(f"  ❌ 没有metrics数据")
                
            # 检查results字段
            if exp.results:
                results = exp.results
                print(f"  results字段:")
                if 'metrics' in results:
                    results_metrics = results['metrics']
                    results_accuracy = results_metrics.get('accuracy')
                    print(f"    results.metrics.accuracy: {results_accuracy}")
                else:
                    print(f"    results中没有metrics")
            else:
                print(f"  没有results数据")
                
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 检查数据库中的准确率数据...")
    check_database_accuracy()
    print("\n🏁 检查完成!")
