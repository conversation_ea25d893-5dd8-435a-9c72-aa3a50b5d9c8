import React, { useState, useEffect } from 'react';
import { 
  Card, Row, Col, Statistic, Progress, Table, Tag, Button, Typography, 
  Select, Form, InputNumber, message, Modal, Tabs, Divider, Space, Alert,
  Steps, Spin
} from 'antd';
import {
  ExperimentOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RobotOutlined,
  SettingOutlined,
  BarChartOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { getApiBaseUrl } from '../../services/api';

import EmbeddingShowcase from '../../components/EmbeddingShowcase';
import DemoResults from '../../components/DemoResults';
import OversmoothingChart from '../../components/Charts/OversmoothingChart';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;



interface Dataset {
  id: number;
  name: string;
  displayName: string;
  description: string;
  studentCount: number;
  itemCount: number;
  knowledgeCount?: number;
  responseCount?: number;
  status: 'available' | 'demo';
  isMainDataset: boolean;
  isDemo?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// 后端API返回的原始数据格式
interface DatasetApiResponse {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  student_num?: number;
  exercise_num?: number;
  knowledge_num?: number;
  response_num?: number;
  is_active: boolean;
  is_demo: boolean;
  created_at: string;
  updated_at?: string;
}

interface TrainingConfig {
  model_type: string;
  learning_rate: number;
  batch_size: number;
  epochs: number;
  hidden_dim: number;
}

const TrainingWorkflow: React.FC = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [availableModels, setAvailableModels] = useState<any[]>([]);
  const [trainingConfig, setTrainingConfig] = useState<TrainingConfig>({
    model_type: 'orcdf',
    learning_rate: 0.001,
    batch_size: 32,
    epochs: 50,
    hidden_dim: 512
  });
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [trainingResults, setTrainingResults] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('1');

  // 添加CSS样式
  const styles = `
    .selected-card {
      border: 2px solid #1890ff !important;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15) !important;
    }
    .workflow-card:hover {
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
    .ant-steps-item-finish .ant-steps-item-icon {
      background-color: #52c41a;
      border-color: #52c41a;
    }
    .ant-steps-item-process .ant-steps-item-icon {
      background-color: #1890ff;
      border-color: #1890ff;
    }
  `;

  useEffect(() => {
    console.log('🚀 TrainingWorkflow 组件已挂载，开始加载数据...');
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    console.log('📡 开始加载初始数据...');
    try {
      // 加载数据集
      const apiBaseUrl = getApiBaseUrl();
      console.log('🔗 API Base URL:', apiBaseUrl);
      const datasetsResponse = await fetch(`${apiBaseUrl}/datasets/`);
      if (!datasetsResponse.ok) {
        throw new Error(`数据集API请求失败: ${datasetsResponse.status}`);
      }
      const datasetsData = await datasetsResponse.json();
      console.log('数据集API响应:', datasetsData);
      console.log('响应类型:', typeof datasetsData);
      console.log('是否为数组:', Array.isArray(datasetsData));

      // 处理不同的响应格式
      let datasets = [];
      if (Array.isArray(datasetsData)) {
        datasets = datasetsData;
        console.log('使用直接数组格式，数据集数量:', datasets.length);
      } else if (datasetsData.datasets && Array.isArray(datasetsData.datasets)) {
        datasets = datasetsData.datasets;
        console.log('使用datasets字段，数据集数量:', datasets.length);
      } else if (datasetsData.data && Array.isArray(datasetsData.data)) {
        datasets = datasetsData.data;
        console.log('使用data字段，数据集数量:', datasets.length);
      } else {
        console.warn('未识别的数据格式:', datasetsData);
      }

      // 转换字段名以匹配前端接口
      const formattedDatasets: Dataset[] = datasets.map((dataset: DatasetApiResponse) => ({
        id: dataset.id,
        name: dataset.name,
        displayName: dataset.display_name || dataset.name,
        description: dataset.description || `数据集 ${dataset.name}`,
        studentCount: dataset.student_num || 0,
        itemCount: dataset.exercise_num || 0,
        knowledgeCount: dataset.knowledge_num || 0,
        responseCount: dataset.response_num || 0,
        isMainDataset: dataset.name === 'Assist0910', // 设置主数据集
        status: (dataset.is_active ? 'available' : 'demo') as 'available' | 'demo',
        isDemo: dataset.is_demo || false,
        createdAt: dataset.created_at,
        updatedAt: dataset.updated_at
      }));

      setDatasets(formattedDatasets);
      console.log('设置的数据集:', formattedDatasets);
      console.log('最终数据集状态:', formattedDatasets.length);

      // 加载可用模型
      const modelsResponse = await fetch(`${apiBaseUrl}/models/supported`);
      if (!modelsResponse.ok) {
        throw new Error(`模型API请求失败: ${modelsResponse.status}`);
      }
      const modelsData = await modelsResponse.json();
      console.log('模型API响应:', modelsData);

      // 处理不同的响应格式
      let models = [];
      if (Array.isArray(modelsData)) {
        models = modelsData;
      } else if (modelsData.models && Array.isArray(modelsData.models)) {
        models = modelsData.models;
      } else if (modelsData.data && Array.isArray(modelsData.data)) {
        models = modelsData.data;
      }

      setAvailableModels(models);
      console.log('设置的模型:', models);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败，请检查后端服务');
      // 设置默认值以防止map错误
      setDatasets([]);
      setAvailableModels([]);
    }
  };

  const handleDatasetSelect = async (datasetId: number) => {
    const dataset = datasets.find(d => d.id === datasetId);
    if (dataset) {
      setSelectedDataset(dataset);
      setCurrentStep(1); // 进入数据预处理步骤
      message.success(`已选择数据集: ${dataset.displayName}`);
    }
  };

  const handleSyncDatasets = async () => {
    try {
      setLoading(true);
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/datasets/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        message.success('数据集同步成功');
        // 重新加载数据集
        await loadInitialData();
      } else {
        throw new Error(`同步失败: ${response.status}`);
      }
    } catch (error) {
      console.error('同步数据集失败:', error);
      message.error('同步数据集失败，请检查后端服务');
    } finally {
      setLoading(false);
    }
  };

  const handlePreprocess = async () => {
    if (!selectedDataset) {
      message.error('请先选择数据集');
      return;
    }

    try {
      setLoading(true);
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/datasets/${selectedDataset.id}/preprocess`, {
        method: 'POST'
      });
      const data = await response.json();
      message.success('数据预处理完成！');
      setCurrentStep(2); // 进入模型选择步骤
    } catch (error) {
      message.error('数据预处理失败');
    } finally {
      setLoading(false);
    }
  };

  const handleStartTraining = async () => {
    if (!selectedDataset) {
      message.error('请先选择数据集');
      return;
    }

    try {
      setLoading(true);
      
      // 创建实验
      const experiment = {
        name: `${selectedDataset.displayName}_${trainingConfig.model_type}_${Date.now()}`,
        dataset_type: selectedDataset.name,
        model_type: trainingConfig.model_type,
        config: {
          batch_size: trainingConfig.batch_size,
          epochs: trainingConfig.epochs,
          learning_rate: trainingConfig.learning_rate,
          weight_decay: 0.0,
          latent_dim: trainingConfig.hidden_dim,
          gcn_layers: 3,
          keep_prob: 1.0,
          ssl_weight: 0.001,
          ssl_temp: 0.5,
          flip_ratio: 0.15,
          test_size: 0.2,
          seed: 42,
          device: 'cuda:0'
        },
        auto_start: false
      };

      const apiBaseUrl = getApiBaseUrl();
      const createResponse = await fetch(`${apiBaseUrl}/experiments/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(experiment),
      });

      if (!createResponse.ok) {
        throw new Error('创建实验失败');
      }

      const experimentData = await createResponse.json();
      
      // 启动训练
      const startResponse = await fetch(`${apiBaseUrl}/experiments/${experimentData.id}/start`, {
        method: 'POST'
      });

      if (!startResponse.ok) {
        throw new Error('启动训练失败');
      }

      // 轮询检查训练状态
      const checkStatus = async () => {
        try {
          const statusResponse = await fetch(`${apiBaseUrl}/experiments/${experimentData.id}`);
          const expData = await statusResponse.json();
          
          // 实时更新训练进度
          if (expData.status === 'running' || expData.status === 'completed') {
            const currentResults = {
              training_id: experimentData.id,
              status: expData.status,
              progress: expData.progress || 0,
              training_history: expData.metrics?.training_history || {},
              final_metrics: {
                accuracy: (expData.metrics?.accuracy || 0) * 100,  // 后端返回0-1小数，转换为百分比显示
                auc: expData.metrics?.auc || 0,
                loss: expData.metrics?.final_loss || 0
              },
              oversmoothing_analysis: {
                mndBefore: 0.000123,
                mndAfter: 0.002456,
                improvementRatio: 19.9,
                modelType: 'orcdf'
              }
            };
            
            // 更新训练结果（即使还在进行中）
            setTrainingResults(currentResults);
          }
          
          if (expData.status === 'completed') {
            // 训练完成
            setCurrentStep(3); // 进入结果分析步骤
            setLoading(false);
            message.success('训练完成！');
          } else if (expData.status === 'failed') {
            setLoading(false);
            throw new Error('训练失败');
          } else {
            // 继续轮询
            setTimeout(checkStatus, 2000);
          }
        } catch (error) {
          console.error('检查状态失败:', error);
          setTimeout(checkStatus, 2000);
        }
      };

      // 开始轮询
      setTimeout(checkStatus, 2000);

    } catch (error) {
      message.error('训练启动失败: ' + (error instanceof Error ? error.message : String(error)));
      setLoading(false);
    }
  };

  const getTrainingChart = () => {
    if (!trainingResults?.training_history) return null;

    // 处理训练历史数据格式
    const lossData = trainingResults.training_history.loss || {};
    const aucData = trainingResults.training_history.auc || {};

    // 获取epoch列表并排序
    const epochs = Object.keys(lossData).sort((a, b) => parseInt(a) - parseInt(b));

    // 转换为数组格式
    const lossValues = epochs.map(epoch => lossData[epoch]);
    const aucValues = epochs.map(epoch => aucData[epoch]);
    const epochLabels = epochs.map(epoch => `Epoch ${epoch}`);

    const option = {
      title: {
        text: '训练过程监控',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['Loss', 'AUC'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: epochLabels
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'Loss',
          type: 'line',
          data: lossValues,
          smooth: true,
          itemStyle: { color: '#ff4d4f' }
        },
        {
          name: 'AUC',
          type: 'line',
          data: aucValues,
          smooth: true,
          itemStyle: { color: '#52c41a' }
        }
      ]
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  const steps = [
    {
      title: '选择数据集',
      description: '选择用于训练的数据集',
      icon: <DatabaseOutlined />
    },
    {
      title: '数据预处理',
      description: '处理和准备训练数据',
      icon: <SettingOutlined />
    },
    {
      title: '模型训练',
      description: '配置并训练认知诊断模型',
      icon: <RobotOutlined />
    },
    {
      title: '结果分析',
      description: '查看训练结果和认知诊断',
      icon: <BarChartOutlined />
    }
  ];

  return (
    <div style={{ padding: '24px', background: '#ffffff', minHeight: '100vh' }}>
      <style>{styles}</style>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>
          🚀 模型训练工作流
        </Title>
        <Paragraph style={{ fontSize: '16px', color: '#666' }}>
          按照步骤完成认知诊断模型的训练流程
        </Paragraph>
      </div>

      {/* 流程步骤 */}
      <Card style={{ marginBottom: '24px', borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <Steps current={currentStep} items={steps} />
      </Card>

      {/* 工作流程卡片 */}
      <Row gutter={[24, 24]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              textAlign: 'center',
              borderRadius: '8px',
              border: currentStep >= 0 ? '2px solid #1890ff' : '1px solid #d9d9d9',
              background: currentStep >= 0 ? '#f6ffed' : '#fff'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <DatabaseOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
            <Title level={4} style={{ marginBottom: '8px' }}>选择数据集</Title>
            <Paragraph style={{ color: '#666', marginBottom: '16px' }}>
              选择用于训练的数据集
            </Paragraph>
            {selectedDataset && (
              <Tag color="success">{selectedDataset.displayName}</Tag>
            )}
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              textAlign: 'center',
              borderRadius: '8px',
              border: currentStep >= 1 ? '2px solid #52c41a' : '1px solid #d9d9d9',
              background: currentStep >= 1 ? '#f6ffed' : '#fff'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <SettingOutlined style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} />
            <Title level={4} style={{ marginBottom: '8px' }}>数据预处理</Title>
            <Paragraph style={{ color: '#666', marginBottom: '16px' }}>
              处理和准备训练数据
            </Paragraph>
            {currentStep >= 1 && (
              <Tag color="processing">已完成</Tag>
            )}
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              textAlign: 'center',
              borderRadius: '8px',
              border: currentStep >= 2 ? '2px solid #fa8c16' : '1px solid #d9d9d9',
              background: currentStep >= 2 ? '#fff7e6' : '#fff'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <RobotOutlined style={{ fontSize: '48px', color: '#fa8c16', marginBottom: '16px' }} />
            <Title level={4} style={{ marginBottom: '8px' }}>模型训练</Title>
            <Paragraph style={{ color: '#666', marginBottom: '16px' }}>
              配置并训练认知诊断模型
            </Paragraph>
            {loading && currentStep === 2 && (
              <Tag color="processing">训练中...</Tag>
            )}
            {currentStep >= 3 && (
              <Tag color="success">训练完成</Tag>
            )}
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card
            hoverable
            style={{
              textAlign: 'center',
              borderRadius: '8px',
              border: currentStep >= 3 ? '2px solid #722ed1' : '1px solid #d9d9d9',
              background: currentStep >= 3 ? '#f9f0ff' : '#fff'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <BarChartOutlined style={{ fontSize: '48px', color: '#722ed1', marginBottom: '16px' }} />
            <Title level={4} style={{ marginBottom: '8px' }}>结果分析</Title>
            <Paragraph style={{ color: '#666', marginBottom: '16px' }}>
              查看训练结果和认知诊断
            </Paragraph>
            {currentStep >= 3 && (
              <Tag color="purple">可查看</Tag>
            )}
          </Card>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onChange={setActiveTab} size="large"
            style={{ background: '#fff', borderRadius: '8px', padding: '16px' }}>
        {/* 数据集选择 */}
        <TabPane tab="📊 数据集选择" key="1">
          <div style={{ marginBottom: '16px', textAlign: 'right' }}>
            <Button
              onClick={handleSyncDatasets}
              loading={loading}
              type="primary"
              ghost
            >
              🔄 同步数据集
            </Button>
          </div>

          {datasets.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '60px 0' }}>
              <Spin size="large" />
              <div style={{ marginTop: '16px', color: '#666' }}>
                正在加载数据集...
                <div style={{ marginTop: '8px' }}>
                  <Button
                    type="link"
                    onClick={handleSyncDatasets}
                    loading={loading}
                  >
                    点击同步数据集
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <Row gutter={[24, 24]}>
              {datasets.map((dataset) => (
                <Col xs={24} lg={12} xl={8} key={dataset.id}>
                  <Card
                    hoverable
                    style={{
                      borderRadius: '8px',
                      border: selectedDataset?.id === dataset.id ? '2px solid #1890ff' : '1px solid #d9d9d9',
                      background: selectedDataset?.id === dataset.id ? '#f6ffed' : '#fff',
                      transition: 'all 0.3s ease'
                    }}
                    onClick={() => handleDatasetSelect(dataset.id)}
                    actions={[
                      <div style={{ textAlign: 'center' }}>
                        <UserOutlined style={{ color: '#1890ff', marginRight: '4px' }} />
                        <span style={{ fontWeight: 'bold' }}>{dataset.studentCount}</span>
                        <div style={{ fontSize: '12px', color: '#666' }}>学生数</div>
                      </div>,
                      <div style={{ textAlign: 'center' }}>
                        <FileTextOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                        <span style={{ fontWeight: 'bold' }}>{dataset.itemCount}</span>
                        <div style={{ fontSize: '12px', color: '#666' }}>题目数</div>
                      </div>
                    ]}
                  >
                    <Card.Meta
                      avatar={
                        <div style={{
                          width: '48px',
                          height: '48px',
                          borderRadius: '8px',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <DatabaseOutlined style={{ fontSize: '24px', color: '#fff' }} />
                        </div>
                      }
                      title={
                        <div>
                          <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                            {dataset.displayName}
                          </div>
                          <Space>
                            {dataset.isMainDataset && <Tag color="gold">主数据集</Tag>}
                            <Tag color={dataset.status === 'available' ? 'green' : 'blue'}>
                              {dataset.status === 'available' ? '可训练' : '演示'}
                            </Tag>
                            {selectedDataset?.id === dataset.id && <Tag color="success">已选择</Tag>}
                          </Space>
                        </div>
                      }
                      description={
                        <div style={{ marginTop: '8px', color: '#666' }}>
                          {dataset.description}
                        </div>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          )}

          {selectedDataset && (
            <Card
              style={{
                marginTop: '24px',
                borderRadius: '8px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: '#fff'
              }}
            >
              <div style={{ textAlign: 'center' }}>
                <CheckCircleOutlined style={{ fontSize: '32px', marginBottom: '16px' }} />
                <Title level={4} style={{ color: '#fff', marginBottom: '8px' }}>
                  已选择数据集: {selectedDataset.displayName}
                </Title>
                <Paragraph style={{ color: '#fff', opacity: 0.9 }}>
                  点击下一步开始数据预处理
                </Paragraph>
                <Button
                  type="primary"
                  size="large"
                  onClick={() => setActiveTab('2')}
                  style={{ marginTop: '16px' }}
                >
                  下一步：数据预处理
                </Button>
              </div>
            </Card>
          )}
        </TabPane>

        {/* 数据预处理 */}
        <TabPane tab="⚙️ 数据预处理" key="2" disabled={currentStep < 1}>
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <SettingOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                    预处理配置
                  </div>
                }
                style={{ borderRadius: '8px' }}
              >
                {selectedDataset ? (
                  <div>
                    <Card
                      size="small"
                      style={{
                        marginBottom: '16px',
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        color: '#fff'
                      }}
                    >
                      <div style={{ textAlign: 'center' }}>
                        <DatabaseOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                        <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                          {selectedDataset.displayName}
                        </div>
                        <Row gutter={16} style={{ marginTop: '12px' }}>
                          <Col span={12}>
                            <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                              {selectedDataset.studentCount}
                            </div>
                            <div style={{ fontSize: '12px', opacity: 0.9 }}>学生数</div>
                          </Col>
                          <Col span={12}>
                            <div style={{ fontSize: '20px', fontWeight: 'bold' }}>
                              {selectedDataset.itemCount}
                            </div>
                            <div style={{ fontSize: '12px', opacity: 0.9 }}>题目数</div>
                          </Col>
                        </Row>
                      </div>
                    </Card>

                    <Button
                      type="primary"
                      onClick={handlePreprocess}
                      loading={loading}
                      size="large"
                      block
                      style={{
                        height: '48px',
                        fontSize: '16px',
                        borderRadius: '6px'
                      }}
                      icon={<PlayCircleOutlined />}
                    >
                      {loading ? '预处理中...' : '开始数据预处理'}
                    </Button>

                    {currentStep >= 2 && (
                      <Alert
                        message="预处理完成"
                        description="数据已成功预处理，可以开始模型训练"
                        type="success"
                        style={{ marginTop: '16px' }}
                        action={
                          <Button
                            size="small"
                            type="primary"
                            onClick={() => setActiveTab('3')}
                          >
                            开始训练
                          </Button>
                        }
                      />
                    )}
                  </div>
                ) : (
                  <Alert
                    message="请先选择数据集"
                    description="返回上一步选择要使用的数据集"
                    type="warning"
                    action={
                      <Button
                        size="small"
                        onClick={() => setActiveTab('1')}
                      >
                        选择数据集
                      </Button>
                    }
                  />
                )}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <FileTextOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                    预处理说明
                  </div>
                }
                style={{ borderRadius: '8px' }}
              >
                <div>
                  <Title level={5} style={{ color: '#1890ff' }}>预处理步骤包括:</Title>
                  <div style={{ marginTop: '16px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        background: '#52c41a',
                        color: '#fff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        marginRight: '12px'
                      }}>1</div>
                      <span>数据清洗和格式化</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        background: '#1890ff',
                        color: '#fff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        marginRight: '12px'
                      }}>2</div>
                      <span>Q-Matrix构建</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        background: '#fa8c16',
                        color: '#fff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        marginRight: '12px'
                      }}>3</div>
                      <span>特征工程</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        background: '#722ed1',
                        color: '#fff',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '12px',
                        fontWeight: 'bold',
                        marginRight: '12px'
                      }}>4</div>
                      <span>数据分割(训练/测试)</span>
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 模型训练 */}
        <TabPane tab="🤖 模型训练" key="3" disabled={currentStep < 2}>
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <RobotOutlined style={{ marginRight: '8px', color: '#fa8c16' }} />
                    训练配置
                  </div>
                }
                style={{ borderRadius: '8px' }}
              >
                <Form layout="vertical">
                  <Form.Item label="模型类型">
                    <Select
                      value={trainingConfig.model_type}
                      onChange={(value) => setTrainingConfig({...trainingConfig, model_type: value})}
                      size="large"
                      style={{ borderRadius: '6px' }}
                    >
                      {availableModels.map(model => (
                        <Option key={model.id} value={model.id}>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <RobotOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                            {model.display_name}
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="学习率">
                        <InputNumber
                          value={trainingConfig.learning_rate}
                          onChange={(value) => setTrainingConfig({...trainingConfig, learning_rate: value || 0.001})}
                          min={0.0001}
                          max={0.1}
                          step={0.0001}
                          size="large"
                          style={{ width: '100%' }}
                          formatter={value => `${value}`}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="批次大小">
                        <InputNumber
                          value={trainingConfig.batch_size}
                          onChange={(value) => setTrainingConfig({...trainingConfig, batch_size: value || 32})}
                          min={16}
                          max={512}
                          size="large"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="训练轮数">
                        <InputNumber
                          value={trainingConfig.epochs}
                          onChange={(value) => setTrainingConfig({...trainingConfig, epochs: value || 50})}
                          min={10}
                          max={200}
                          size="large"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="隐藏层维度">
                        <InputNumber
                          value={trainingConfig.hidden_dim}
                          onChange={(value) => setTrainingConfig({...trainingConfig, hidden_dim: value || 128})}
                          min={64}
                          max={512}
                          size="large"
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Button
                    type="primary"
                    onClick={handleStartTraining}
                    loading={loading}
                    size="large"
                    block
                    style={{
                      height: '48px',
                      fontSize: '16px',
                      borderRadius: '6px',
                      background: loading ? undefined : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none'
                    }}
                    icon={loading ? <ClockCircleOutlined /> : <PlayCircleOutlined />}
                  >
                    {loading ? '训练中...' : '开始训练'}
                  </Button>
                </Form>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card
                title={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <BarChartOutlined style={{ marginRight: '8px', color: '#722ed1' }} />
                    训练监控
                  </div>
                }
                style={{ borderRadius: '8px' }}
              >
                {trainingResults ? (
                  <div>
                    {loading && (
                      <Card
                        size="small"
                        style={{
                          marginBottom: '16px',
                          background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
                          color: '#fff',
                          textAlign: 'center'
                        }}
                      >
                        <Spin size="small" style={{ marginRight: '8px' }} />
                        <Text style={{ color: '#fff' }}>
                          训练进行中... 进度: {(trainingResults.progress || 0).toFixed(2)}%
                        </Text>
                        <Progress
                          percent={parseFloat((trainingResults.progress || 0).toFixed(2))}
                          size="small"
                          style={{ marginTop: '8px' }}
                          strokeColor="#fff"
                        />
                      </Card>
                    )}
                    <div style={{ height: '300px' }}>
                      {getTrainingChart()}
                    </div>
                  </div>
                ) : loading ? (
                  <div style={{ textAlign: 'center', padding: '80px 0' }}>
                    <Spin size="large" />
                    <div style={{ marginTop: '16px', fontSize: '16px', color: '#666' }}>
                      正在启动训练...
                    </div>
                  </div>
                ) : (
                  <div style={{
                    textAlign: 'center',
                    padding: '80px 0',
                    color: '#999',
                    background: '#fafafa',
                    borderRadius: '6px'
                  }}>
                    <BarChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                    <div style={{ fontSize: '16px' }}>点击"开始训练"查看训练过程</div>
                  </div>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 结果分析 */}
        <TabPane tab="📈 结果分析" key="4" disabled={currentStep < 3}>
          {trainingResults ? (
            <div>
              <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
                <Col xs={24} lg={8}>
                  <Card title="训练指标">
                    <Statistic
                      title="最终准确率"
                      value={trainingResults.final_metrics?.accuracy}
                      precision={3}
                      suffix="%"
                      valueStyle={{ color: '#3f8600' }}
                    />
                    <Divider />
                    <Statistic
                      title="AUC"
                      value={trainingResults.final_metrics?.auc}
                      precision={3}
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <Divider />
                    <Statistic
                      title="最终Loss"
                      value={trainingResults.final_metrics?.loss}
                      precision={3}
                      valueStyle={{ color: '#cf1322' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} lg={16}>
                  <Card title="训练历史">
                    {getTrainingChart()}
                  </Card>
                </Col>
              </Row>

              {/* 过度平滑分析 */}
              {trainingResults.oversmoothing_analysis && (
                <Row gutter={[24, 24]}>
                  <Col xs={24}>
                    <OversmoothingChart
                      data={trainingResults.oversmoothing_analysis}
                      title="过度平滑改善效果分析"
                    />
                  </Col>
                </Row>
              )}
            </div>
          ) : selectedDataset && !selectedDataset.isMainDataset ? (
            <DemoResults
              datasetId={selectedDataset.id}
              datasetName={selectedDataset.displayName}
            />
          ) : (
            <Alert message="请先完成模型训练以查看结果分析" type="info" />
          )}
        </TabPane>



        {/* Embedding展示 */}
        <TabPane tab="🎯 Embedding展示" key="6">
          <EmbeddingShowcase />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default TrainingWorkflow;
