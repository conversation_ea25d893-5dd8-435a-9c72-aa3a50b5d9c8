import React from 'react';
import { Typography } from 'antd';
import { BrainIcon } from './BrainIcon';

const { Title } = Typography;

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
  style?: React.CSSProperties;
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'medium', 
  showText = true, 
  style = {} 
}) => {
  const sizeConfig = {
    small: { iconSize: 24, fontSize: 16 },
    medium: { iconSize: 32, fontSize: 20 },
    large: { iconSize: 48, fontSize: 28 }
  };

  const config = sizeConfig[size];

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        padding: '8px 16px',
        borderRadius: '12px',
        background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.05) 0%, rgba(114, 46, 209, 0.05) 100%)',
        border: '1px solid rgba(24, 144, 255, 0.1)',
        boxShadow: '0 2px 8px rgba(24, 144, 255, 0.1)',
        transition: 'all 0.3s ease',
        cursor: 'pointer',
        ...style
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-2px)';
        e.currentTarget.style.boxShadow = '0 4px 16px rgba(24, 144, 255, 0.2)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(24, 144, 255, 0.1)';
      }}
    >
      <BrainIcon size={config.iconSize} />
      {showText && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
          <Title
            level={size === 'large' ? 2 : size === 'medium' ? 3 : 4}
            style={{
              margin: 0,
              background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 50%, #40a9ff 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              fontSize: config.fontSize,
              fontWeight: 'bold',
              letterSpacing: '0.5px'
            }}
          >
            知擎EduBrain
          </Title>
          <div style={{
            fontSize: size === 'large' ? '12px' : size === 'medium' ? '10px' : '8px',
            color: '#8c8c8c',
            fontWeight: '500',
            letterSpacing: '1px',
            textTransform: 'uppercase'
          }}>
            Cognitive Diagnosis Platform
          </div>
        </div>
      )}
    </div>
  );
};

export default Logo;
