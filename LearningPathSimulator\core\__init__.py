"""
Learning Path Simulator - Core Module

核心模块包含仿真器的基础类和数学模型实现。
"""

from .base import BaseAgent, BaseEnvironment
from .mdp import MarkovDecisionProcess
from .cognitive_models import (
    EbbinghausForgettingCurve,
    VygotskyZPD,
    SwellerCognitiveLoad,
    MetacognitionModel
)

__all__ = [
    'BaseAgent',
    'BaseEnvironment', 
    'MarkovDecisionProcess',
    'EbbinghausForgettingCurve',
    'VygotskyZPD',
    'SwellerCognitiveLoad',
    'MetacognitionModel'
]

__version__ = '1.0.0'
