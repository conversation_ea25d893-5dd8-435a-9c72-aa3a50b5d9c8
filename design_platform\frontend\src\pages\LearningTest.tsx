import React, { useState } from 'react';
import { Card, Button, Space, Typography, Divider } from 'antd';
import { BookOutlined, PlayCircleOutlined } from '@ant-design/icons';
import KnowledgePointLearning from '../components/learning/KnowledgePointLearning';
import { LearningProgress } from '../types/learning';

const { Title, Paragraph } = Typography;

const LearningTest: React.FC = () => {
  const [currentKnowledgePoint, setCurrentKnowledgePoint] = useState<string | null>(null);
  const [learningResults, setLearningResults] = useState<LearningProgress[]>([]);

  const testKnowledgePoints = [
    { id: '1', name: '几何推理' },
    { id: '2', name: '概率统计' },
    { id: '3', name: '逻辑推理' },
    { id: '4', name: '代数运算' },
    { id: '5', name: '函数理解' }
  ];

  const handleStartLearning = (id: string) => {
    setCurrentKnowledgePoint(id);
  };

  const handleLearningComplete = (progress: LearningProgress) => {
    console.log('学习完成:', progress);
    setLearningResults(prev => [...prev, progress]);
    setCurrentKnowledgePoint(null);
  };

  const handleBackToList = () => {
    setCurrentKnowledgePoint(null);
  };

  if (currentKnowledgePoint) {
    const kp = testKnowledgePoints.find(k => k.id === currentKnowledgePoint);
    return (
      <KnowledgePointLearning
        knowledgePointId={currentKnowledgePoint}
        knowledgePointName={kp?.name || '未知知识点'}
        onComplete={handleLearningComplete}
        onBack={handleBackToList}
      />
    );
  }

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto', padding: '20px' }}>
      <Card>
        <Title level={2}>
          <BookOutlined /> 学习模块测试页面
        </Title>
        <Paragraph>
          这是一个测试页面，用于验证知识点学习模块的功能。点击下面的知识点开始学习体验。
        </Paragraph>

        <Divider orientation="left">可用知识点</Divider>

        <Space direction="vertical" style={{ width: '100%' }}>
          {testKnowledgePoints.map(kp => (
            <Card 
              key={kp.id}
              size="small" 
              hoverable
              style={{ width: '100%' }}
              actions={[
                <Button 
                  type="primary" 
                  icon={<PlayCircleOutlined />}
                  onClick={() => handleStartLearning(kp.id)}
                >
                  开始学习
                </Button>
              ]}
            >
              <Card.Meta
                title={kp.name}
                description={`知识点ID: ${kp.id} - 包含概念学习、例题演示、练习题目等完整学习内容`}
              />
            </Card>
          ))}
        </Space>

        {learningResults.length > 0 && (
          <>
            <Divider orientation="left">学习记录</Divider>
            <Space direction="vertical" style={{ width: '100%' }}>
              {learningResults.map((result, index) => (
                <Card key={index} size="small">
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <strong>知识点:</strong> {testKnowledgePoints.find(k => k.id === result.knowledgePointId)?.name}
                    </div>
                    <div>
                      <strong>得分:</strong> {result.score}分
                    </div>
                    <div>
                      <strong>用时:</strong> {result.timeSpent}分钟
                    </div>
                    <div>
                      <strong>完成时间:</strong> {result.completedAt.toLocaleString()}
                    </div>
                  </div>
                </Card>
              ))}
            </Space>
          </>
        )}
      </Card>
    </div>
  );
};

export default LearningTest;
