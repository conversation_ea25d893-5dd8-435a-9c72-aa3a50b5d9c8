// 学习相关的类型定义

export interface Question {
  id: number;
  type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  tags?: string[];
}

export interface Example {
  id: number;
  title: string;
  problem: string;
  solution: string;
  difficulty: 'easy' | 'medium' | 'hard';
  tags?: string[];
}

export interface ConceptContent {
  title: string;
  content: string;
  keyPoints: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // 分钟
}

export interface LearningResource {
  id: number;
  title: string;
  type: 'video' | 'document' | 'interactive' | 'link';
  url: string;
  duration?: string;
  description: string;
  tags?: string[];
}

export interface LearningContent {
  id: string;
  name: string;
  concept: ConceptContent;
  examples: Example[];
  questions: Question[];
  resources: LearningResource[];
}

export interface LearningProgress {
  knowledgePointId: string;
  conceptRead: boolean;
  examplesViewed: boolean;
  practiceCompleted: boolean;
  score: number;
  timeSpent: number; // 分钟
  completedAt: Date;
  answers?: Record<number, string>;
}

export interface LearningSession {
  id: string;
  userId: string;
  knowledgePointId: string;
  startTime: Date;
  endTime?: Date;
  progress: LearningProgress;
  status: 'in-progress' | 'completed' | 'paused';
}

export interface LearningStatistics {
  totalTimeSpent: number;
  completedKnowledgePoints: number;
  averageScore: number;
  strongAreas: string[];
  weakAreas: string[];
  recentActivity: LearningSession[];
}

export interface KnowledgePointDetail {
  id: string;
  name: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  prerequisites: string[];
  description: string;
  learningObjectives: string[];
  estimatedTime: number;
  tags: string[];
}

export interface LearningPathItem {
  knowledgePointId: string;
  knowledgePointName: string;
  order: number;
  status: 'not-started' | 'in-progress' | 'completed';
  score?: number;
  timeSpent?: number;
  completedAt?: Date;
}

export interface PersonalizedLearningPath {
  id: string;
  userId: string;
  title: string;
  description: string;
  items: LearningPathItem[];
  totalEstimatedTime: number;
  difficulty: 'easy' | 'medium' | 'hard';
  createdAt: Date;
  updatedAt: Date;
  status: 'active' | 'completed' | 'paused';
}
