#!/usr/bin/env python3
"""
开发环境快速启动脚本
自动启动后端和前端服务
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

class DevServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.project_root = Path(__file__).parent.parent
        
    def check_dependencies(self):
        """检查依赖是否安装"""
        print("🔍 检查依赖...")
        
        # 检查 Python 依赖
        requirements_file = self.project_root / "backend" / "requirements.txt"
        if requirements_file.exists():
            try:
                import uvicorn
                import fastapi
                print("  ✅ Python 依赖已安装")
            except ImportError:
                print("  ❌ Python 依赖未安装，请运行: pip install -r backend/requirements.txt")
                return False
        
        # 检查 Node.js 依赖
        node_modules = self.project_root / "frontend" / "node_modules"
        if not node_modules.exists():
            print("  ❌ Node.js 依赖未安装，请运行: cd frontend && npm install")
            return False
        else:
            print("  ✅ Node.js 依赖已安装")
        
        return True
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        backend_dir = self.project_root / "backend"
        os.chdir(backend_dir)
        
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--reload", 
            "--host", "0.0.0.0", 
            "--port", "8000"
        ]
        
        try:
            self.backend_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 在单独线程中输出后端日志
            def log_backend():
                for line in iter(self.backend_process.stdout.readline, ''):
                    print(f"[后端] {line.rstrip()}")
            
            backend_thread = threading.Thread(target=log_backend, daemon=True)
            backend_thread.start()
            
            print("  ✅ 后端服务启动成功 (http://localhost:8000)")
            
        except Exception as e:
            print(f"  ❌ 后端服务启动失败: {e}")
            return False
        
        return True
    
    def start_frontend(self):
        """启动前端服务"""
        print("🎨 启动前端服务...")
        
        frontend_dir = self.project_root / "frontend"
        os.chdir(frontend_dir)
        
        # 检查是否是 Windows 系统
        if os.name == 'nt':
            cmd = ["npm.cmd", "start"]
        else:
            cmd = ["npm", "start"]
        
        try:
            self.frontend_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 在单独线程中输出前端日志
            def log_frontend():
                for line in iter(self.frontend_process.stdout.readline, ''):
                    print(f"[前端] {line.rstrip()}")
            
            frontend_thread = threading.Thread(target=log_frontend, daemon=True)
            frontend_thread.start()
            
            print("  ✅ 前端服务启动成功 (http://localhost:3000)")
            
        except Exception as e:
            print(f"  ❌ 前端服务启动失败: {e}")
            return False
        
        return True
    
    def stop_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止服务...")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("  ✅ 后端服务已停止")
        
        if self.frontend_process:
            self.frontend_process.terminate()
            print("  ✅ 前端服务已停止")
    
    def run(self):
        """运行开发服务器"""
        print("🚀 EduBrain 设计平台 - 开发环境启动")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            return 1
        
        # 设置信号处理器
        def signal_handler(signum, frame):
            self.stop_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动后端
            if not self.start_backend():
                return 1
            
            # 等待后端启动
            time.sleep(3)
            
            # 启动前端
            if not self.start_frontend():
                self.stop_services()
                return 1
            
            print("\n" + "=" * 50)
            print("✨ 开发环境启动完成！")
            print("📋 服务地址:")
            print("  🔗 前端应用: http://localhost:3000")
            print("  🔗 后端 API: http://localhost:8000")
            print("  🔗 API 文档: http://localhost:8000/docs")
            print("\n💡 按 Ctrl+C 停止所有服务")
            print("=" * 50)
            
            # 等待进程结束
            while True:
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ 后端服务意外停止")
                    break
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("❌ 前端服务意外停止")
                    break
                time.sleep(1)
                
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_services()
        
        return 0

def main():
    """主函数"""
    server = DevServer()
    return server.run()

if __name__ == "__main__":
    exit(main())
