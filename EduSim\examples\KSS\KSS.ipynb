{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": "{'knowledge_structures': <EduSim.Envs.shared.KSS_KES.KS.KS object at 0x000002C768145C70>, 'action_space': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']}"}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import gym\n", "from EduSim.Envs.KSS import KSSEnv, KSSAgent, kss_train_eval\n", "\n", "env: KSSEnv = gym.make(\"KSS-v2\", seed=10)\n", "agent = KSSAgent(env.action_space)\n", "\n", "env"]}, {"cell_type": "code", "execution_count": 3, "outputs": [{"data": {"text/plain": "['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']"}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["env.action_space"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 5, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Episode|    Total-E   Episode-Reward             Progress           \n", "      4000|       4000         0.134079    [00:12<00:00, 332.54it/s]   \n", "done\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Expected Reward: 0.1323220238095233\n"]}], "source": ["from longling import set_logging_info\n", "set_logging_info()\n", "kss_train_eval(\n", "    agent,\n", "    env,\n", "    max_steps=20,\n", "    max_episode_num=4000,\n", "    level=\"summary\",\n", ")\n", "print(\"done\")\n"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}