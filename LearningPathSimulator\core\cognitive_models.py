"""
认知科学模型实现

基于认知科学理论的数学模型，用于准确建模学习过程。
"""

import numpy as np
from typing import Dict, Any
from .base import BaseCognitiveModel


class EbbinghausForgettingCurve(BaseCognitiveModel):
    """
    Ebbinghaus遗忘曲线模型
    
    基于Hermann Ebbinghaus的遗忘曲线理论，建模记忆随时间的衰减。
    数学公式：M(t) = M₀ * e^(-t/τ)
    """
    
    def __init__(self, tau: float = 24.0, initial_strength: float = 1.0):
        """
        初始化遗忘曲线模型
        
        Args:
            tau: 记忆时间常数（小时）
            initial_strength: 初始记忆强度
        """
        parameters = {
            'tau': tau,
            'initial_strength': initial_strength
        }
        super().__init__('EbbinghausForgettingCurve', parameters)
    
    def apply(self, state: np.ndarray, action: int, time_elapsed: float = 1.0, **kwargs) -> float:
        """
        计算遗忘效应
        
        Args:
            state: 当前学习状态
            action: 学习动作（这里不直接使用）
            time_elapsed: 经过的时间
            
        Returns:
            遗忘因子 [0,1]
        """
        tau = self.parameters['tau']
        return np.exp(-time_elapsed / tau)
    
    def get_retention_rate(self, time_elapsed: float) -> float:
        """获取指定时间后的记忆保持率"""
        return self.apply(None, None, time_elapsed)


class VygotskyZPD(BaseCognitiveModel):
    """
    Vygotsky最近发展区(ZPD)模型
    
    基于Lev Vygotsky的最近发展区理论，建模最优学习难度。
    """
    
    def __init__(self, optimal_challenge: float = 0.2, zpd_width: float = 0.3):
        """
        初始化ZPD模型
        
        Args:
            optimal_challenge: 最优挑战度（相对于当前能力的提升）
            zpd_width: ZPD宽度参数
        """
        parameters = {
            'optimal_challenge': optimal_challenge,
            'zpd_width': zpd_width
        }
        super().__init__('VygotskyZPD', parameters)
    
    def apply(self, state: np.ndarray, action: int, task_difficulty: float, **kwargs) -> float:
        """
        计算ZPD学习效果
        
        Args:
            state: 当前学习状态
            action: 学习的知识点
            task_difficulty: 任务难度
            
        Returns:
            ZPD效应系数 [0,1]
        """
        current_ability = state[action]
        optimal_difficulty = current_ability + self.parameters['optimal_challenge']
        zpd_width = self.parameters['zpd_width']
        
        # 高斯分布建模ZPD效应
        zpd_effect = np.exp(-((task_difficulty - optimal_difficulty) ** 2) / (2 * zpd_width ** 2))
        
        return zpd_effect
    
    def get_optimal_difficulty(self, current_ability: float) -> float:
        """获取给定能力水平的最优难度"""
        return current_ability + self.parameters['optimal_challenge']


class SwellerCognitiveLoad(BaseCognitiveModel):
    """
    Sweller认知负荷理论模型
    
    基于John Sweller的认知负荷理论，建模学习容量限制。
    """
    
    def __init__(self, working_memory_capacity: float = 7.0, 
                 intrinsic_load_factor: float = 1.0,
                 extraneous_load_factor: float = 0.5):
        """
        初始化认知负荷模型
        
        Args:
            working_memory_capacity: 工作记忆容量
            intrinsic_load_factor: 内在负荷因子
            extraneous_load_factor: 外在负荷因子
        """
        parameters = {
            'capacity': working_memory_capacity,
            'intrinsic_factor': intrinsic_load_factor,
            'extraneous_factor': extraneous_load_factor
        }
        super().__init__('SwellerCognitiveLoad', parameters)
    
    def apply(self, state: np.ndarray, action: int, 
              task_complexity: float = 1.0, 
              interface_complexity: float = 0.5, **kwargs) -> float:
        """
        计算认知负荷效应
        
        Args:
            state: 当前学习状态
            action: 学习动作
            task_complexity: 任务复杂度
            interface_complexity: 界面复杂度
            
        Returns:
            学习效果系数 [0,1]
        """
        # 计算各类认知负荷
        intrinsic_load = task_complexity * self.parameters['intrinsic_factor']
        extraneous_load = interface_complexity * self.parameters['extraneous_factor']
        
        # 相关负荷（用于学习的有效负荷）
        current_mastery = state[action]
        germane_load = (1 - current_mastery) * 0.5  # 基于当前掌握程度
        
        total_load = intrinsic_load + extraneous_load + germane_load
        capacity = self.parameters['capacity']
        
        # 认知负荷效应
        if total_load <= capacity:
            return 1.0  # 负荷在容量范围内，学习效果最佳
        else:
            return capacity / total_load  # 负荷超出容量，效果下降


class MetacognitionModel(BaseCognitiveModel):
    """
    元认知模型
    
    建模学习者的元认知监控和调节能力。
    """
    
    def __init__(self, monitoring_accuracy: float = 0.8, 
                 regulation_strength: float = 0.5):
        """
        初始化元认知模型
        
        Args:
            monitoring_accuracy: 元认知监控准确性
            regulation_strength: 元认知调节强度
        """
        parameters = {
            'monitoring_accuracy': monitoring_accuracy,
            'regulation_strength': regulation_strength
        }
        super().__init__('MetacognitionModel', parameters)
    
    def apply(self, state: np.ndarray, action: int, **kwargs) -> Dict[str, float]:
        """
        应用元认知模型
        
        Args:
            state: 当前学习状态
            action: 学习动作
            
        Returns:
            元认知效应字典
        """
        actual_mastery = state[action]
        monitoring_accuracy = self.parameters['monitoring_accuracy']
        regulation_strength = self.parameters['regulation_strength']
        
        # 元认知监控：感知的掌握程度
        monitoring_noise = np.random.normal(0, 1 - monitoring_accuracy)
        perceived_mastery = np.clip(actual_mastery + monitoring_noise, 0, 1)
        
        # 元认知调节：基于感知差异的策略调整
        mastery_gap = perceived_mastery - actual_mastery
        regulation_effect = regulation_strength * mastery_gap
        
        return {
            'perceived_mastery': perceived_mastery,
            'mastery_gap': mastery_gap,
            'regulation_effect': regulation_effect,
            'confidence': 1 - abs(mastery_gap)
        }


class SpacingEffectModel(BaseCognitiveModel):
    """
    间隔效应模型
    
    基于间隔重复的学习效果增强模型。
    """
    
    def __init__(self, spacing_factor: float = 1.5, min_interval: float = 1.0):
        """
        初始化间隔效应模型
        
        Args:
            spacing_factor: 间隔因子
            min_interval: 最小间隔时间
        """
        parameters = {
            'spacing_factor': spacing_factor,
            'min_interval': min_interval
        }
        super().__init__('SpacingEffectModel', parameters)
    
    def apply(self, state: np.ndarray, action: int, 
              last_study_time: float = 0.0, current_time: float = 1.0, **kwargs) -> float:
        """
        计算间隔效应
        
        Args:
            state: 当前学习状态
            action: 学习动作
            last_study_time: 上次学习时间
            current_time: 当前时间
            
        Returns:
            间隔效应系数
        """
        time_interval = current_time - last_study_time
        min_interval = self.parameters['min_interval']
        spacing_factor = self.parameters['spacing_factor']
        
        if time_interval < min_interval:
            return 1.0  # 间隔太短，无额外效果
        
        # 对数增长的间隔效应
        spacing_effect = 1.0 + spacing_factor * np.log(time_interval / min_interval)
        
        return min(spacing_effect, 3.0)  # 限制最大效应


class FlowStateModel(BaseCognitiveModel):
    """
    心流状态模型
    
    基于Csikszentmihalyi心流理论的学习状态模型。
    """
    
    def __init__(self, flow_threshold: float = 0.1):
        """
        初始化心流模型
        
        Args:
            flow_threshold: 进入心流状态的挑战-技能平衡阈值
        """
        parameters = {
            'flow_threshold': flow_threshold
        }
        super().__init__('FlowStateModel', parameters)
    
    def apply(self, state: np.ndarray, action: int, 
              challenge_level: float, **kwargs) -> float:
        """
        计算心流状态效应
        
        Args:
            state: 当前学习状态
            action: 学习动作
            challenge_level: 挑战水平
            
        Returns:
            心流效应系数
        """
        skill_level = state[action]
        challenge_skill_balance = abs(challenge_level - skill_level)
        flow_threshold = self.parameters['flow_threshold']
        
        if challenge_skill_balance <= flow_threshold:
            # 在心流区域，学习效果最佳
            return 2.0
        else:
            # 偏离心流区域，效果递减
            return np.exp(-challenge_skill_balance / flow_threshold)


class MotivationModel(BaseCognitiveModel):
    """
    动机模型
    
    基于自我决定理论的内在动机模型。
    """
    
    def __init__(self, autonomy_weight: float = 0.33, 
                 competence_weight: float = 0.33,
                 relatedness_weight: float = 0.34):
        """
        初始化动机模型
        
        Args:
            autonomy_weight: 自主性权重
            competence_weight: 胜任感权重  
            relatedness_weight: 关联性权重
        """
        parameters = {
            'autonomy_weight': autonomy_weight,
            'competence_weight': competence_weight,
            'relatedness_weight': relatedness_weight
        }
        super().__init__('MotivationModel', parameters)
    
    def apply(self, state: np.ndarray, action: int,
              autonomy_level: float = 0.8,
              competence_level: float = None,
              relatedness_level: float = 0.6, **kwargs) -> float:
        """
        计算动机效应
        
        Args:
            state: 当前学习状态
            action: 学习动作
            autonomy_level: 自主性水平
            competence_level: 胜任感水平（默认基于当前掌握程度）
            relatedness_level: 关联性水平
            
        Returns:
            动机效应系数
        """
        if competence_level is None:
            competence_level = state[action]
        
        # 计算内在动机
        intrinsic_motivation = (
            self.parameters['autonomy_weight'] * autonomy_level +
            self.parameters['competence_weight'] * competence_level +
            self.parameters['relatedness_weight'] * relatedness_level
        )
        
        return max(0.1, intrinsic_motivation)  # 确保最小动机水平
