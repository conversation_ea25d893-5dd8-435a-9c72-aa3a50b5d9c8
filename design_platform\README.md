# EduBrain 设计平台

一个基于 React + FastAPI 的认知诊断和学习路径推荐平台。

> 🚀 **想要快速开始？** 查看 [快速启动指南](QUICK_START.md)

## 📋 项目概述

本平台提供以下核心功能：
- 🧠 认知诊断分析
- 📚 学习路径推荐
- 📊 数据可视化
- 🔄 实验管理

## 🏗️ 项目结构

```
design_platform/
├── backend/                 # FastAPI 后端服务
│   ├── app/                # 应用核心代码
│   │   ├── api/           # API 路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── schemas/       # Pydantic 模式
│   │   ├── services/      # 业务逻辑
│   │   └── main.py        # 应用入口
│   ├── data/              # 数据文件
│   ├── datasets/          # 数据集
│   ├── routes/            # 额外路由
│   ├── scripts/           # 工具脚本
│   ├── services/          # 服务层
│   ├── static/            # 静态文件
│   ├── tasks/             # 异步任务
│   ├── tests/             # 测试文件
│   ├── uploads/           # 上传文件
│   ├── requirements.txt   # Python 依赖
│   └── *.db              # SQLite 数据库
├── frontend/               # React 前端应用
│   ├── public/            # 公共资源
│   ├── src/               # 源代码
│   │   ├── components/    # React 组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API 服务
│   │   ├── types/         # TypeScript 类型
│   │   └── utils/         # 工具函数
│   ├── package.json       # Node.js 依赖
│   └── tsconfig.json      # TypeScript 配置
├── docs/                  # 项目文档
│   ├── user_guide.md      # 用户使用指南
│   ├── deployment.md      # 部署指南
│   ├── ORCDF_Evaluation_Guide.md  # ORCDF 评估指南
│   └── cognitive_diagnosis_output_format.md  # 认知诊断输出格式
├── scripts/               # 项目脚本
│   ├── clean_project.py   # 项目清理脚本
│   ├── start_dev.py       # Python 启动脚本
│   └── *.ps1             # PowerShell 脚本
├── start_dev.bat          # Windows 快速启动
└── start_dev.sh           # Linux/macOS 快速启动
```

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **Node.js**: 16+
- **npm**: 8+

### 1. 克隆项目

```bash
git clone <repository-url>
cd EduBrain/design_platform
```

### 2. 后端启动

```bash
# 进入后端目录
cd backend

# 安装 Python 依赖
pip install -r requirements.txt

# 启动后端服务
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

后端服务将在 `http://localhost:8000` 启动

### 3. 前端启动

```bash
# 进入前端目录
cd frontend

# 安装 Node.js 依赖
npm install

# 启动前端开发服务器
npm start
```

前端应用将在 `http://localhost:3000` 启动

## 🔧 开发指南

### API 文档

后端启动后，可以访问以下地址查看 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### 数据库

项目使用 SQLite 数据库，数据库文件位于：
- `backend/edubrain_platform.db` - 主数据库
- `backend/edubrain_platform.db` - 教育大脑数据库

### 环境变量

可以在 `backend/.env` 文件中配置环境变量（如果需要）。

## 📦 生产部署

### 构建前端应用

```bash
cd frontend
npm run build
```

### 启动生产服务

```bash
# 后端（生产模式）
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# 前端构建文件可以通过 Web 服务器（如 Nginx）提供服务
```

### 部署注意事项

1. 确保服务器已安装 Python 3.8+ 和 Node.js 16+
2. 配置环境变量（如果需要）
3. 设置防火墙规则开放相应端口
4. 建议使用进程管理器（如 PM2 或 systemd）管理服务

## 🛠️ 常用命令

### 后端

```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 运行测试
python -m pytest tests/
```

### 前端

```bash
# 安装依赖
npm install

# 启动开发服务器
npm start

# 构建生产版本
npm run build

# 运行测试
npm test

# 代码检查
npm run lint
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**: 确保 8000 和 3000 端口未被占用
2. **依赖安装失败**: 检查 Python 和 Node.js 版本
3. **数据库连接错误**: 确保数据库文件存在且有读写权限
4. **CORS 错误**: 检查后端 CORS 配置

### 日志查看

- 后端日志：控制台输出
- 前端日志：浏览器开发者工具

## 📚 相关文档

- [用户指南](docs/user_guide.md)
- [部署指南](docs/deployment.md)
- [EduBrain 评估指南](docs/EduBrain_Evaluation_Guide.md)
- [认知诊断输出格式](docs/cognitive_diagnosis_output_format.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

[添加许可证信息]