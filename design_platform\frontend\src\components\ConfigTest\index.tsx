import React, { useState, useEffect } from 'react';
import { Card, Button, Space, Typography, Alert, Input, message } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { getApiBaseUrl } from '../../services/api';
import { detectEnvironment, saveBackendConfig, testBackendConnection } from '../../utils/envConfig';

const { Title, Text, Paragraph } = Typography;

const ConfigTest: React.FC = () => {
  const [currentConfig, setCurrentConfig] = useState<any>(null);
  const [testUrl, setTestUrl] = useState('');
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  const refreshConfig = () => {
    const envConfig = detectEnvironment();
    const apiUrl = getApiBaseUrl();
    
    setCurrentConfig({
      ...envConfig,
      currentApiUrl: apiUrl,
      envVars: {
        REACT_APP_API_URL: process.env.REACT_APP_API_URL,
        REACT_APP_BACKEND_URL: process.env.REACT_APP_BACKEND_URL,
      },
      localStorage: {
        BACKEND_URL: localStorage.getItem('BACKEND_URL'),
      },
      runtimeConfig: (window as any).__RUNTIME_CONFIG__,
    });
  };

  useEffect(() => {
    refreshConfig();
  }, []);

  const handleTestConnection = async () => {
    if (!testUrl.trim()) {
      message.error('请输入测试URL');
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      const isConnected = await testBackendConnection(testUrl.trim());
      setTestResult(isConnected ? 'success' : 'error');
      message[isConnected ? 'success' : 'error'](
        isConnected ? '连接测试成功！' : '连接测试失败！'
      );
    } catch (error) {
      setTestResult('error');
      message.error('连接测试失败！');
    } finally {
      setTesting(false);
    }
  };

  const handleSaveConfig = () => {
    if (!testUrl.trim()) {
      message.error('请输入后端URL');
      return;
    }

    saveBackendConfig(testUrl.trim());
    message.success('配置已保存！');
    
    // 刷新配置显示
    setTimeout(() => {
      refreshConfig();
    }, 100);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={3}>🔧 API配置测试工具</Title>
      
      <Card title="当前配置状态" style={{ marginBottom: '16px' }}>
        <Button 
          icon={<ReloadOutlined />} 
          onClick={refreshConfig}
          style={{ marginBottom: '16px' }}
        >
          刷新配置
        </Button>
        
        {currentConfig && (
          <div>
            <Paragraph>
              <Text strong>当前API地址：</Text>
              <Text code>{currentConfig.currentApiUrl}</Text>
            </Paragraph>
            
            <Paragraph>
              <Text strong>是否外网访问：</Text>
              <Text type={currentConfig.isExternalAccess ? 'warning' : 'success'}>
                {currentConfig.isExternalAccess ? '是' : '否'}
              </Text>
            </Paragraph>
            
            <Paragraph>
              <Text strong>需要配置：</Text>
              <Text type={currentConfig.needsConfiguration ? 'danger' : 'success'}>
                {currentConfig.needsConfiguration ? '是' : '否'}
              </Text>
            </Paragraph>

            <details style={{ marginTop: '16px' }}>
              <summary>详细配置信息</summary>
              <pre style={{ background: '#f5f5f5', padding: '12px', marginTop: '8px' }}>
                {JSON.stringify(currentConfig, null, 2)}
              </pre>
            </details>
          </div>
        )}
      </Card>

      <Card title="测试新配置" style={{ marginBottom: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input
            placeholder="输入后端URL (如: http://168b91de.r36.cpolar.top)"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            style={{ width: '100%' }}
          />
          
          <Space>
            <Button 
              onClick={handleTestConnection}
              loading={testing}
              icon={testResult === 'success' ? <CheckCircleOutlined /> : 
                    testResult === 'error' ? <ExclamationCircleOutlined /> : undefined}
              type={testResult === 'success' ? 'primary' : 'default'}
            >
              测试连接
            </Button>
            
            <Button 
              onClick={handleSaveConfig}
              disabled={!testUrl.trim() || testResult !== 'success'}
              type="primary"
            >
              保存配置
            </Button>
          </Space>

          {testResult && (
            <Alert
              message={testResult === 'success' ? '连接成功' : '连接失败'}
              type={testResult === 'success' ? 'success' : 'error'}
              showIcon
            />
          )}
        </Space>
      </Card>

      <Alert
        message="使用说明"
        description={
          <div>
            <p>1. 查看当前配置状态，确认API地址是否正确</p>
            <p>2. 输入新的后端URL进行测试</p>
            <p>3. 测试成功后保存配置，配置会立即生效</p>
            <p>4. 无需刷新页面，新配置会自动应用到所有API请求</p>
          </div>
        }
        type="info"
        showIcon
      />
    </div>
  );
};

export default ConfigTest;
