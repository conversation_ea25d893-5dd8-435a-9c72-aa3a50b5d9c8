"""
评估指标实现

包含各种用于评估学习路径规划算法性能的指标。
"""

import numpy as np
from typing import Dict, List, Any
from abc import ABC, abstractmethod

from ..core.base import BaseMetric


class LearningEfficiencyMetric(BaseMetric):
    """学习效率指标：单位时间内的学习进步"""
    
    def __init__(self):
        super().__init__("LearningEfficiency")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        """
        计算学习效率
        
        Args:
            data: 评估数据列表，每个元素包含episode的详细信息
            
        Returns:
            学习效率值
        """
        if not data:
            return 0.0
        
        total_reward = sum(episode['total_reward'] for episode in data)
        total_steps = sum(episode['steps'] for episode in data)
        
        if total_steps == 0:
            return 0.0
        
        return total_reward / total_steps
    
    def get_description(self) -> str:
        return "学习效率：平均每步获得的奖励，反映学习速度"


class LearningEffectivenessMetric(BaseMetric):
    """学习效果指标：最终掌握程度"""
    
    def __init__(self):
        super().__init__("LearningEffectiveness")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        final_scores = [episode['final_score'] for episode in data]
        return np.mean(final_scores)
    
    def get_description(self) -> str:
        return "学习效果：平均最终掌握程度，反映学习质量"


class StabilityMetric(BaseMetric):
    """稳定性指标：性能方差"""
    
    def __init__(self):
        super().__init__("Stability")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        final_scores = [episode['final_score'] for episode in data]
        return np.std(final_scores)
    
    def get_description(self) -> str:
        return "稳定性：最终得分的标准差，值越小越稳定"


class SuccessRateMetric(BaseMetric):
    """成功率指标：完成任务的比例"""
    
    def __init__(self):
        super().__init__("SuccessRate")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        success_count = sum(1 for episode in data if episode.get('success', False))
        return success_count / len(data)
    
    def get_description(self) -> str:
        return "成功率：成功完成学习任务的回合比例"


class AverageRewardMetric(BaseMetric):
    """平均奖励指标"""
    
    def __init__(self):
        super().__init__("AverageReward")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        rewards = [episode['total_reward'] for episode in data]
        return np.mean(rewards)
    
    def get_description(self) -> str:
        return "平均奖励：每回合的平均累积奖励"


class AverageStepsMetric(BaseMetric):
    """平均步数指标"""
    
    def __init__(self):
        super().__init__("AverageSteps")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        steps = [episode['steps'] for episode in data]
        return np.mean(steps)
    
    def get_description(self) -> str:
        return "平均步数：完成任务所需的平均学习步数"


class ConvergenceSpeedMetric(BaseMetric):
    """收敛速度指标：达到目标的平均时间"""
    
    def __init__(self, target_score: float = 0.6):
        super().__init__("ConvergenceSpeed")
        self.target_score = target_score
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return float('inf')
        
        convergence_times = []
        
        for episode in data:
            if 'learning_curve' in episode:
                curve = episode['learning_curve']
                for step, score in enumerate(curve):
                    if score >= self.target_score:
                        convergence_times.append(step + 1)
                        break
                else:
                    # 未收敛
                    convergence_times.append(len(curve))
        
        return np.mean(convergence_times) if convergence_times else float('inf')
    
    def get_description(self) -> str:
        return f"收敛速度：达到目标分数{self.target_score}的平均步数"


class RobustnessMetric(BaseMetric):
    """鲁棒性指标：不同条件下的性能一致性"""
    
    def __init__(self):
        super().__init__("Robustness")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        # 按不同条件分组（如果有的话）
        conditions = {}
        for episode in data:
            condition = episode.get('condition', 'default')
            if condition not in conditions:
                conditions[condition] = []
            conditions[condition].append(episode['final_score'])
        
        if len(conditions) <= 1:
            # 只有一种条件，返回稳定性
            scores = [episode['final_score'] for episode in data]
            return 1.0 / (1.0 + np.std(scores))  # 转换为正向指标
        
        # 计算不同条件间的性能差异
        condition_means = [np.mean(scores) for scores in conditions.values()]
        between_condition_var = np.var(condition_means)
        
        # 鲁棒性 = 1 / (1 + 条件间方差)
        return 1.0 / (1.0 + between_condition_var)
    
    def get_description(self) -> str:
        return "鲁棒性：不同条件下性能的一致性，值越高越鲁棒"


class LearningRetentionMetric(BaseMetric):
    """学习保持率指标：长期记忆效果"""
    
    def __init__(self):
        super().__init__("LearningRetention")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        retention_rates = []
        
        for episode in data:
            if 'retention_test' in episode:
                initial_score = episode['final_score']
                retention_score = episode['retention_test']
                
                if initial_score > 0:
                    retention_rate = retention_score / initial_score
                    retention_rates.append(retention_rate)
        
        return np.mean(retention_rates) if retention_rates else 0.0
    
    def get_description(self) -> str:
        return "学习保持率：一段时间后知识保持的比例"


class PersonalizationEffectivenessMetric(BaseMetric):
    """个性化有效性指标：算法对不同学习者的适应性"""
    
    def __init__(self):
        super().__init__("PersonalizationEffectiveness")
    
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        if not data:
            return 0.0
        
        # 按学习者类型分组
        learner_types = {}
        for episode in data:
            learner_type = episode.get('learner_type', 'default')
            if learner_type not in learner_types:
                learner_types[learner_type] = []
            learner_types[learner_type].append(episode['final_score'])
        
        if len(learner_types) <= 1:
            return 0.0  # 无法评估个性化效果
        
        # 计算每种学习者类型的平均性能
        type_performances = []
        for learner_type, scores in learner_types.items():
            avg_performance = np.mean(scores)
            type_performances.append(avg_performance)
        
        # 个性化有效性 = 最低性能 / 最高性能
        # 值越接近1，说明对不同类型学习者都有效
        min_performance = min(type_performances)
        max_performance = max(type_performances)
        
        if max_performance == 0:
            return 0.0
        
        return min_performance / max_performance
    
    def get_description(self) -> str:
        return "个性化有效性：算法对不同类型学习者的适应程度"


class MetricCalculator:
    """指标计算器：统一管理和计算所有指标"""
    
    def __init__(self, metrics: List[BaseMetric] = None):
        """
        初始化指标计算器
        
        Args:
            metrics: 指标列表，如果为None则使用默认指标
        """
        if metrics is None:
            self.metrics = [
                LearningEfficiencyMetric(),
                LearningEffectivenessMetric(),
                StabilityMetric(),
                SuccessRateMetric(),
                AverageRewardMetric(),
                AverageStepsMetric(),
                ConvergenceSpeedMetric(),
                RobustnessMetric()
            ]
        else:
            self.metrics = metrics
    
    def calculate_all_metrics(self, data: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        计算所有指标
        
        Args:
            data: 评估数据
            
        Returns:
            指标名称到值的映射
        """
        results = {}
        
        for metric in self.metrics:
            try:
                value = metric.calculate(data)
                results[metric.name] = value
            except Exception as e:
                print(f"计算指标 {metric.name} 时出错: {e}")
                results[metric.name] = 0.0
        
        return results
    
    def add_metric(self, metric: BaseMetric):
        """添加新指标"""
        self.metrics.append(metric)
    
    def remove_metric(self, metric_name: str):
        """移除指标"""
        self.metrics = [m for m in self.metrics if m.name != metric_name]
    
    def get_metric_descriptions(self) -> Dict[str, str]:
        """获取所有指标的描述"""
        return {metric.name: metric.get_description() for metric in self.metrics}
    
    def calculate_composite_score(self, data: List[Dict[str, Any]], 
                                weights: Dict[str, float] = None) -> float:
        """
        计算综合得分
        
        Args:
            data: 评估数据
            weights: 指标权重，如果为None则使用均匀权重
            
        Returns:
            综合得分
        """
        metric_values = self.calculate_all_metrics(data)
        
        if weights is None:
            # 均匀权重
            weights = {name: 1.0 / len(metric_values) for name in metric_values.keys()}
        
        # 归一化权重
        total_weight = sum(weights.values())
        if total_weight > 0:
            weights = {k: v / total_weight for k, v in weights.items()}
        
        # 计算加权平均（注意：稳定性指标需要反向）
        composite_score = 0.0
        for metric_name, value in metric_values.items():
            weight = weights.get(metric_name, 0.0)
            
            # 对于稳定性指标，值越小越好，需要转换
            if metric_name == "Stability":
                normalized_value = 1.0 / (1.0 + value)  # 转换为正向指标
            else:
                normalized_value = value
            
            composite_score += weight * normalized_value
        
        return composite_score
