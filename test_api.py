#!/usr/bin/env python3
import requests
import json

def test_datasets_api():
    try:
        # 测试数据集API
        response = requests.get("http://localhost:8000/api/v1/datasets/")
        if response.status_code == 200:
            data = response.json()
            print("数据集API响应:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查每个数据集的统计信息
            for dataset in data:
                print(f"\n数据集: {dataset.get('name', 'Unknown')}")
                print(f"  学生数: {dataset.get('student_num', 'N/A')}")
                print(f"  题目数: {dataset.get('exercise_num', 'N/A')}")
                print(f"  记录数: {dataset.get('record_num', 'N/A')}")
        else:
            print(f"API请求失败: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_datasets_api()
