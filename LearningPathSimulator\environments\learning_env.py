"""
学习环境实现

基于认知科学理论的学习过程仿真环境。
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import random
from dataclasses import dataclass

from ..core.base import BaseEnvironment, BaseRewardFunction
from ..core.cognitive_models import (
    EbbinghausForgettingCurve, VygotskyZPD, SwellerCognitiveLoad,
    MetacognitionModel, SpacingEffectModel, FlowStateModel, MotivationModel
)


@dataclass
class LearnerProfile:
    """学习者画像"""
    learning_rate: float = 0.3          # 基础学习率
    forgetting_rate: float = 0.02       # 遗忘率
    working_memory_capacity: float = 7.0 # 工作记忆容量
    motivation_level: float = 0.8       # 动机水平
    metacognition_accuracy: float = 0.7 # 元认知准确性
    preferred_difficulty: float = 0.5   # 偏好难度
    attention_span: int = 30            # 注意力持续时间


class BaseReward(BaseRewardFunction):
    """基础奖励函数"""
    
    def __init__(self, weight: float = 2.0):
        super().__init__("BaseReward", weight)
    
    def calculate(self, old_state: np.ndarray, action: int, 
                 new_state: np.ndarray, info: Dict[str, Any]) -> float:
        improvement = new_state[action] - old_state[action]
        return improvement * self.weight


class AchievementReward(BaseRewardFunction):
    """成就奖励函数"""
    
    def __init__(self, threshold: float = 0.6, weight: float = 1.0):
        super().__init__("AchievementReward", weight)
        self.threshold = threshold
    
    def calculate(self, old_state: np.ndarray, action: int,
                 new_state: np.ndarray, info: Dict[str, Any]) -> float:
        if (old_state[action] < self.threshold and 
            new_state[action] >= self.threshold):
            return self.weight
        return 0.0


class EfficiencyReward(BaseRewardFunction):
    """效率奖励函数"""
    
    def __init__(self, weight: float = 0.5):
        super().__init__("EfficiencyReward", weight)
    
    def calculate(self, old_state: np.ndarray, action: int,
                 new_state: np.ndarray, info: Dict[str, Any]) -> float:
        overall_progress = np.mean(new_state)
        return overall_progress * self.weight


class CompletionReward(BaseRewardFunction):
    """完成奖励函数"""
    
    def __init__(self, threshold: float = 0.6, min_success: int = 3, weight: float = 5.0):
        super().__init__("CompletionReward", weight)
        self.threshold = threshold
        self.min_success = min_success
    
    def calculate(self, old_state: np.ndarray, action: int,
                 new_state: np.ndarray, info: Dict[str, Any]) -> float:
        success_count = np.sum(new_state >= self.threshold)
        if success_count >= self.min_success:
            return self.weight
        return 0.0


class EfficiencyPenalty(BaseRewardFunction):
    """效率惩罚函数"""
    
    def __init__(self, threshold: float = 0.8, weight: float = -0.2):
        super().__init__("EfficiencyPenalty", weight)
        self.threshold = threshold
    
    def calculate(self, old_state: np.ndarray, action: int,
                 new_state: np.ndarray, info: Dict[str, Any]) -> float:
        if old_state[action] > self.threshold:
            return self.weight
        return 0.0


class LearningEnvironment(BaseEnvironment):
    """
    基础学习环境
    
    实现了完整的学习过程仿真，包括：
    - 学习效果建模
    - 遗忘曲线
    - 知识依赖关系
    - 多目标奖励函数
    """
    
    def __init__(self, 
                 num_knowledge_points: int = 5,
                 learning_rate: float = 0.3,
                 forgetting_rate: float = 0.02,
                 success_threshold: float = 0.6,
                 min_success_kps: int = 3,
                 max_steps: int = 30,
                 learner_profile: Optional[LearnerProfile] = None):
        """
        初始化学习环境
        
        Args:
            num_knowledge_points: 知识点数量
            learning_rate: 学习率
            forgetting_rate: 遗忘率
            success_threshold: 成功阈值
            min_success_kps: 最少成功知识点数
            max_steps: 最大学习步数
            learner_profile: 学习者画像
        """
        config = {
            'num_knowledge_points': num_knowledge_points,
            'learning_rate': learning_rate,
            'forgetting_rate': forgetting_rate,
            'success_threshold': success_threshold,
            'min_success_kps': min_success_kps,
            'max_steps': max_steps
        }
        super().__init__(config)
        
        self.num_kps = num_knowledge_points
        self.action_space_size = num_knowledge_points
        self.learning_rate = learning_rate
        self.forgetting_rate = forgetting_rate
        self.success_threshold = success_threshold
        self.min_success_kps = min_success_kps
        self.max_steps = max_steps
        
        # 学习者画像
        self.learner_profile = learner_profile or LearnerProfile()
        
        # 知识点特性
        self.difficulty = np.random.uniform(0.2, 0.8, num_knowledge_points)
        self.complexity = np.random.uniform(0.3, 1.0, num_knowledge_points)
        
        # 依赖关系矩阵
        self.dependency_matrix = self._generate_dependency_matrix()
        
        # 奖励函数组件
        self.reward_functions = [
            BaseReward(weight=2.0),
            AchievementReward(threshold=success_threshold, weight=1.0),
            EfficiencyReward(weight=0.5),
            CompletionReward(threshold=success_threshold, min_success=min_success_kps, weight=5.0),
            EfficiencyPenalty(threshold=0.8, weight=-0.2)
        ]
        
        # 认知模型
        self.forgetting_model = EbbinghausForgettingCurve()
        
        # 状态变量
        self.current_state = None
        self.step_count = 0
        self.last_study_times = np.zeros(num_knowledge_points)
        
    def _generate_dependency_matrix(self) -> np.ndarray:
        """生成知识点依赖关系矩阵"""
        matrix = np.zeros((self.num_kps, self.num_kps))
        
        # 简单的线性依赖：后面的知识点依赖前面的
        for i in range(1, self.num_kps):
            for j in range(i):
                # 距离越近依赖越强
                dependency_strength = 0.3 * np.exp(-(i-j-1) * 0.5)
                matrix[i][j] = dependency_strength
        
        return matrix
    
    def reset(self) -> np.ndarray:
        """重置环境到初始状态"""
        # 初始掌握程度：随机但偏低
        self.current_state = np.random.uniform(0.1, 0.4, self.num_kps)
        self.step_count = 0
        self.episode_count += 1
        self.last_study_times = np.zeros(self.num_kps)
        
        return self.current_state.copy()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """执行一个学习动作"""
        if action < 0 or action >= self.num_kps:
            raise ValueError(f"无效动作: {action}")
        
        # 保存旧状态
        old_state = self.current_state.copy()
        
        # 1. 计算学习效果
        learning_effect = self._calculate_learning_effect(action)
        
        # 2. 更新状态
        self._update_state(action, learning_effect)
        
        # 3. 计算奖励
        reward = self._calculate_reward(old_state, action, self.current_state)
        
        # 4. 检查是否完成
        done = self._check_completion()
        
        # 5. 更新计数器
        self.step_count += 1
        self.last_study_times[action] = self.step_count
        
        if self.step_count >= self.max_steps:
            done = True
        
        # 6. 准备信息
        info = {
            'step': self.step_count,
            'action': action,
            'learning_effect': learning_effect,
            'success_kps': self._count_success_kps(),
            'completion_rate': self._get_completion_rate(),
            'old_state': old_state,
            'difficulty': self.difficulty[action],
            'complexity': self.complexity[action]
        }
        
        return self.current_state.copy(), reward, done, info
    
    def _calculate_learning_effect(self, action: int) -> float:
        """计算学习效果"""
        # 基础学习效果
        base_effect = self.learner_profile.learning_rate
        
        # 难度调整
        difficulty_factor = 1.0 - self.difficulty[action] * 0.5
        
        # 当前掌握程度调整（边际效应递减）
        current_mastery = self.current_state[action]
        mastery_factor = 1.0 - current_mastery * 0.7
        
        # 依赖关系调整
        dependency_factor = self._calculate_dependency_factor(action)
        
        # 遗忘效应（基于上次学习时间）
        time_since_last_study = self.step_count - self.last_study_times[action]
        forgetting_factor = self.forgetting_model.apply(
            None, None, time_since_last_study
        )
        
        # 综合学习效果
        learning_effect = (base_effect * difficulty_factor * 
                          mastery_factor * dependency_factor * forgetting_factor)
        
        # 添加随机性
        noise = np.random.normal(0, 0.05)
        learning_effect = max(0, learning_effect + noise)
        
        return learning_effect
    
    def _calculate_dependency_factor(self, action: int) -> float:
        """计算依赖因子"""
        dependencies = self.dependency_matrix[action]
        dependency_factor = 1.0
        
        for i, dep_strength in enumerate(dependencies):
            if dep_strength > 0:
                prerequisite_mastery = self.current_state[i]
                if prerequisite_mastery < 0.5:
                    penalty = dep_strength * (0.5 - prerequisite_mastery)
                    dependency_factor -= penalty
        
        return max(0.1, dependency_factor)
    
    def _update_state(self, action: int, learning_effect: float):
        """更新掌握程度状态"""
        # 学习的知识点：掌握程度提升
        self.current_state[action] = min(1.0, 
            self.current_state[action] + learning_effect)
        
        # 其他知识点：遗忘效应
        for i in range(self.num_kps):
            if i != action:
                forgetting = self.learner_profile.forgetting_rate * self.current_state[i]
                self.current_state[i] = max(0.0, 
                    self.current_state[i] - forgetting)
    
    def _calculate_reward(self, old_state: np.ndarray, action: int, 
                         new_state: np.ndarray) -> float:
        """计算总奖励"""
        total_reward = 0.0
        
        info = {
            'step': self.step_count,
            'action': action,
            'difficulty': self.difficulty[action]
        }
        
        # 计算各个奖励组件
        for reward_func in self.reward_functions:
            reward_component = reward_func.calculate(old_state, action, new_state, info)
            total_reward += reward_component
        
        return total_reward
    
    def _check_completion(self) -> bool:
        """检查是否完成学习任务"""
        success_count = self._count_success_kps()
        return success_count >= self.min_success_kps
    
    def _count_success_kps(self) -> int:
        """统计成功掌握的知识点数量"""
        return np.sum(self.current_state >= self.success_threshold)
    
    def _get_completion_rate(self) -> float:
        """获取任务完成率"""
        success_count = self._count_success_kps()
        return success_count / self.min_success_kps
    
    def get_state_info(self) -> Dict[str, Any]:
        """获取当前状态的详细信息"""
        return {
            'current_state': self.current_state.tolist(),
            'step_count': self.step_count,
            'episode_count': self.episode_count,
            'success_kps': self._count_success_kps(),
            'completion_rate': self._get_completion_rate(),
            'is_completed': self._check_completion(),
            'difficulty': self.difficulty.tolist(),
            'dependency_matrix': self.dependency_matrix.tolist(),
            'learner_profile': {
                'learning_rate': self.learner_profile.learning_rate,
                'forgetting_rate': self.learner_profile.forgetting_rate,
                'motivation_level': self.learner_profile.motivation_level
            }
        }
    
    def render(self, mode: str = 'human'):
        """渲染当前状态"""
        if mode == 'human':
            print(f"\n=== 学习状态 (步骤 {self.step_count}) ===")
            print(f"掌握程度: {[f'{x:.3f}' for x in self.current_state]}")
            print(f"成功知识点: {self._count_success_kps()}/{self.min_success_kps}")
            print(f"完成率: {self._get_completion_rate():.1%}")
            print(f"是否完成: {'是' if self._check_completion() else '否'}")


class AdvancedLearningEnvironment(LearningEnvironment):
    """
    高级学习环境
    
    在基础环境基础上增加了更多认知科学特性：
    - ZPD理论
    - 认知负荷
    - 元认知
    - 动机模型
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 高级认知模型
        self.zpd_model = VygotskyZPD()
        self.cognitive_load_model = SwellerCognitiveLoad()
        self.metacognition_model = MetacognitionModel()
        self.spacing_model = SpacingEffectModel()
        self.flow_model = FlowStateModel()
        self.motivation_model = MotivationModel()
        
        # 额外状态跟踪
        self.cognitive_load_history = []
        self.motivation_history = []
        self.flow_state_history = []
    
    def _calculate_learning_effect(self, action: int) -> float:
        """高级学习效果计算"""
        # 基础学习效果
        base_effect = super()._calculate_learning_effect(action)
        
        # ZPD效应
        zpd_effect = self.zpd_model.apply(
            self.current_state, action, self.difficulty[action]
        )
        
        # 认知负荷效应
        cognitive_load_effect = self.cognitive_load_model.apply(
            self.current_state, action, 
            task_complexity=self.complexity[action],
            interface_complexity=0.3
        )
        
        # 间隔效应
        time_since_last = self.step_count - self.last_study_times[action]
        spacing_effect = self.spacing_model.apply(
            self.current_state, action,
            last_study_time=self.last_study_times[action],
            current_time=self.step_count
        )
        
        # 心流状态效应
        flow_effect = self.flow_model.apply(
            self.current_state, action,
            challenge_level=self.difficulty[action]
        )
        
        # 动机效应
        motivation_effect = self.motivation_model.apply(
            self.current_state, action,
            autonomy_level=0.8,
            relatedness_level=0.6
        )
        
        # 记录历史
        self.cognitive_load_history.append(cognitive_load_effect)
        self.motivation_history.append(motivation_effect)
        self.flow_state_history.append(flow_effect)
        
        # 综合效果
        total_effect = (base_effect * zpd_effect * cognitive_load_effect * 
                       spacing_effect * flow_effect * motivation_effect)
        
        return max(0, total_effect)
    
    def get_state_info(self) -> Dict[str, Any]:
        """获取高级状态信息"""
        base_info = super().get_state_info()
        
        # 添加高级信息
        advanced_info = {
            'cognitive_load_avg': np.mean(self.cognitive_load_history) if self.cognitive_load_history else 0,
            'motivation_avg': np.mean(self.motivation_history) if self.motivation_history else 0,
            'flow_state_avg': np.mean(self.flow_state_history) if self.flow_state_history else 0,
            'zpd_optimal_difficulties': [
                self.zpd_model.get_optimal_difficulty(mastery) 
                for mastery in self.current_state
            ]
        }
        
        base_info.update(advanced_info)
        return base_info
