#!/usr/bin/env python3
"""
测试API集成
"""
import sys
import os
import requests
import json
import time

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

# API基础URL
API_BASE_URL = "http://localhost:8001/api/v1"

def test_api_health():
    """测试API健康状态"""
    print("=== 测试API健康状态 ===")
    
    try:
        response = requests.get(f"{API_BASE_URL.replace('/api/v1', '')}/health", timeout=5)
        if response.status_code == 200:
            print("✓ API服务正常运行")
            return True
        else:
            print(f"✗ API服务状态异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到API服务: {e}")
        return False

def test_datasets_api():
    """测试数据集API"""
    print("\n=== 测试数据集API ===")
    
    try:
        # 测试获取数据集列表
        response = requests.get(f"{API_BASE_URL}/datasets/", timeout=10)
        if response.status_code == 200:
            datasets = response.json()
            print(f"✓ 获取数据集列表成功: {len(datasets)} 个数据集")
        else:
            print(f"✗ 获取数据集列表失败: {response.status_code}")
            return False
        
        # 测试获取可用数据集
        response = requests.get(f"{API_BASE_URL}/datasets/available", timeout=10)
        if response.status_code == 200:
            available_data = response.json()
            available_datasets = available_data.get('available_datasets', [])
            training_ready = available_data.get('training_ready', [])
            print(f"✓ 获取可用数据集成功: {len(available_datasets)} 个可用, {len(training_ready)} 个训练就绪")
            
            # 测试数据集验证
            if training_ready:
                dataset_name = training_ready[0]
                response = requests.get(f"{API_BASE_URL}/datasets/{dataset_name}/validate", timeout=10)
                if response.status_code == 200:
                    validation = response.json()
                    print(f"✓ 数据集验证成功: {dataset_name} - 有效: {validation.get('valid', False)}")
                else:
                    print(f"✗ 数据集验证失败: {response.status_code}")
            
        else:
            print(f"✗ 获取可用数据集失败: {response.status_code}")
            return False
        
        # 测试同步数据集
        response = requests.post(f"{API_BASE_URL}/datasets/sync", timeout=30)
        if response.status_code == 200:
            sync_result = response.json()
            print(f"✓ 数据集同步成功: {sync_result.get('message', '')}")
        else:
            print(f"✗ 数据集同步失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 数据集API测试失败: {e}")
        return False

def test_training_data_api():
    """测试训练数据API"""
    print("\n=== 测试训练数据API ===")
    
    try:
        # 获取可用数据集
        response = requests.get(f"{API_BASE_URL}/datasets/available", timeout=10)
        if response.status_code != 200:
            print("✗ 无法获取可用数据集")
            return False
        
        available_data = response.json()
        training_ready = available_data.get('training_ready', [])
        
        if not training_ready:
            print("✗ 没有训练就绪的数据集")
            return False
        
        dataset_name = training_ready[0]
        print(f"测试数据集: {dataset_name}")
        
        # 测试获取训练数据
        response = requests.get(f"{API_BASE_URL}/datasets/{dataset_name}/training-data", timeout=30)
        if response.status_code == 200:
            training_data = response.json()
            metadata = training_data.get('metadata', {})
            print(f"✓ 获取训练数据成功: {metadata.get('display_name', dataset_name)}")
            print(f"  数据类型: {metadata.get('type', 'unknown')}")
        else:
            print(f"✗ 获取训练数据失败: {response.status_code}")
            return False
        
        # 测试获取可视化数据
        response = requests.get(f"{API_BASE_URL}/datasets/{dataset_name}/visualization-data", timeout=30)
        if response.status_code == 200:
            viz_data = response.json()
            viz_components = viz_data.get('visualization_data', {})
            print(f"✓ 获取可视化数据成功: {len(viz_components)} 个组件")
        else:
            print(f"✗ 获取可视化数据失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 训练数据API测试失败: {e}")
        return False

def test_experiments_api():
    """测试实验API"""
    print("\n=== 测试实验API ===")
    
    try:
        # 测试获取实验列表
        response = requests.get(f"{API_BASE_URL}/experiments/", timeout=10)
        if response.status_code == 200:
            experiments = response.json()
            print(f"✓ 获取实验列表成功: {len(experiments)} 个实验")
        else:
            print(f"✗ 获取实验列表失败: {response.status_code}")
            return False
        
        # 测试获取支持的模型
        response = requests.get(f"{API_BASE_URL}/models/supported", timeout=10)
        if response.status_code == 200:
            models = response.json()
            print(f"✓ 获取支持的模型成功: {len(models)} 个模型")
        else:
            print(f"✗ 获取支持的模型失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 实验API测试失败: {e}")
        return False

def test_end_to_end_workflow():
    """测试端到端工作流"""
    print("\n=== 测试端到端工作流 ===")
    
    try:
        # 1. 获取可用数据集
        response = requests.get(f"{API_BASE_URL}/datasets/available", timeout=10)
        if response.status_code != 200:
            print("✗ 步骤1失败: 无法获取可用数据集")
            return False
        
        available_data = response.json()
        training_ready = available_data.get('training_ready', [])
        
        if not training_ready:
            print("✗ 步骤1失败: 没有训练就绪的数据集")
            return False
        
        dataset_name = training_ready[0]
        print(f"✓ 步骤1成功: 选择数据集 {dataset_name}")
        
        # 2. 验证数据集
        response = requests.get(f"{API_BASE_URL}/datasets/{dataset_name}/validate", timeout=10)
        if response.status_code != 200:
            print("✗ 步骤2失败: 数据集验证失败")
            return False
        
        validation = response.json()
        if not validation.get('valid', False):
            print("✗ 步骤2失败: 数据集无效")
            return False
        
        print("✓ 步骤2成功: 数据集验证通过")
        
        # 3. 准备训练数据
        prepare_params = {
            "test_size": 0.2,
            "validation_size": 0.1,
            "seed": 42
        }
        
        response = requests.post(
            f"{API_BASE_URL}/datasets/{dataset_name}/prepare-training",
            json=prepare_params,
            timeout=30
        )
        
        if response.status_code == 200:
            prepare_result = response.json()
            print("✓ 步骤3成功: 训练数据准备完成")
            print(f"  数据集: {prepare_result.get('dataset_name', 'unknown')}")
        else:
            print(f"✗ 步骤3失败: 训练数据准备失败 ({response.status_code})")
            return False
        
        print("✓ 端到端工作流测试成功")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 端到端工作流测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始API集成测试...")
    print(f"API基础URL: {API_BASE_URL}")
    
    success_count = 0
    total_tests = 5
    
    # 运行所有测试
    if test_api_health():
        success_count += 1
    
    if test_datasets_api():
        success_count += 1
    
    if test_training_data_api():
        success_count += 1
    
    if test_experiments_api():
        success_count += 1
    
    if test_end_to_end_workflow():
        success_count += 1
    
    print(f"\n=== API集成测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count >= 4:  # 允许一个测试失败
        print("✓ API集成基本正常！")
        print("\n集成特性:")
        print("✓ 真实数据集API")
        print("✓ 数据验证API")
        print("✓ 训练数据准备API")
        print("✓ 可视化数据API")
        print("✓ 端到端工作流")
    else:
        print("✗ API集成存在问题，需要检查")
        print("\n可能的问题:")
        print("- API服务未启动")
        print("- 数据集文件缺失")
        print("- 网络连接问题")
        print("- 服务配置错误")
    
    print("\n测试完成!")
    return success_count >= 4

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
