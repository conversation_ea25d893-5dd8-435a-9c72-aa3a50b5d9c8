#!/usr/bin/env python3
"""
修复数据库中的准确率数据格式
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'design_platform', 'backend'))

from app.core.database import SessionLocal
from app.models.experiment import Experiment
import json

def fix_accuracy_data():
    """修复数据库中的准确率数据"""
    
    db = SessionLocal()
    
    try:
        # 获取所有实验
        experiments = db.query(Experiment).all()
        
        print(f"🔍 找到 {len(experiments)} 个实验")
        
        fixed_count = 0
        
        for exp in experiments:
            print(f"\n实验 {exp.id}: {exp.name}")

            # 修复 metrics 字段
            if exp.metrics:
                metrics = exp.metrics.copy()
                accuracy = metrics.get('accuracy')

                if accuracy is not None and accuracy > 1:
                    new_accuracy = accuracy / 100
                    metrics['accuracy'] = new_accuracy
                    exp.metrics = metrics

                    print(f"  ✅ 修复metrics.accuracy: {accuracy} -> {new_accuracy}")
                    fixed_count += 1
                elif accuracy is not None:
                    print(f"  ✅ metrics.accuracy已经是正确格式: {accuracy}")
                else:
                    print(f"  ❌ metrics中没有accuracy数据")

            # 修复 results.metrics 字段
            if exp.results and 'metrics' in exp.results:
                results = exp.results.copy()
                results_metrics = results['metrics'].copy()
                results_accuracy = results_metrics.get('accuracy')

                if results_accuracy is not None and results_accuracy > 1:
                    new_results_accuracy = results_accuracy / 100
                    results_metrics['accuracy'] = new_results_accuracy
                    results['metrics'] = results_metrics
                    exp.results = results

                    print(f"  ✅ 修复results.metrics.accuracy: {results_accuracy} -> {new_results_accuracy}")
                    fixed_count += 1
                elif results_accuracy is not None:
                    print(f"  ✅ results.metrics.accuracy已经是正确格式: {results_accuracy}")
                else:
                    print(f"  ❌ results.metrics中没有accuracy数据")
            else:
                print(f"  ❌ 没有results.metrics数据")
        
        if fixed_count > 0:
            db.commit()
            print(f"\n🎉 成功修复 {fixed_count} 个实验的accuracy数据")
        else:
            print(f"\n✅ 所有实验的accuracy数据都已经是正确格式")
            
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        db.rollback()
    finally:
        db.close()

def test_new_experiment():
    """测试创建新实验的accuracy格式"""
    import requests
    
    print("\n🧪 测试创建新实验...")
    
    base_url = "http://localhost:8000"
    
    # 创建新实验的测试数据
    experiment_data = {
        "name": "准确率格式测试实验",
        "model_type": "orcdf",
        "dataset_id": 1,
        "config": {
            "epochs": 5,
            "learning_rate": 0.001,
            "batch_size": 256
        }
    }
    
    try:
        # 创建实验
        create_response = requests.post(
            f"{base_url}/api/v1/experiments/",
            json=experiment_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if create_response.status_code == 200:
            experiment = create_response.json()
            experiment_id = experiment['id']
            print(f"✅ 创建实验成功，ID: {experiment_id}")
            
            # 启动训练
            start_response = requests.post(
                f"{base_url}/api/v1/experiments/{experiment_id}/start",
                timeout=30
            )
            
            if start_response.status_code == 200:
                print(f"✅ 启动训练成功")
                
                # 等待训练完成
                import time
                print("⏳ 等待训练完成...")
                
                for i in range(30):  # 最多等待30秒
                    time.sleep(1)
                    
                    status_response = requests.get(
                        f"{base_url}/api/v1/experiments/{experiment_id}",
                        timeout=30
                    )
                    
                    if status_response.status_code == 200:
                        exp_data = status_response.json()
                        status = exp_data.get('status')
                        
                        print(f"  状态: {status}")
                        
                        if status == 'completed':
                            metrics = exp_data.get('metrics', {})
                            accuracy = metrics.get('accuracy')
                            auc = metrics.get('auc')
                            
                            print(f"🎉 训练完成!")
                            print(f"  accuracy: {accuracy}")
                            print(f"  auc: {auc}")
                            
                            if accuracy is not None:
                                if 0 <= accuracy <= 1:
                                    print(f"  ✅ accuracy格式正确 (0-1): {accuracy}")
                                    print(f"  📊 显示为: {accuracy * 100:.1f}%")
                                else:
                                    print(f"  ❌ accuracy格式错误: {accuracy}")
                            
                            break
                        elif status == 'failed':
                            print(f"❌ 训练失败")
                            break
                else:
                    print(f"⏰ 训练超时")
                    
            else:
                print(f"❌ 启动训练失败: {start_response.status_code}")
                print(f"错误: {start_response.text}")
        else:
            print(f"❌ 创建实验失败: {create_response.status_code}")
            print(f"错误: {create_response.text}")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")

if __name__ == "__main__":
    print("🚀 开始修复准确率数据格式...")
    
    # 修复现有数据
    fix_accuracy_data()
    
    print("\n" + "="*60)
    
    # 测试新实验
    test_new_experiment()
    
    print("\n🏁 修复完成!")
