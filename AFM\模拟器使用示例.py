#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习路径规划模拟器使用示例
演示如何使用模拟器评估不同的路径规划算法
"""

import numpy as np
import matplotlib.pyplot as plt
from simulation_framework_explained import LearningEnvironment, RandomAgent, GreedyAgent, SmartGreedyAgent, evaluate_agent

class YourCustomAgent:
    """
    示例：自定义路径规划算法
    这里实现一个基于学习进度的自适应策略
    """
    
    def __init__(self, action_space_size: int):
        self.action_space_size = action_space_size
        self.learning_history = []
    
    def get_action(self, observation: np.ndarray) -> int:
        """
        自定义决策逻辑：
        1. 优先选择掌握程度在0.3-0.7之间的知识点（最佳学习区间）
        2. 避免重复学习最近学过的知识点
        3. 考虑整体学习进度的平衡
        """
        scores = []
        
        for action in range(self.action_space_size):
            current_mastery = observation[action]
            
            # 1. 基于掌握程度的学习价值
            if 0.3 <= current_mastery <= 0.7:
                learning_value = 1.0  # 最佳学习区间
            elif current_mastery < 0.3:
                learning_value = 0.6  # 太难，学习困难
            else:
                learning_value = 0.2  # 太简单，价值低
            
            # 2. 避免重复学习
            repetition_penalty = 0
            if len(self.learning_history) >= 3:
                recent_actions = self.learning_history[-3:]
                repetition_penalty = recent_actions.count(action) * 0.2
            
            # 3. 学习需求
            learning_need = 1.0 - current_mastery
            
            # 综合得分
            score = learning_value * 0.5 + learning_need * 0.4 - repetition_penalty * 0.1
            scores.append(score)
        
        # 选择最佳动作
        best_action = np.argmax(scores)
        self.learning_history.append(best_action)
        
        return best_action
    
    def reset(self):
        """重置学习历史"""
        self.learning_history = []
    
    def update(self, observation, action, reward, next_observation, done):
        """可选：用于学习型算法的参数更新"""
        pass

def quick_evaluation_demo():
    """快速评估演示"""
    print("🚀 学习路径规划算法快速评估演示")
    print("=" * 60)
    
    # 1. 创建仿真环境
    env = LearningEnvironment(
        num_knowledge_points=5,      # 5个知识点
        learning_rate=0.3,           # 学习率30%
        forgetting_rate=0.02,        # 遗忘率2%
        success_threshold=0.6,       # 成功阈值60%
        min_success_kps=3,           # 至少掌握3个知识点
        max_steps=25                 # 最多25步学习
    )
    
    print(f"📚 环境配置:")
    print(f"  知识点数量: {env.num_kps}")
    print(f"  成功标准: {env.min_success_kps}个知识点达到{env.success_threshold}")
    print(f"  最大学习步数: {env.max_steps}")
    
    # 2. 创建待评估的算法
    algorithms = {
        'Random': RandomAgent(env.action_space_size),
        'Greedy': GreedyAgent(env.action_space_size),
        'Smart_Greedy': SmartGreedyAgent(env.action_space_size, env.dependency_matrix),
        'Your_Custom': YourCustomAgent(env.action_space_size)
    }
    
    print(f"\n🤖 待评估算法: {list(algorithms.keys())}")
    
    # 3. 批量评估
    print(f"\n🧪 开始评估 (每个算法运行50回合)...")
    results = {}
    
    for name, agent in algorithms.items():
        print(f"  评估 {name}...")
        result = evaluate_agent(agent, env, num_episodes=50)
        results[name] = result
    
    # 4. 结果展示
    print(f"\n📊 评估结果:")
    print(f"{'算法名称':<15} {'平均奖励':<10} {'最终得分':<10} {'成功率':<8} {'平均步数':<8}")
    print("-" * 60)
    
    # 按最终得分排序
    sorted_results = sorted(results.items(), key=lambda x: x[1]['avg_final_score'], reverse=True)
    
    for name, result in sorted_results:
        print(f"{name:<15} {result['avg_reward']:<10.2f} {result['avg_final_score']:<10.3f} "
              f"{result['success_rate']:<8.1%} {result['avg_steps']:<8.1f}")
    
    return results

def detailed_analysis_demo():
    """详细分析演示"""
    print(f"\n🔍 详细性能分析")
    print("=" * 60)
    
    # 运行评估
    results = quick_evaluation_demo()
    
    # 性能分析
    print(f"\n📈 性能分析:")
    
    # 找出最佳算法
    best_algorithm = max(results.keys(), key=lambda x: results[x]['avg_final_score'])
    worst_algorithm = min(results.keys(), key=lambda x: results[x]['avg_final_score'])
    
    best_score = results[best_algorithm]['avg_final_score']
    worst_score = results[worst_algorithm]['avg_final_score']
    improvement = ((best_score - worst_score) / worst_score) * 100
    
    print(f"🏆 最佳算法: {best_algorithm} (得分: {best_score:.3f})")
    print(f"📉 最差算法: {worst_algorithm} (得分: {worst_score:.3f})")
    print(f"📊 性能提升: {improvement:.1f}%")
    
    # 稳定性分析
    print(f"\n🎯 稳定性分析:")
    for name, result in results.items():
        stability = result['std_final_score'] / result['avg_final_score']
        print(f"  {name}: 变异系数 = {stability:.3f} ({'稳定' if stability < 0.1 else '不稳定'})")
    
    # 效率分析
    print(f"\n⚡ 效率分析:")
    for name, result in results.items():
        efficiency = result['avg_final_score'] / result['avg_steps']
        print(f"  {name}: 学习效率 = {efficiency:.4f} (得分/步数)")

def algorithm_comparison_visualization():
    """算法对比可视化"""
    print(f"\n📊 生成性能对比图表...")
    
    # 运行评估获取数据
    env = LearningEnvironment(num_knowledge_points=5, max_steps=25)
    algorithms = {
        'Random': RandomAgent(env.action_space_size),
        'Greedy': GreedyAgent(env.action_space_size),
        'Smart_Greedy': SmartGreedyAgent(env.action_space_size, env.dependency_matrix),
        'Your_Custom': YourCustomAgent(env.action_space_size)
    }
    
    # 收集详细数据
    detailed_results = {}
    for name, agent in algorithms.items():
        episode_scores = []
        episode_rewards = []
        
        for episode in range(20):  # 减少回合数以便演示
            observation = env.reset()
            agent.reset()
            total_reward = 0
            
            while True:
                action = agent.get_action(observation)
                observation, reward, done, info = env.step(action)
                total_reward += reward
                
                if done:
                    break
            
            episode_scores.append(np.mean(observation))
            episode_rewards.append(total_reward)
        
        detailed_results[name] = {
            'scores': episode_scores,
            'rewards': episode_rewards
        }
    
    # 创建对比图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 最终得分对比
    names = list(detailed_results.keys())
    avg_scores = [np.mean(detailed_results[name]['scores']) for name in names]
    std_scores = [np.std(detailed_results[name]['scores']) for name in names]
    
    ax1.bar(names, avg_scores, yerr=std_scores, capsize=5, alpha=0.7)
    ax1.set_title('算法最终得分对比')
    ax1.set_ylabel('平均最终得分')
    ax1.tick_params(axis='x', rotation=45)
    
    # 学习曲线对比
    for name in names:
        scores = detailed_results[name]['scores']
        ax2.plot(scores, label=name, alpha=0.7)
    
    ax2.set_title('学习曲线对比')
    ax2.set_xlabel('回合数')
    ax2.set_ylabel('最终得分')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('algorithm_comparison.png', dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存为 'algorithm_comparison.png'")
    
    return detailed_results

def custom_environment_demo():
    """自定义环境参数演示"""
    print(f"\n🔧 自定义环境参数演示")
    print("=" * 60)
    
    # 测试不同环境配置对算法性能的影响
    configs = [
        {'name': '简单环境', 'learning_rate': 0.4, 'forgetting_rate': 0.01, 'max_steps': 20},
        {'name': '标准环境', 'learning_rate': 0.3, 'forgetting_rate': 0.02, 'max_steps': 25},
        {'name': '困难环境', 'learning_rate': 0.2, 'forgetting_rate': 0.03, 'max_steps': 30},
    ]
    
    agent = YourCustomAgent(5)  # 使用自定义算法
    
    print(f"🧪 测试自定义算法在不同环境下的表现:")
    print(f"{'环境类型':<10} {'平均得分':<10} {'成功率':<8} {'平均步数':<8}")
    print("-" * 40)
    
    for config in configs:
        env = LearningEnvironment(
            num_knowledge_points=5,
            learning_rate=config['learning_rate'],
            forgetting_rate=config['forgetting_rate'],
            max_steps=config['max_steps']
        )
        
        result = evaluate_agent(agent, env, num_episodes=30)
        
        print(f"{config['name']:<10} {result['avg_final_score']:<10.3f} "
              f"{result['success_rate']:<8.1%} {result['avg_steps']:<8.1f}")

def main():
    """主演示函数"""
    print("🎯 学习路径规划模拟器完整使用演示")
    print("=" * 80)
    
    # 1. 快速评估演示
    quick_evaluation_demo()
    
    # 2. 详细分析演示
    detailed_analysis_demo()
    
    # 3. 可视化演示
    try:
        algorithm_comparison_visualization()
    except ImportError:
        print("⚠️  matplotlib未安装，跳过可视化演示")
    
    # 4. 自定义环境演示
    custom_environment_demo()
    
    print(f"\n" + "=" * 80)
    print(f"🎉 演示完成！")
    print(f"\n💡 使用建议:")
    print(f"1. 根据你的算法特点调整环境参数")
    print(f"2. 增加评估回合数以获得更稳定的结果")
    print(f"3. 对比多个基线算法以验证改进效果")
    print(f"4. 关注多个评估指标，避免过度优化单一指标")
    print(f"5. 在真实数据上验证仿真结果的有效性")

if __name__ == "__main__":
    main()
