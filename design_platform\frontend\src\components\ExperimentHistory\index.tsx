import React, { useState, useEffect } from 'react';
import { 
  Card, Table, Tag, Button, Space, Modal, Descriptions, 
  Typography, message, Popconfirm, Tooltip, Input, Select,
  DatePicker, Row, Col
} from 'antd';
import {
  EyeOutlined,
  DeleteOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SearchOutlined,
  FilterOutlined,
  ExperimentOutlined,
  BulbOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs, { Dayjs } from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { experimentService } from '../../services/api';
import CognitiveDiagnosisModal from '../CognitiveDiagnosisModal';

dayjs.extend(isBetween);

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

interface Experiment {
  id: string;
  name: string;
  model_type: string;
  dataset_name: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  created_at: string;
  completed_at?: string;
  duration?: number;
  metrics?: {
    accuracy: number;
    auc: number;
    loss: number;
  };
  config?: any;
}

interface ExperimentHistoryProps {
  onExperimentSelect?: (experiment: Experiment) => void;
  refreshTrigger?: number;
}

const ExperimentHistory: React.FC<ExperimentHistoryProps> = ({ 
  onExperimentSelect,
  refreshTrigger 
}) => {
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [modelFilter, setModelFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null] | null>(null);
  const [diagnosisModalVisible, setDiagnosisModalVisible] = useState(false);
  const [diagnosisExperiment, setDiagnosisExperiment] = useState<Experiment | null>(null);

  const fetchExperiments = async () => {
    setLoading(true);
    try {
      const response = await experimentService.getExperiments();
      setExperiments(response.data);
    } catch (error) {
      console.error('Failed to fetch experiments:', error);
      // 使用模拟数据作为后备
      const mockData: Experiment[] = [
        {
          id: 'exp_001',
          name: '数学认知诊断实验_001',
          model_type: 'ORCDF',
          dataset_name: 'Assist0910',
          status: 'completed',
          created_at: '2024-01-15T10:30:00Z',
          completed_at: '2024-01-15T11:45:00Z',
          duration: 4500,
          metrics: { accuracy: 0.856, auc: 0.892, loss: 0.234 }
        },
        {
          id: 'exp_002',
          name: '数学认知诊断实验_002',
          model_type: 'NCD',
          dataset_name: 'Assist0910',
          status: 'completed',
          created_at: '2024-01-14T14:20:00Z',
          completed_at: '2024-01-14T15:10:00Z',
          duration: 3000,
          metrics: { accuracy: 0.823, auc: 0.867, loss: 0.287 }
        },
        {
          id: 'exp_003',
          name: '数学认知诊断实验_003',
          model_type: 'ORCDF',
          dataset_name: 'Junyi',
          status: 'running',
          created_at: '2024-01-16T09:15:00Z'
        },
        {
          id: 'exp_004',
          name: '数学认知诊断实验_004',
          model_type: 'IRT',
          dataset_name: 'Assist0910',
          status: 'failed',
          created_at: '2024-01-13T16:45:00Z'
        }
      ];
      setExperiments(mockData);
      message.error('获取实验历史失败，使用模拟数据');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchExperiments();
  }, [refreshTrigger]);

  const handleDelete = async (experimentId: string) => {
    try {
      await experimentService.deleteExperiment(Number(experimentId));
      message.success('实验删除成功');
      fetchExperiments();
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('删除失败');
    }
  };

  const handleDownload = async (experimentId: string) => {
    try {
      // 暂时使用模拟下载，因为后端可能没有export端点
      const experiment = experiments.find(exp => exp.id === experimentId);
      if (experiment) {
        const dataStr = JSON.stringify(experiment, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = window.URL.createObjectURL(dataBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `experiment_${experimentId}.json`;
        a.click();
        window.URL.revokeObjectURL(url);
        message.success('导出成功');
      }
    } catch (error) {
      console.error('Export failed:', error);
      message.error('导出失败');
    }
  };

  const handleCognitiveDiagnosis = (experiment: Experiment) => {
    setDiagnosisExperiment(experiment);
    setDiagnosisModalVisible(true);
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'default', text: '已取消' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.cancelled;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  // 过滤数据
  const filteredExperiments = experiments.filter(exp => {
    const matchesSearch = exp.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         exp.model_type.toLowerCase().includes(searchText.toLowerCase()) ||
                         exp.dataset_name.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || exp.status === statusFilter;
    const matchesModel = modelFilter === 'all' || exp.model_type === modelFilter;
    
    let matchesDate = true;
    if (dateRange && dateRange[0] && dateRange[1]) {
      const expDate = dayjs(exp.created_at);
      matchesDate = expDate.isAfter(dateRange[0].startOf('day')) && expDate.isBefore(dateRange[1].endOf('day'));
    }
    
    return matchesSearch && matchesStatus && matchesModel && matchesDate;
  });

  const columns: ColumnsType<Experiment> = [
    {
      title: '实验名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setSelectedExperiment(record);
            setDetailModalVisible(true);
            onExperimentSelect?.(record);
          }}
        >
          {text}
        </Button>
      )
    },
    {
      title: '模型类型',
      dataIndex: 'model_type',
      key: 'model_type',
      width: 100,
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '数据集',
      dataIndex: 'dataset_name',
      key: 'dataset_name',
      width: 120
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag
    },
    {
      title: '准确率',
      key: 'accuracy',
      width: 100,
      render: (_, record) => 
        record.metrics?.accuracy ? `${(record.metrics.accuracy * 100).toFixed(1)}%` : '-'
    },
    {
      title: 'AUC',
      key: 'auc',
      width: 100,
      render: (_, record) => 
        record.metrics?.auc ? record.metrics.auc.toFixed(3) : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (text) => dayjs(text).format('MM-DD HH:mm')
    },
    {
      title: '耗时',
      key: 'duration',
      width: 100,
      render: (_, record) => formatDuration(record.duration)
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedExperiment(record);
                setDetailModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="导出结果">
            <Button
              size="small"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record.id)}
              disabled={record.status !== 'completed'}
            />
          </Tooltip>
          {record.status === 'completed' && (
            <Tooltip title="认知诊断">
              <Button
                size="small"
                icon={<BulbOutlined />}
                onClick={() => handleCognitiveDiagnosis(record)}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确定要删除这个实验吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                size="small" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>
            <ExperimentOutlined style={{ marginRight: '8px' }} />
            实验历史
          </span>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchExperiments}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      }
    >
      {/* 过滤器 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col xs={24} sm={8}>
          <Input
            placeholder="搜索实验名称、模型或数据集"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            allowClear
          />
        </Col>
        <Col xs={12} sm={4}>
          <Select
            placeholder="状态"
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: '100%' }}
          >
            <Option value="all">全部状态</Option>
            <Option value="running">运行中</Option>
            <Option value="completed">已完成</Option>
            <Option value="failed">失败</Option>
            <Option value="cancelled">已取消</Option>
          </Select>
        </Col>
        <Col xs={12} sm={4}>
          <Select
            placeholder="模型"
            value={modelFilter}
            onChange={setModelFilter}
            style={{ width: '100%' }}
          >
            <Option value="all">全部模型</Option>
            <Option value="EduBrain">EduBrain抗过平滑特征丰富方法</Option>
            <Option value="NCD">NCD</Option>
            <Option value="IRT">IRT</Option>
            <Option value="MIRT">MIRT</Option>
          </Select>
        </Col>
        <Col xs={24} sm={8}>
          <RangePicker
            value={dateRange}
            onChange={(dates) => setDateRange(dates)}
            style={{ width: '100%' }}
            placeholder={['开始日期', '结束日期']}
          />
        </Col>
      </Row>

      <Table
        columns={columns}
        dataSource={filteredExperiments}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`
        }}
        scroll={{ x: 1000 }}
      />

      {/* 详情模态框 */}
      <Modal
        title="实验详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
          selectedExperiment?.status === 'completed' && (
            <Button 
              key="download" 
              type="primary" 
              icon={<DownloadOutlined />}
              onClick={() => selectedExperiment && handleDownload(selectedExperiment.id)}
            >
              导出结果
            </Button>
          )
        ]}
        width={800}
      >
        {selectedExperiment && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="实验ID">{selectedExperiment.id}</Descriptions.Item>
            <Descriptions.Item label="实验名称">{selectedExperiment.name}</Descriptions.Item>
            <Descriptions.Item label="模型类型">{selectedExperiment.model_type}</Descriptions.Item>
            <Descriptions.Item label="数据集">{selectedExperiment.dataset_name}</Descriptions.Item>
            <Descriptions.Item label="状态">{getStatusTag(selectedExperiment.status)}</Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(selectedExperiment.created_at).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            {selectedExperiment.completed_at && (
              <Descriptions.Item label="完成时间">
                {dayjs(selectedExperiment.completed_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="耗时">
              {formatDuration(selectedExperiment.duration)}
            </Descriptions.Item>
            {selectedExperiment.metrics && (
              <>
                <Descriptions.Item label="准确率">
                  {(selectedExperiment.metrics.accuracy * 100).toFixed(2)}%
                </Descriptions.Item>
                <Descriptions.Item label="AUC">
                  {selectedExperiment.metrics.auc.toFixed(4)}
                </Descriptions.Item>
                <Descriptions.Item label="损失">
                  {selectedExperiment.metrics.loss.toFixed(4)}
                </Descriptions.Item>
              </>
            )}
          </Descriptions>
        )}
      </Modal>

      {/* 认知诊断模态框 */}
      <Modal
        title={`认知诊断 - ${diagnosisExperiment?.name}`}
        open={diagnosisModalVisible}
        onCancel={() => setDiagnosisModalVisible(false)}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {diagnosisExperiment && (
          <CognitiveDiagnosisModal
            experimentId={diagnosisExperiment.id}
            onClose={() => setDiagnosisModalVisible(false)}
          />
        )}
      </Modal>
    </Card>
  );
};

export default ExperimentHistory;
