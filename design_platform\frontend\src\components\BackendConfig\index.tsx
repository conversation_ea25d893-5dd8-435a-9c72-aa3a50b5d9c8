import React, { useState, useEffect } from 'react';
import { Modal, Input, Button, message, Alert, Space, Typography } from 'antd';
import { SettingOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { saveBackendConfig, testBackendConnection } from '../../utils/envConfig';

const { Text, Title } = Typography;

interface BackendConfigProps {
  visible: boolean;
  onClose: () => void;
  onConfigured: (backendUrl: string) => void;
}

const BackendConfig: React.FC<BackendConfigProps> = ({ visible, onClose, onConfigured }) => {
  const [backendUrl, setBackendUrl] = useState('');
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);

  useEffect(() => {
    // 加载已保存的配置
    const savedUrl = localStorage.getItem('BACKEND_URL');
    if (savedUrl) {
      setBackendUrl(savedUrl);
    }
  }, [visible]);

  const testConnection = async () => {
    if (!backendUrl.trim()) {
      message.error('请输入后端地址');
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      const isConnected = await testBackendConnection(backendUrl.trim());

      if (isConnected) {
        setTestResult('success');
        message.success('后端连接测试成功！');
      } else {
        setTestResult('error');
        message.error('连接失败，请检查后端地址是否正确');
      }
    } catch (error) {
      setTestResult('error');
      message.error(`连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('Backend test error:', error);
    } finally {
      setTesting(false);
    }
  };

  const handleSave = () => {
    if (!backendUrl.trim()) {
      message.error('请输入后端地址');
      return;
    }

    // 标准化URL格式
    let normalizedUrl = backendUrl.trim();
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = `https://${normalizedUrl}`;
    }
    if (normalizedUrl.endsWith('/')) {
      normalizedUrl = normalizedUrl.slice(0, -1);
    }

    // 使用工具函数保存配置
    saveBackendConfig(normalizedUrl);

    // 显示成功消息，但不自动刷新
    message.success({
      content: '后端地址配置成功！配置已立即生效，无需刷新页面。',
      duration: 3,
      key: 'backend-config-success'
    });

    // 通知父组件
    onConfigured(normalizedUrl);
    onClose();
  };

  const handleSkip = () => {
    onClose();
  };

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          <span>配置后端服务地址</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="skip" onClick={handleSkip}>
          跳过配置
        </Button>,
        <Button key="test" onClick={testConnection} loading={testing}>
          测试连接
        </Button>,
        <Button key="save" type="primary" onClick={handleSave} disabled={testResult !== 'success'}>
          保存配置
        </Button>,
      ]}
      width={600}
      maskClosable={false}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Alert
          message="检测到内网穿透环境"
          description="为了正常使用系统功能，请配置后端服务的公网地址。"
          type="info"
          showIcon
        />

        <div>
          <Title level={5}>后端服务地址：</Title>
          <Input
            placeholder="请输入后端服务的完整地址，如：https://abc123.ngrok.io"
            value={backendUrl}
            onChange={(e) => setBackendUrl(e.target.value)}
            size="large"
            prefix={<SettingOutlined />}
          />
          <Text type="secondary" style={{ fontSize: '12px', marginTop: '4px', display: 'block' }}>
            示例：https://abc123.ngrok.io 或 https://def456.cpolar.top
          </Text>
        </div>

        {testResult === 'success' && (
          <Alert
            message="连接测试成功"
            description="后端服务连接正常，可以保存配置。"
            type="success"
            showIcon
            icon={<CheckCircleOutlined />}
          />
        )}

        {testResult === 'error' && (
          <Alert
            message="连接测试失败"
            description="无法连接到后端服务，请检查地址是否正确，或确认后端服务是否正常运行。"
            type="error"
            showIcon
            icon={<ExclamationCircleOutlined />}
          />
        )}

        <div style={{ background: '#f6f8fa', padding: '12px', borderRadius: '6px' }}>
          <Title level={5} style={{ margin: 0, marginBottom: '8px' }}>💡 配置说明：</Title>
          <Text type="secondary" style={{ fontSize: '13px' }}>
            1. 确保后端服务已通过内网穿透暴露到公网<br/>
            2. 输入完整的后端地址（包含 https:// 前缀）<br/>
            3. 点击"测试连接"验证地址是否可用<br/>
            4. 测试成功后点击"保存配置"应用设置
          </Text>
        </div>
      </Space>
    </Modal>
  );
};

export default BackendConfig;
