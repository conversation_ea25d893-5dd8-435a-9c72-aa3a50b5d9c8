import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Statistic, Typography, List, Avatar, Tag, Button, Space,
  Progress, Timeline, Alert, Divider
} from 'antd';
import ReactECharts from 'echarts-for-react';
import {
  ExperimentOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RobotOutlined,
  BarChartOutlined,
  UserOutlined,
  FileTextOutlined,
  TrophyOutlined,
  RiseOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Paragraph, Text } = Typography;

// 动态API配置 - 支持内网穿透
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // 检测当前访问域名
  const currentHost = window.location.hostname;

  // 如果是内网穿透域名，使用相对路径或提示用户配置
  if (currentHost.includes('ngrok') || currentHost.includes('cpolar') || currentHost.includes('frp')) {
    // 内网穿透环境，需要用户手动配置后端地址
    const backendUrl = localStorage.getItem('BACKEND_URL');
    if (backendUrl) {
      return `${backendUrl}/api/v1`;
    }

    // 如果没有配置，提示用户配置
    console.warn('🌐 检测到内网穿透环境，请配置后端地址');
    return 'http://localhost:8000/api/v1'; // 默认值
  }

  // 本地开发环境
  return 'http://localhost:8000/api/v1';
};

interface DashboardStats {
  totalExperiments: number;
  runningExperiments: number;
  completedExperiments: number;
  failedExperiments: number;
  availableDatasets: number;
  totalStudents: number;
  totalQuestions: number;
  avgAccuracy: number;
}

interface RecentExperiment {
  id: number;
  name: string;
  status: string;
  progress: number;
  created_at: string;
  dataset_type: string;
  model_type: string;
}

const DashboardHome: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalExperiments: 0,
    runningExperiments: 0,
    completedExperiments: 0,
    failedExperiments: 0,
    availableDatasets: 0,
    totalStudents: 0,
    totalQuestions: 0,
    avgAccuracy: 0
  });
  const [recentExperiments, setRecentExperiments] = useState<RecentExperiment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 加载实验统计
      const apiBaseUrl = getApiBaseUrl();
      const experimentsResponse = await fetch(`${apiBaseUrl}/experiments`);
      if (experimentsResponse.ok) {
        const experimentsData = await experimentsResponse.json();
        const totalExperiments = experimentsData.length;
        const runningExperiments = experimentsData.filter((exp: any) => exp.status === 'running').length;
        const completedExperiments = experimentsData.filter((exp: any) => exp.status === 'completed').length;
        const failedExperiments = experimentsData.filter((exp: any) => exp.status === 'failed').length;
        
        // 计算平均准确率 (后端返回0-1格式，需要转换为百分比)
        const completedWithMetrics = experimentsData.filter((exp: any) =>
          exp.status === 'completed' && exp.metrics?.accuracy
        );
        const avgAccuracy = completedWithMetrics.length > 0
          ? (completedWithMetrics.reduce((sum: number, exp: any) => sum + exp.metrics.accuracy, 0) / completedWithMetrics.length) * 100
          : 0;

        // 获取最近的实验
        const recent = experimentsData
          .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 5);
        setRecentExperiments(recent);

        setStats(prev => ({
          ...prev,
          totalExperiments,
          runningExperiments,
          completedExperiments,
          failedExperiments,
          avgAccuracy
        }));
      }

      // 加载数据集统计
      const datasetsResponse = await fetch(`${apiBaseUrl}/datasets/`);
      if (datasetsResponse.ok) {
        const datasetsData = await datasetsResponse.json();
        console.log('数据集API响应:', datasetsData);

        // 处理不同的响应格式
        let datasets = [];
        if (Array.isArray(datasetsData)) {
          datasets = datasetsData;
        } else if (datasetsData.datasets && Array.isArray(datasetsData.datasets)) {
          datasets = datasetsData.datasets;
        } else if (datasetsData.data && Array.isArray(datasetsData.data)) {
          datasets = datasetsData.data;
        }

        // 使用正确的字段名
        const totalStudents = datasets.reduce((sum: number, ds: any) => sum + (ds.student_num || 0), 0);
        const totalQuestions = datasets.reduce((sum: number, ds: any) => sum + (ds.exercise_num || 0), 0);

        console.log('数据集统计:', { totalStudents, totalQuestions, datasets: datasets.length });
        console.log('数据集详情:', datasets);

        setStats(prev => ({
          ...prev,
          availableDatasets: datasets.length,
          totalStudents,
          totalQuestions
        }));
      }
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'warning', text: '已取消' },
    };
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 实验状态分布饼图
  const getExperimentStatusChart = () => {
    const option = {
      title: {
        text: '实验状态分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: ['运行中', '已完成', '失败', '等待中']
      },
      series: [
        {
          name: '实验状态',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: [
            { value: stats.runningExperiments, name: '运行中', itemStyle: { color: '#52c41a' } },
            { value: stats.completedExperiments, name: '已完成', itemStyle: { color: '#1890ff' } },
            { value: stats.failedExperiments, name: '失败', itemStyle: { color: '#ff4d4f' } },
            { value: stats.totalExperiments - stats.runningExperiments - stats.completedExperiments - stats.failedExperiments, name: '等待中', itemStyle: { color: '#faad14' } }
          ]
        }
      ]
    };
    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  // 性能趋势图
  const getPerformanceTrendChart = () => {
    const option = {
      title: {
        text: '模型性能趋势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['准确率', 'AUC', 'F1-Score'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['实验1', '实验2', '实验3', '实验4', '实验5', '实验6', '实验7']
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '准确率',
          type: 'line',
          smooth: true,
          data: [82.5, 85.2, 87.8, 89.1, 91.3, 93.2, 94.8],
          itemStyle: { color: '#1890ff' },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          }
        },
        {
          name: 'AUC',
          type: 'line',
          smooth: true,
          data: [78.3, 81.7, 84.2, 86.8, 88.9, 90.5, 92.1],
          itemStyle: { color: '#52c41a' }
        },
        {
          name: 'F1-Score',
          type: 'line',
          smooth: true,
          data: [75.8, 79.2, 82.1, 84.7, 87.2, 89.8, 91.5],
          itemStyle: { color: '#faad14' }
        }
      ]
    };
    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  // 数据集使用热力图
  const getDatasetHeatmapChart = () => {
    const option = {
      title: {
        text: '数据集使用热力图',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        position: 'top',
        formatter: function (params: any) {
          return `${params.data[1]}月${params.data[0]}日: ${params.data[2]}次使用`;
        }
      },
      grid: {
        height: '50%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30'],
        splitArea: {
          show: true
        }
      },
      yAxis: {
        type: 'category',
        data: ['Assist0910', 'EdNet', 'Junyi', 'PISA2015'],
        splitArea: {
          show: true
        }
      },
      visualMap: {
        min: 0,
        max: 10,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%',
        inRange: {
          color: ['#e0f3ff', '#1890ff']
        }
      },
      series: [{
        name: '使用次数',
        type: 'heatmap',
        data: generateHeatmapData(),
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };
    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  // 生成热力图数据
  const generateHeatmapData = () => {
    const data = [];
    const datasets = ['Assist0910', 'EdNet', 'Junyi', 'PISA2015'];
    for (let i = 0; i < 30; i++) {
      for (let j = 0; j < datasets.length; j++) {
        data.push([i + 1, j, Math.floor(Math.random() * 10)]);
      }
    }
    return data;
  };

  const quickActions = [
    {
      title: '开始新训练',
      description: '创建新的认知诊断模型训练',
      icon: <RobotOutlined />,
      color: '#1890ff',
      action: () => navigate('/dashboard/training')
    },
    {
      title: '实验管理',
      description: '查看和管理所有实验',
      icon: <ExperimentOutlined />,
      color: '#52c41a',
      action: () => navigate('/experiments')
    },
    {
      title: '数据集管理',
      description: '管理训练数据集',
      icon: <DatabaseOutlined />,
      color: '#faad14',
      action: () => navigate('/datasets')
    },
    {
      title: '结果分析',
      description: '查看训练结果和性能分析',
      icon: <BarChartOutlined />,
      color: '#722ed1',
      action: () => navigate('/analysis')
    },
    {
      title: '诊断与规划',
      description: '进行学生认知诊断和学习路径规划',
      icon: <EyeOutlined />,
      color: '#eb2f96',
      action: () => navigate('/cognitive-diagnosis-center')
    }
  ];

  return (
    <div style={{ padding: '24px', background: '#ffffff', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          🧠 智擎EduBrain 仪表盘
        </Title>
        <Paragraph>
          欢迎使用双驱协同认知诊断与规划系统 - 您的智能教育AI助手
        </Paragraph>
      </div>

      {/* 核心统计指标 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总实验数"
              value={stats.totalExperiments}
              prefix={<ExperimentOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.runningExperiments}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completedExperiments}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均准确率"
              value={stats.avgAccuracy}
              precision={1}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据概览 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="可用数据集"
              value={stats.availableDatasets}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总学生数"
              value={stats.totalStudents}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总题目数"
              value={stats.totalQuestions}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 可视化图表区域 */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={8}>
          <Card>
            {getExperimentStatusChart()}
          </Card>
        </Col>
        <Col xs={24} lg={16}>
          <Card>
            {getPerformanceTrendChart()}
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24}>
          <Card>
            {getDatasetHeatmapChart()}
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        {/* 快速操作 */}
        <Col xs={24} lg={12}>
          <Card title="快速操作" extra={<RiseOutlined />}>
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={24} sm={12} key={index}>
                  <Card
                    hoverable
                    size="small"
                    onClick={action.action}
                    style={{ cursor: 'pointer' }}
                  >
                    <Card.Meta
                      avatar={
                        <Avatar
                          style={{ backgroundColor: action.color }}
                          icon={action.icon}
                        />
                      }
                      title={action.title}
                      description={action.description}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>

        {/* 最近实验 */}
        <Col xs={24} lg={12}>
          <Card title="最近实验" extra={<ClockCircleOutlined />}>
            <List
              dataSource={recentExperiments}
              renderItem={(experiment) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<ExperimentOutlined />} />}
                    title={
                      <Space>
                        {experiment.name}
                        {getStatusTag(experiment.status)}
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">
                          {experiment.dataset_type} • {experiment.model_type}
                        </Text>
                        {experiment.status === 'running' && (
                          <Progress
                            percent={experiment.progress}
                            size="small"
                            style={{ marginTop: 4 }}
                          />
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DashboardHome;
