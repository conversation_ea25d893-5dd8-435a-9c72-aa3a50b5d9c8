# EduBrain评测机制与指标详解

## 1. EduBrain模型概述

EduBrain (Educational Brain) 是一个基于图神经网络的认知诊断模型，它通过学习学生、题目和知识点之间的复杂关系来进行认知诊断。

## 2. 评测机制

### 2.1 推荐效果评测

EduBrain的推荐效果主要通过以下几个维度进行评测：

#### A. 预测准确性评测
- **目标**: 评估模型预测学生答题结果的准确性
- **方法**: 使用测试集中的真实答题记录与模型预测结果进行对比
- **实现**: 
  ```python
  # 从代码中可以看到评测流程
  def evaluate(self, adaptest_data: AdapTestDataset):
      # 1. 获取真实答题数据
      real = []  # 真实答题结果 (0或1)
      pred = []  # 模型预测概率 (0-1之间)
      
      # 2. 模型推理
      for student_id, question_id in test_data:
          prediction = model.predict(student_id, question_id)
          pred.append(prediction)
          real.append(actual_answer)
      
      # 3. 计算评测指标
      return calculate_metrics(real, pred)
  ```

#### B. 知识点覆盖度评测
- **目标**: 评估推荐的题目是否能有效覆盖学生需要掌握的知识点
- **计算方法**:
  ```python
  coverage = len(tested_concepts) / len(all_concepts)
  ```
- **意义**: 覆盖度越高，说明推荐的题目越全面

### 2.2 自适应测试评测

EduBrain支持自适应测试，即根据学生的答题情况动态调整后续题目的难度和类型：

#### A. 题目选择策略
- **信息熵最大化**: 选择能最大化信息增益的题目
- **不确定性最小化**: 优先推荐模型预测不确定性较高的题目
- **知识点平衡**: 确保各个知识点都得到充分测试

#### B. 停止条件
- **精度阈值**: 当诊断精度达到预设阈值时停止
- **题目数量限制**: 达到最大题目数量时停止
- **置信度要求**: 当所有知识点的掌握程度置信度都足够高时停止

## 3. 评测指标详解

### 3.1 ACC (Accuracy) - 准确率
- **定义**: 模型正确预测的样本数占总样本数的比例
- **计算公式**: 
  ```
  ACC = (TP + TN) / (TP + TN + FP + FN)
  ```
- **代码实现**:
  ```python
  threshold = 0.5  # 分类阈值
  binary_pred = (pred >= threshold).astype(int)
  acc = accuracy_score(real, binary_pred)
  ```
- **取值范围**: [0, 1]，越接近1越好
- **意义**: 反映模型整体的预测准确性

### 3.2 AUC (Area Under Curve) - ROC曲线下面积
- **定义**: ROC曲线下的面积，衡量模型的分类性能
- **计算**: 
  ```python
  auc = roc_auc_score(real, pred)
  ```
- **取值范围**: [0, 1]，0.5表示随机猜测，1表示完美分类
- **意义**: 
  - AUC > 0.9: 优秀
  - 0.8 < AUC ≤ 0.9: 良好
  - 0.7 < AUC ≤ 0.8: 一般
  - 0.6 < AUC ≤ 0.7: 较差
  - AUC ≤ 0.6: 很差

### 3.3 COV (Coverage) - 知识点覆盖率
- **定义**: 测试过程中覆盖的知识点占总知识点的比例
- **计算公式**:
  ```python
  coverage = len(tested_concepts) / len(all_concepts)
  ```
- **取值范围**: [0, 1]，越接近1越好
- **意义**: 衡量测试的全面性，覆盖率高说明诊断更全面

### 3.4 F1-Score - F1分数
- **定义**: 精确率和召回率的调和平均数
- **计算公式**:
  ```
  F1 = 2 * (Precision * Recall) / (Precision + Recall)
  ```
- **意义**: 综合考虑精确率和召回率，适用于不平衡数据集

### 3.5 RMSE (Root Mean Square Error) - 均方根误差
- **定义**: 预测值与真实值差异的均方根
- **计算公式**:
  ```python
  rmse = np.sqrt(mean_squared_error(true_r, pred_r))
  ```
- **取值范围**: [0, +∞)，越小越好
- **意义**: 衡量预测的精确程度，对大误差更敏感

### 3.6 DOA (Degree of Agreement) - 一致性程度
- **定义**: 衡量诊断结果与专家标注的一致性
- **计算**: 基于知识状态向量的相似度计算
- **意义**: 反映模型诊断结果的可信度

### 3.7 MAD (Mean Average Distance) - 平均距离
- **定义**: 学生认知状态向量之间的平均距离
- **计算**:
  ```python
  def mean_average_distance(mastery_level):
      n = mastery_level.shape[0]
      # 计算所有学生对之间的欧氏距离
      distances = []
      for i in range(n):
          for j in range(i+1, n):
              dist = np.linalg.norm(mastery_level[i] - mastery_level[j])
              distances.append(dist)
      return np.mean(distances)
  ```
- **意义**: 衡量学生群体认知状态的分散程度

## 4. 推荐系统评测

### 4.1 推荐准确性
- **点击率预测**: 预测学生是否会正确回答推荐的题目
- **难度匹配**: 推荐题目的难度是否与学生能力匹配
- **知识点相关性**: 推荐题目是否针对学生的薄弱知识点

### 4.2 推荐多样性
- **题目类型多样性**: 推荐的题目类型是否丰富
- **知识点分布**: 是否均匀覆盖各个知识点
- **难度梯度**: 是否有合适的难度递进

### 4.3 推荐时效性
- **响应时间**: 生成推荐结果的时间
- **更新频率**: 根据学生答题情况更新推荐的频率
- **实时性**: 能否实时响应学生的学习状态变化

## 5. 评测流程

### 5.1 数据准备
1. **训练集**: 用于模型训练
2. **验证集**: 用于模型调参和早停
3. **测试集**: 用于最终评测，不参与训练过程

### 5.2 评测步骤
1. **模型训练**: 在训练集上训练EduBrain模型
2. **参数调优**: 在验证集上调整超参数
3. **性能评测**: 在测试集上计算各项指标
4. **结果分析**: 分析不同指标的表现和相关性

### 5.3 交叉验证
- **K折交叉验证**: 将数据分为K份，轮流作为测试集
- **时间序列验证**: 按时间顺序划分训练和测试数据
- **学生分组验证**: 按学生分组进行验证，避免数据泄露

## 6. 实际应用中的评测

### 6.1 在线评测
- **A/B测试**: 对比不同推荐策略的效果
- **用户反馈**: 收集学生和教师的使用反馈
- **学习效果跟踪**: 长期跟踪学生的学习进步

### 6.2 离线评测
- **历史数据回放**: 使用历史数据模拟推荐效果
- **专家评估**: 邀请教育专家评估推荐质量
- **基准对比**: 与其他认知诊断模型对比

## 7. 评测结果解读

### 7.1 指标权衡
- **准确性 vs 覆盖性**: 高准确性可能牺牲知识点覆盖
- **效率 vs 效果**: 快速诊断可能影响诊断精度
- **个性化 vs 通用性**: 个性化推荐可能降低通用性

### 7.2 应用场景适配
- **形成性评估**: 注重实时性和适应性
- **总结性评估**: 注重准确性和全面性
- **诊断性评估**: 注重精确性和可解释性

通过这套完整的评测体系，EduBrain能够在多个维度上验证其推荐效果，确保在实际教育场景中的有效性和可靠性。
