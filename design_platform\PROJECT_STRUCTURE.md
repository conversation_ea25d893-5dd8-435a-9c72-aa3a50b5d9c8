# 项目结构说明

## 📁 目录结构

```
design_platform/
├── 📄 README.md                    # 项目说明和使用指南
├── 📄 PROJECT_STRUCTURE.md         # 项目结构说明（本文件）
├── 🚀 start_dev.bat               # Windows 快速启动脚本
├── 🚀 start_dev.sh                # Linux/macOS 快速启动脚本
│
├── 📁 backend/                     # 后端服务 (FastAPI)
│   ├── 📁 app/                    # 应用核心代码
│   │   ├── 📁 api/               # API 路由定义
│   │   ├── 📁 core/              # 核心配置和设置
│   │   ├── 📁 models/            # 数据库模型
│   │   ├── 📁 schemas/           # Pydantic 数据模式
│   │   ├── 📁 services/          # 业务逻辑服务
│   │   └── 📄 main.py            # FastAPI 应用入口
│   ├── 📁 data/                  # 数据文件存储
│   ├── 📁 datasets/              # 数据集文件
│   ├── 📁 routes/                # 额外的路由定义
│   ├── 📁 scripts/               # 工具脚本
│   ├── 📁 services/              # 服务层实现
│   ├── 📁 static/                # 静态文件
│   ├── 📁 tasks/                 # 异步任务
│   ├── 📁 tests/                 # 测试文件
│   ├── 📁 uploads/               # 文件上传目录
│   ├── 📄 requirements.txt       # Python 依赖列表
│   ├── 📄 Dockerfile             # Docker 配置
│   ├── 🗄️ edubrain_platform.db   # 主数据库
│   └── 🗄️ edubrain_platform.db   # 教育大脑数据库
│
├── 📁 frontend/                   # 前端应用 (React + TypeScript)
│   ├── 📁 public/                # 公共静态资源
│   │   ├── 📄 index.html         # HTML 模板
│   │   ├── 🖼️ favicon.svg        # 网站图标
│   │   └── 📄 manifest.json      # PWA 配置
│   ├── 📁 src/                   # 源代码
│   │   ├── 📁 components/        # React 组件
│   │   ├── 📁 pages/             # 页面组件
│   │   ├── 📁 services/          # API 服务
│   │   ├── 📁 types/             # TypeScript 类型定义
│   │   ├── 📁 utils/             # 工具函数
│   │   ├── 📄 App.tsx            # 主应用组件
│   │   └── 📄 index.tsx          # 应用入口
│   ├── 📄 package.json           # Node.js 依赖和脚本
│   └── 📄 tsconfig.json          # TypeScript 配置
│
├── 📁 docs/                       # 项目文档
│   ├── 📄 user_guide.md          # 用户使用指南
│   ├── 📄 deployment.md          # 部署指南
│   ├── 📄 EduBrain_Evaluation_Guide.md  # EduBrain 评估指南
│   └── 📄 cognitive_diagnosis_output_format.md  # 认知诊断输出格式
│
└── 📁 scripts/                    # 项目脚本
    ├── 🧹 clean_project.py       # 项目清理脚本
    ├── 🚀 start_dev.py           # Python 启动脚本
    ├── 📄 expose-services.ps1    # 服务暴露脚本
    ├── 📄 quick-expose.ps1       # 快速暴露脚本
    ├── 📄 restore-config.ps1     # 配置恢复脚本
    └── 📄 update-api-config.ps1  # API 配置更新脚本
```

## 🔧 核心组件说明

### 后端 (FastAPI)

- **app/main.py**: FastAPI 应用的主入口点
- **app/api/**: REST API 路由定义
- **app/models/**: SQLAlchemy 数据库模型
- **app/schemas/**: Pydantic 数据验证模式
- **app/services/**: 业务逻辑实现
- **app/core/**: 应用配置和核心设置

### 前端 (React + TypeScript)

- **src/App.tsx**: 主应用组件，包含路由配置
- **src/components/**: 可复用的 React 组件
- **src/pages/**: 页面级组件
- **src/services/**: API 调用服务
- **src/types/**: TypeScript 类型定义
- **src/utils/**: 工具函数和帮助方法

### 数据库

- **edubrain_platform_main.db**: 主要的 SQLite 数据库
- **edubrain_platform.db**: 教育大脑相关数据

### 配置文件

- **backend/requirements.txt**: Python 依赖包列表
- **frontend/package.json**: Node.js 依赖和脚本
- **frontend/tsconfig.json**: TypeScript 编译配置

## 🚀 快速启动

### Windows 用户
```bash
# 双击运行或在命令行执行
start_dev.bat
```

### Linux/macOS 用户
```bash
# 给脚本执行权限（首次运行）
chmod +x start_dev.sh

# 运行启动脚本
./start_dev.sh
```

### 手动启动

#### 后端
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端
```bash
cd frontend
npm install
npm start
```

## 🧹 项目清理

运行清理脚本来删除临时文件和缓存：

```bash
cd scripts
python clean_project.py
```

## 📊 服务地址

- **前端应用**: http://localhost:3000
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **ReDoc 文档**: http://localhost:8000/redoc

## 🔍 开发工具

### 推荐的 IDE 扩展

**VS Code 扩展**:
- Python
- TypeScript and JavaScript Language Features
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Auto Rename Tag

### 调试

- **后端调试**: 使用 VS Code 的 Python 调试器
- **前端调试**: 使用浏览器开发者工具
- **API 测试**: 使用 Swagger UI (http://localhost:8000/docs)

## 📝 开发规范

### 代码风格

- **Python**: 遵循 PEP 8 规范
- **TypeScript/React**: 使用 ESLint 和 Prettier
- **提交信息**: 使用语义化提交信息

### 文件命名

- **组件文件**: PascalCase (如 `UserProfile.tsx`)
- **工具文件**: camelCase (如 `apiUtils.ts`)
- **页面文件**: PascalCase (如 `Dashboard.tsx`)
- **Python 文件**: snake_case (如 `user_service.py`)

## 🔒 环境变量

可以在以下位置配置环境变量：
- `backend/.env` - 后端环境变量
- `frontend/.env` - 前端环境变量

## 📦 生产部署

### 构建前端应用
```bash
cd frontend
npm run build
```

### 启动生产服务
```bash
# 后端（生产模式）
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# 前端构建文件可以通过 Web 服务器提供服务
```

详细的部署说明请参考 [docs/deployment.md](docs/deployment.md)。
