/**
 * PDF生成工具 - 简化版本
 * 用于将诊断报告导出为PDF格式
 */

interface DiagnosisData {
  student_id: string;
  overall_ability: number;
  knowledge_diagnosis: Record<string, any>;
  llm_report?: any;
  diagnosis_time: string;
}

export const generateDiagnosisPDF = async (diagnosisData: DiagnosisData): Promise<void> => {
  try {
    // 动态导入所需库
    const [{ jsPDF }, html2canvas] = await Promise.all([
      import('jspdf'),
      import('html2canvas')
    ]);

    // 创建临时的HTML容器
    const container = document.createElement('div');
    container.style.position = 'fixed';
    container.style.left = '-9999px';
    container.style.top = '0';
    container.style.width = '800px';
    container.style.backgroundColor = '#ffffff';
    container.style.padding = '20px';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.fontSize = '14px';
    container.style.lineHeight = '1.5';
    container.style.color = '#333';

    // 生成简化的HTML内容
    container.innerHTML = generateSimpleHTML(diagnosisData);
    document.body.appendChild(container);

    // 等待渲染
    await new Promise(resolve => setTimeout(resolve, 500));

    // 生成canvas
    const canvas = await html2canvas.default(container, {
      scale: 1.5,
      useCORS: true,
      backgroundColor: '#ffffff',
      width: 800,
      height: container.scrollHeight
    });

    // 创建PDF
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgWidth = 190;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    const pageHeight = 280; // A4页面高度（减去边距）

    // 如果内容高度超过一页，需要分页
    if (imgHeight > pageHeight) {
      let yPosition = 10;
      let remainingHeight = imgHeight;
      let sourceY = 0;

      while (remainingHeight > 0) {
        const currentPageHeight = Math.min(remainingHeight, pageHeight);
        const sourceHeight = (currentPageHeight * canvas.height) / imgHeight;

        // 创建临时canvas来截取当前页面内容
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        tempCanvas.width = canvas.width;
        tempCanvas.height = sourceHeight;

        tempCtx?.drawImage(
          canvas,
          0, sourceY, canvas.width, sourceHeight,
          0, 0, canvas.width, sourceHeight
        );

        // 添加到PDF
        pdf.addImage(tempCanvas.toDataURL('image/png'), 'PNG', 10, yPosition, imgWidth, currentPageHeight);

        remainingHeight -= currentPageHeight;
        sourceY += sourceHeight;

        // 如果还有内容，添加新页面
        if (remainingHeight > 0) {
          pdf.addPage();
          yPosition = 10;
        }
      }
    } else {
      // 内容适合一页
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 10, 10, imgWidth, imgHeight);
    }

    pdf.save(`诊断报告_${diagnosisData.student_id}.pdf`);

    // 清理
    document.body.removeChild(container);

  } catch (error) {
    console.error('PDF生成失败:', error);
    alert('PDF生成失败，请重试');
  }
};

// 生成简化的HTML内容
const generateSimpleHTML = (diagnosisData: DiagnosisData): string => {
  const abilityLevel = diagnosisData.overall_ability > 0.8 ? '优秀' :
                      diagnosisData.overall_ability > 0.6 ? '良好' :
                      diagnosisData.overall_ability > 0.4 ? '中等' : '需要提升';

  const knowledgeEntries = Object.entries(diagnosisData.knowledge_diagnosis || {});
  const masteredCount = knowledgeEntries.filter(([_, data]: [string, any]) =>
    (data.mastery_probability || 0) > 0.7).length;
  const partialCount = knowledgeEntries.filter(([_, data]: [string, any]) => {
    const prob = data.mastery_probability || 0;
    return prob > 0.5 && prob <= 0.7;
  }).length;
  const weakCount = knowledgeEntries.length - masteredCount - partialCount;



  return `
    <div style="font-family: Arial, sans-serif; color: #333; background: #fff; padding: 0;">
      <!-- 头部 -->
      <div style="text-align: center; margin-bottom: 30px; padding: 25px; background: #4a90e2; color: white; border-radius: 8px;">
        <h1 style="margin: 0 0 10px 0; font-size: 24px; font-weight: bold;">🤖 AI智能认知诊断报告</h1>
        <p style="margin: 0; font-size: 14px;">基于ORCDF深度学习模型的个性化学习分析</p>
      </div>

      <!-- 基本信息 -->
      <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #1890ff;">
        <h2 style="color: #1890ff; margin: 0 0 15px 0; font-size: 18px; font-weight: bold;">📋 基本信息</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div><strong>学生ID:</strong> ${diagnosisData.student_id}</div>
          <div><strong>诊断时间:</strong> ${new Date(diagnosisData.diagnosis_time).toLocaleString()}</div>
          <div><strong>整体能力:</strong> <span style="color: #52c41a; font-weight: bold;">${(diagnosisData.overall_ability * 100).toFixed(1)}% (${abilityLevel})</span></div>
          <div><strong>报告类型:</strong> <span style="color: #1890ff;">AI智能分析</span></div>
        </div>
      </div>

      <!-- 能力概览 -->
      <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e8e8e8;">
        <h2 style="margin: 0 0 15px 0; color: #52c41a; font-size: 18px; font-weight: bold;">📊 能力概览</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; text-align: center;">
          <div style="padding: 15px; background: #f6ffed; border-radius: 8px; border: 1px solid #b7eb8f;">
            <div style="font-size: 24px; font-weight: bold; color: #52c41a;">${masteredCount}</div>
            <div style="color: #666; font-size: 14px;">已掌握知识点</div>
          </div>
          <div style="padding: 15px; background: #fffbe6; border-radius: 8px; border: 1px solid #ffe58f;">
            <div style="font-size: 24px; font-weight: bold; color: #faad14;">${partialCount}</div>
            <div style="color: #666; font-size: 14px;">部分掌握</div>
          </div>
          <div style="padding: 15px; background: #fff2f0; border-radius: 8px; border: 1px solid #ffccc7;">
            <div style="font-size: 24px; font-weight: bold; color: #ff4d4f;">${weakCount}</div>
            <div style="color: #666; font-size: 14px;">需要加强</div>
          </div>
        </div>
      </div>

      <!-- 知识点详情 -->
      <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e8e8e8;">
        <h2 style="margin: 0 0 15px 0; color: #722ed1; font-size: 18px; font-weight: bold;">📚 知识点掌握详情</h2>
        <div style="display: grid; gap: 8px;">
          ${knowledgeEntries.map(([kc, data]: [string, any]) => {
            const prob = data.mastery_probability || 0;
            const level = data.mastery_level || '未知';
            const color = prob > 0.7 ? '#52c41a' : prob > 0.5 ? '#faad14' : '#ff4d4f';
            const bgColor = prob > 0.7 ? '#f6ffed' : prob > 0.5 ? '#fffbe6' : '#fff2f0';

            return `
              <div style="padding: 12px; background: ${bgColor}; border-radius: 6px; border-left: 4px solid ${color}; display: flex; justify-content: space-between; align-items: center;">
                <span style="font-weight: bold;">${kc}</span>
                <div>
                  <span style="color: ${color}; font-weight: bold;">${(prob * 100).toFixed(1)}%</span>
                  <span style="margin-left: 10px; padding: 2px 8px; background: ${color}; color: white; border-radius: 12px; font-size: 12px;">${level}</span>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>

      ${diagnosisData.llm_report ? generateLLMReportHTML(diagnosisData.llm_report) : ''}

      <!-- 页脚 -->
      <div style="text-align: center; margin-top: 30px; padding: 20px; border-top: 1px solid #e8e8e8; color: #666; font-size: 12px;">
        <p style="margin: 0;">本报告由AI智能认知诊断系统生成 | 生成时间: ${new Date().toLocaleString()}</p>
        <p style="margin: 5px 0 0 0;">基于ORCDF深度学习模型 | 仅供学习参考</p>
      </div>
    </div>
  `;
};

// 生成LLM报告HTML
const generateLLMReportHTML = (llmReport: any): string => {
  if (!llmReport) return '';

  return `
    <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #e8e8e8;">
      <h2 style="margin: 0 0 15px 0; color: #722ed1; font-size: 18px; font-weight: bold;">🤖 AI智能分析报告</h2>
      <div style="line-height: 1.6;">
        ${llmReport.summary ? `
          <div style="margin-bottom: 15px;">
            <h3 style="color: #1890ff; font-size: 16px; margin: 0 0 8px 0;">学习现状总结</h3>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #1890ff;">
              ${llmReport.summary.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}

        ${llmReport.detailed_analysis ? `
          <div style="margin-bottom: 15px;">
            <h3 style="color: #1890ff; font-size: 16px; margin: 0 0 8px 0;">详细分析</h3>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #1890ff;">
              ${llmReport.detailed_analysis.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}

        ${llmReport.recommendations ? `
          <div style="margin-bottom: 15px;">
            <h3 style="color: #1890ff; font-size: 16px; margin: 0 0 8px 0;">学习建议</h3>
            <div style="padding: 10px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #1890ff;">
              ${llmReport.recommendations.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}
      </div>
    </div>
  `;
};