import React, { useState, useEffect } from 'react';
import { Card, Button, Progress, Tabs, Alert, Spin, Tag, Space, Typography, Divider, Row, Col } from 'antd';
import { PlayCircleOutlined, BookOutlined, EditOutlined, CheckCircleOutlined, ClockCircleOutlined, TrophyOutlined } from '@ant-design/icons';
import { LearningContent, Question, LearningProgress } from '../../types/learning';
import QuestionComponent from './QuestionComponent';
import LearningResourceList from './LearningResourceList';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

interface KnowledgePointLearningProps {
  knowledgePointId: string;
  knowledgePointName: string;
  onComplete?: (progress: LearningProgress) => void;
  onBack?: () => void;
}

const KnowledgePointLearning: React.FC<KnowledgePointLearningProps> = ({
  knowledgePointId,
  knowledgePointName,
  onComplete,
  onBack
}) => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('concept');
  const [learningContent, setLearningContent] = useState<LearningContent | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [startTime, setStartTime] = useState<Date>(new Date());
  const [progress, setProgress] = useState({
    conceptRead: false,
    examplesViewed: false,
    practiceCompleted: false,
    totalTime: 0
  });

  useEffect(() => {
    loadLearningContent();
  }, [knowledgePointId]);

  const loadLearningContent = async () => {
    setLoading(true);
    try {
      // 模拟加载学习内容
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockContent: LearningContent = {
        id: knowledgePointId,
        name: knowledgePointName,
        concept: {
          title: `${knowledgePointName}基础概念`,
          content: generateConceptContent(knowledgePointName),
          keyPoints: generateKeyPoints(knowledgePointName),
          difficulty: 'medium',
          estimatedTime: 15
        },
        examples: generateExamples(knowledgePointName),
        questions: generateQuestions(knowledgePointName),
        resources: generateResources(knowledgePointName)
      };
      
      setLearningContent(mockContent);
    } catch (error) {
      console.error('加载学习内容失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateConceptContent = (name: string): string => {
    const concepts: Record<string, string> = {
      '几何推理': `
        几何推理是数学中的重要分支，主要研究空间中点、线、面的位置关系和性质。
        
        **核心要素：**
        1. 点：没有大小，只有位置的几何对象
        2. 线：由无数个点组成，有长度但没有宽度
        3. 面：由无数条线组成，有长度和宽度但没有厚度
        4. 体：三维空间中的几何对象
        
        **基本定理：**
        - 两点确定一条直线
        - 三点确定一个平面（非共线）
        - 平行线的性质和判定
        - 相似三角形的性质
        
        **应用场景：**
        几何推理广泛应用于建筑设计、工程制图、计算机图形学等领域。
      `,
      '概率统计': `
        概率统计是研究随机现象规律性的数学分支，包括概率论和数理统计两部分。
        
        **基本概念：**
        1. 随机事件：在一定条件下可能发生也可能不发生的事件
        2. 概率：事件发生可能性的数值度量，取值范围[0,1]
        3. 样本空间：所有可能结果的集合
        4. 随机变量：将随机事件的结果用数值表示
        
        **重要公式：**
        - 古典概率：P(A) = 有利结果数 / 总结果数
        - 条件概率：P(A|B) = P(A∩B) / P(B)
        - 贝叶斯公式：P(A|B) = P(B|A)P(A) / P(B)
        
        **实际应用：**
        概率统计在金融风险评估、医学诊断、质量控制等领域有重要应用。
      `,
      '逻辑推理': `
        逻辑推理是根据已知条件，运用逻辑规则得出结论的思维过程。
        
        **推理类型：**
        1. 演绎推理：从一般到特殊的推理
        2. 归纳推理：从特殊到一般的推理
        3. 类比推理：从特殊到特殊的推理
        
        **逻辑连接词：**
        - 与（∧）：两个条件都成立
        - 或（∨）：至少一个条件成立
        - 非（¬）：条件不成立
        - 蕴含（→）：如果...那么...
        
        **推理规则：**
        - 肯定前件：如果P→Q且P为真，则Q为真
        - 否定后件：如果P→Q且Q为假，则P为假
        - 假言三段论：如果P→Q且Q→R，则P→R
        
        **应用领域：**
        逻辑推理在计算机科学、法律论证、科学研究等领域广泛应用。
      `
    };
    
    return concepts[name] || `
      ${name}是数学学习中的重要知识点，需要通过理论学习和实践练习来掌握。
      
      **学习目标：**
      1. 理解基本概念和原理
      2. 掌握解题方法和技巧
      3. 能够应用到实际问题中
      
      **学习建议：**
      - 先理解概念，再练习应用
      - 多做例题，总结规律
      - 注意与其他知识点的联系
    `;
  };

  const generateKeyPoints = (name: string): string[] => {
    const keyPoints: Record<string, string[]> = {
      '几何推理': [
        '掌握基本几何图形的性质',
        '理解平行线和相交线的关系',
        '学会使用几何定理进行推理',
        '培养空间想象能力'
      ],
      '概率统计': [
        '理解随机事件和概率的概念',
        '掌握概率计算的基本方法',
        '学会使用统计图表分析数据',
        '理解期望值和方差的意义'
      ],
      '逻辑推理': [
        '掌握基本逻辑连接词的用法',
        '理解各种推理规则',
        '学会构造逻辑论证',
        '培养批判性思维能力'
      ]
    };
    
    return keyPoints[name] || [
      '理解基本概念',
      '掌握解题方法',
      '培养应用能力',
      '建立知识联系'
    ];
  };

  const generateExamples = (name: string) => {
    return [
      {
        id: 1,
        title: `${name}基础例题`,
        problem: `这是一个关于${name}的基础例题，帮助理解核心概念。`,
        solution: '解题步骤：\n1. 分析题目条件\n2. 选择合适方法\n3. 逐步求解\n4. 验证答案',
        difficulty: 'easy' as const
      },
      {
        id: 2,
        title: `${name}进阶例题`,
        problem: `这是一个关于${name}的进阶例题，需要综合运用多个知识点。`,
        solution: '解题步骤：\n1. 理解题意\n2. 建立数学模型\n3. 运用相关定理\n4. 得出结论',
        difficulty: 'medium' as const
      }
    ];
  };

  const generateQuestions = (name: string): Question[] => {
    const questionTemplates: Record<string, Question[]> = {
      '几何推理': [
        {
          id: 1,
          type: 'multiple-choice',
          question: '在三角形ABC中，如果AB = AC，那么三角形ABC是：',
          options: ['等边三角形', '等腰三角形', '直角三角形', '钝角三角形'],
          correctAnswer: '等腰三角形',
          explanation: '当三角形的两边相等时，该三角形为等腰三角形。',
          difficulty: 'easy',
          points: 5
        },
        {
          id: 2,
          type: 'multiple-choice',
          question: '两条平行线被第三条直线所截，同位角的关系是：',
          options: ['相等', '互补', '互余', '无关系'],
          correctAnswer: '相等',
          explanation: '平行线的性质：两条平行线被第三条直线所截，同位角相等。',
          difficulty: 'medium',
          points: 8
        }
      ],
      '概率统计': [
        {
          id: 1,
          type: 'multiple-choice',
          question: '抛掷一枚均匀硬币，正面朝上的概率是：',
          options: ['0', '0.25', '0.5', '1'],
          correctAnswer: '0.5',
          explanation: '均匀硬币有两个等可能的结果，正面朝上的概率为1/2 = 0.5。',
          difficulty: 'easy',
          points: 5
        },
        {
          id: 2,
          type: 'multiple-choice',
          question: '从52张牌中抽取一张，抽到红桃的概率是：',
          options: ['1/4', '1/2', '1/13', '1/52'],
          correctAnswer: '1/4',
          explanation: '52张牌中有13张红桃，所以概率为13/52 = 1/4。',
          difficulty: 'medium',
          points: 8
        }
      ],
      '逻辑推理': [
        {
          id: 1,
          type: 'multiple-choice',
          question: '如果"所有的鸟都会飞"为真，"企鹅是鸟"为真，那么可以推出：',
          options: ['企鹅会飞', '企鹅不会飞', '无法确定', '前提矛盾'],
          correctAnswer: '前提矛盾',
          explanation: '这是一个经典的逻辑矛盾例子，因为企鹅是鸟但不会飞，所以前提"所有的鸟都会飞"是错误的。',
          difficulty: 'medium',
          points: 8
        },
        {
          id: 2,
          type: 'multiple-choice',
          question: '命题"如果下雨，那么地面湿润"的逆否命题是：',
          options: ['如果地面湿润，那么下雨', '如果不下雨，那么地面不湿润', '如果地面不湿润，那么不下雨', '如果下雨，那么地面不湿润'],
          correctAnswer: '如果地面不湿润，那么不下雨',
          explanation: '逆否命题是将原命题的条件和结论都否定后再交换位置。',
          difficulty: 'hard',
          points: 10
        }
      ]
    };
    
    return questionTemplates[name] || [
      {
        id: 1,
        type: 'multiple-choice',
        question: `关于${name}的基础问题`,
        options: ['选项A', '选项B', '选项C', '选项D'],
        correctAnswer: '选项A',
        explanation: '这是正确答案的解释。',
        difficulty: 'easy',
        points: 5
      }
    ];
  };

  const generateResources = (name: string) => {
    return [
      {
        id: 1,
        title: `${name}视频教程`,
        type: 'video' as const,
        url: '#',
        duration: '15分钟',
        description: `详细讲解${name}的核心概念和解题方法`
      },
      {
        id: 2,
        title: `${name}练习册`,
        type: 'document' as const,
        url: '#',
        description: `包含大量${name}练习题和详细解答`
      },
      {
        id: 3,
        title: `${name}知识图谱`,
        type: 'interactive' as const,
        url: '#',
        description: `交互式知识图谱，展示${name}与其他知识点的关系`
      }
    ];
  };

  const handleAnswerChange = (questionId: number, answer: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleSubmitPractice = () => {
    setShowResults(true);
    const endTime = new Date();
    const timeSpent = Math.round((endTime.getTime() - startTime.getTime()) / 1000 / 60); // 分钟

    const newProgress = {
      ...progress,
      practiceCompleted: true,
      totalTime: timeSpent
    };
    setProgress(newProgress);

    // 不立即调用 onComplete，让用户查看结果
    // onComplete 将在用户点击"完成学习"按钮时调用
  };

  const handleCompleteLearning = () => {
    if (onComplete) {
      const learningProgress: LearningProgress = {
        knowledgePointId,
        conceptRead: progress.conceptRead,
        examplesViewed: progress.examplesViewed,
        practiceCompleted: progress.practiceCompleted,
        score: calculateScore(),
        timeSpent: progress.totalTime,
        completedAt: new Date()
      };
      onComplete(learningProgress);
    }
  };

  const handleRetryPractice = () => {
    setShowResults(false);
    setAnswers({});
    setCurrentQuestionIndex(0);
    setStartTime(new Date());
    // 重置练习完成状态，但保留其他进度
    setProgress(prev => ({
      ...prev,
      practiceCompleted: false,
      totalTime: 0
    }));
  };

  const calculateScore = (): number => {
    if (!learningContent) return 0;
    
    let correctCount = 0;
    let totalPoints = 0;
    let earnedPoints = 0;
    
    learningContent.questions.forEach(question => {
      totalPoints += question.points;
      if (answers[question.id] === question.correctAnswer) {
        correctCount++;
        earnedPoints += question.points;
      }
    });
    
    return Math.round((earnedPoints / totalPoints) * 100);
  };

  const markConceptAsRead = () => {
    setProgress(prev => ({ ...prev, conceptRead: true }));
  };

  const markExamplesAsViewed = () => {
    setProgress(prev => ({ ...prev, examplesViewed: true }));
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载学习内容中...</div>
      </div>
    );
  }

  if (!learningContent) {
    return (
      <Alert
        message="加载失败"
        description="无法加载学习内容，请稍后重试。"
        type="error"
        showIcon
      />
    );
  }

  const overallProgress = [
    progress.conceptRead,
    progress.examplesViewed,
    progress.practiceCompleted
  ].filter(Boolean).length / 3 * 100;

  return (
    <div style={{ maxWidth: 1200, margin: '0 auto', padding: '20px' }}>
      {/* 头部信息 */}
      <Card style={{ marginBottom: 20 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              <BookOutlined /> {learningContent.name}
            </Title>
            <Space style={{ marginTop: 8 }}>
              <Tag color="blue">{learningContent.concept.difficulty}</Tag>
              <Text type="secondary">
                <ClockCircleOutlined /> 预计学习时间: {learningContent.concept.estimatedTime}分钟
              </Text>
            </Space>
          </div>
          <div style={{ textAlign: 'right' }}>
            <div>学习进度</div>
            <Progress 
              type="circle" 
              percent={Math.round(overallProgress)} 
              size={80}
              status={overallProgress === 100 ? 'success' : 'active'}
            />
          </div>
        </div>
        {onBack && (
          <Button onClick={onBack} style={{ marginTop: 16 }}>
            返回推荐列表
          </Button>
        )}
      </Card>

      {/* 学习内容标签页 */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <BookOutlined />
                概念学习
                {progress.conceptRead && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: 4 }} />}
              </span>
            }
            key="concept"
          >
            <div style={{ maxWidth: 900, margin: '0 auto' }}>
              {/* 概念标题区域 */}
              <div style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
                padding: '24px',
                marginBottom: '24px',
                color: 'white'
              }}>
                <Title level={2} style={{ color: 'white', margin: 0, marginBottom: '8px' }}>
                  {learningContent.concept.title}
                </Title>
                <div style={{ opacity: 0.9, fontSize: '16px' }}>
                  预计学习时间: {learningContent.concept.estimatedTime}分钟
                </div>
              </div>

              {/* 概念内容区域 */}
              <Card style={{ marginBottom: '24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                <div style={{
                  fontSize: '16px',
                  lineHeight: '1.8',
                  color: '#333',
                  whiteSpace: 'pre-line'
                }}>
                  {learningContent.concept.content.split('\n').map((paragraph, index) => {
                    if (paragraph.trim().startsWith('**') && paragraph.trim().endsWith('**')) {
                      // 处理粗体标题
                      return (
                        <div key={index} style={{
                          fontSize: '18px',
                          fontWeight: 'bold',
                          color: '#1890ff',
                          margin: '20px 0 12px 0'
                        }}>
                          {paragraph.replace(/\*\*/g, '')}
                        </div>
                      );
                    } else if (paragraph.trim().startsWith('- ')) {
                      // 处理列表项
                      return (
                        <div key={index} style={{
                          marginLeft: '20px',
                          marginBottom: '8px',
                          position: 'relative'
                        }}>
                          <span style={{
                            position: 'absolute',
                            left: '-16px',
                            color: '#1890ff',
                            fontWeight: 'bold'
                          }}>•</span>
                          {paragraph.substring(2)}
                        </div>
                      );
                    } else if (paragraph.trim()) {
                      // 处理普通段落
                      return (
                        <div key={index} style={{ marginBottom: '16px' }}>
                          {paragraph}
                        </div>
                      );
                    }
                    return null;
                  })}
                </div>
              </Card>

              {/* 学习要点区域 */}
              <Card
                title={
                  <span style={{ color: '#1890ff', fontSize: '18px' }}>
                    <TrophyOutlined style={{ marginRight: '8px' }} />
                    学习要点
                  </span>
                }
                style={{ marginBottom: '24px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
              >
                <Row gutter={[16, 16]}>
                  {learningContent.concept.keyPoints.map((point, index) => (
                    <Col span={12} key={index}>
                      <div style={{
                        padding: '16px',
                        background: '#f8f9fa',
                        borderRadius: '8px',
                        borderLeft: '4px solid #1890ff',
                        height: '100%'
                      }}>
                        <div style={{
                          fontSize: '14px',
                          color: '#666',
                          marginBottom: '4px'
                        }}>
                          要点 {index + 1}
                        </div>
                        <div style={{
                          fontSize: '16px',
                          color: '#333',
                          fontWeight: '500'
                        }}>
                          {point}
                        </div>
                      </div>
                    </Col>
                  ))}
                </Row>
              </Card>

              {/* 完成按钮区域 */}
              <div style={{
                textAlign: 'center',
                padding: '24px',
                background: '#fafafa',
                borderRadius: '8px'
              }}>
                <Button
                  type="primary"
                  size="large"
                  onClick={markConceptAsRead}
                  disabled={progress.conceptRead}
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    borderRadius: '24px',
                    paddingLeft: '32px',
                    paddingRight: '32px'
                  }}
                >
                  {progress.conceptRead ? (
                    <>
                      <CheckCircleOutlined /> 已完成概念学习
                    </>
                  ) : (
                    <>
                      <BookOutlined /> 标记为已读
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <PlayCircleOutlined />
                例题演示
                {progress.examplesViewed && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: 4 }} />}
              </span>
            }
            key="examples"
          >
            <div style={{ maxWidth: 900, margin: '0 auto' }}>
              {/* 例题标题区域 */}
              <div style={{
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                borderRadius: '12px',
                padding: '24px',
                marginBottom: '24px',
                color: 'white'
              }}>
                <Title level={2} style={{ color: 'white', margin: 0, marginBottom: '8px' }}>
                  <PlayCircleOutlined style={{ marginRight: '8px' }} />
                  例题演示
                </Title>
                <div style={{ opacity: 0.9, fontSize: '16px' }}>
                  通过典型例题掌握解题方法和思路
                </div>
              </div>

              {learningContent.examples.map((example, index) => (
                <Card
                  key={example.id}
                  style={{
                    marginBottom: '24px',
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                    borderRadius: '12px',
                    overflow: 'hidden'
                  }}
                >
                  {/* 例题头部 */}
                  <div style={{
                    background: index % 2 === 0 ? '#f0f9ff' : '#fff7ed',
                    padding: '20px',
                    marginBottom: '20px',
                    borderRadius: '8px'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                        例题 {index + 1}: {example.title}
                      </Title>
                      <Tag
                        color={example.difficulty === 'easy' ? 'success' : example.difficulty === 'medium' ? 'warning' : 'error'}
                        style={{ fontSize: '14px', padding: '4px 12px' }}
                      >
                        {example.difficulty === 'easy' ? '简单' : example.difficulty === 'medium' ? '中等' : '困难'}
                      </Tag>
                    </div>
                  </div>

                  {/* 题目内容 */}
                  <div style={{ marginBottom: '24px' }}>
                    <div style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: '#1890ff',
                      marginBottom: '12px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        background: '#1890ff',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '8px',
                        fontSize: '14px'
                      }}>
                        题
                      </div>
                      题目描述
                    </div>
                    <div style={{
                      background: '#fafafa',
                      padding: '16px',
                      borderRadius: '8px',
                      borderLeft: '4px solid #1890ff',
                      fontSize: '16px',
                      lineHeight: '1.6'
                    }}>
                      {example.problem}
                    </div>
                  </div>

                  {/* 解答过程 */}
                  <div>
                    <div style={{
                      fontSize: '16px',
                      fontWeight: 'bold',
                      color: '#52c41a',
                      marginBottom: '12px',
                      display: 'flex',
                      alignItems: 'center'
                    }}>
                      <div style={{
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        background: '#52c41a',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginRight: '8px',
                        fontSize: '14px'
                      }}>
                        解
                      </div>
                      解题步骤
                    </div>
                    <div style={{
                      background: '#f6ffed',
                      padding: '16px',
                      borderRadius: '8px',
                      borderLeft: '4px solid #52c41a',
                      whiteSpace: 'pre-line',
                      fontSize: '16px',
                      lineHeight: '1.8'
                    }}>
                      {example.solution}
                    </div>
                  </div>
                </Card>
              ))}

              {/* 完成按钮区域 */}
              <div style={{
                textAlign: 'center',
                padding: '24px',
                background: '#fafafa',
                borderRadius: '8px'
              }}>
                <Button
                  type="primary"
                  size="large"
                  onClick={markExamplesAsViewed}
                  disabled={progress.examplesViewed}
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    borderRadius: '24px',
                    paddingLeft: '32px',
                    paddingRight: '32px'
                  }}
                >
                  {progress.examplesViewed ? (
                    <>
                      <CheckCircleOutlined /> 已完成例题学习
                    </>
                  ) : (
                    <>
                      <PlayCircleOutlined /> 标记为已学习
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <EditOutlined />
                练习题目
                {progress.practiceCompleted && <CheckCircleOutlined style={{ color: '#52c41a', marginLeft: 4 }} />}
              </span>
            } 
            key="practice"
          >
            <div style={{ maxWidth: 900, margin: '0 auto' }}>
              {!showResults ? (
                <>
                  {/* 练习标题区域 */}
                  <div style={{
                    background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                    borderRadius: '12px',
                    padding: '24px',
                    marginBottom: '24px',
                    color: 'white'
                  }}>
                    <Title level={2} style={{ color: 'white', margin: 0, marginBottom: '8px' }}>
                      <EditOutlined style={{ marginRight: '8px' }} />
                      练习题目
                    </Title>
                    <div style={{ opacity: 0.9, fontSize: '16px' }}>
                      请认真完成以下练习题，这将帮助您巩固所学知识
                    </div>
                  </div>
                  
                  {learningContent.questions.map((question, index) => (
                    <QuestionComponent
                      key={question.id}
                      question={question}
                      questionNumber={index + 1}
                      selectedAnswer={answers[question.id]}
                      onAnswerChange={(answer) => handleAnswerChange(question.id, answer)}
                      showResult={false}
                    />
                  ))}
                  
                  {/* 提交按钮区域 */}
                  <div style={{
                    textAlign: 'center',
                    padding: '24px',
                    background: '#fafafa',
                    borderRadius: '8px',
                    marginTop: '24px'
                  }}>
                    <Button
                      type="primary"
                      size="large"
                      onClick={handleSubmitPractice}
                      disabled={Object.keys(answers).length < learningContent.questions.length}
                      style={{
                        height: '48px',
                        fontSize: '16px',
                        borderRadius: '24px',
                        paddingLeft: '32px',
                        paddingRight: '32px'
                      }}
                    >
                      <EditOutlined /> 提交答案
                    </Button>
                    <div style={{ marginTop: '12px', color: '#666' }}>
                      已完成 {Object.keys(answers).length} / {learningContent.questions.length} 题
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {/* 完成结果区域 */}
                  <div style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '12px',
                    padding: '24px',
                    marginBottom: '24px',
                    color: 'white',
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                      🎉
                    </div>
                    <Title level={2} style={{ color: 'white', margin: 0, marginBottom: '8px' }}>
                      练习完成！
                    </Title>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '8px' }}>
                      得分：{calculateScore()}分
                    </div>
                    <div style={{ opacity: 0.9, fontSize: '16px' }}>
                      用时 {progress.totalTime} 分钟 | 正确率 {Math.round((calculateScore() / (learningContent.questions.reduce((sum, q) => sum + q.points, 0))) * 100)}%
                    </div>
                  </div>
                  
                  {learningContent.questions.map((question, index) => (
                    <QuestionComponent
                      key={question.id}
                      question={question}
                      questionNumber={index + 1}
                      selectedAnswer={answers[question.id]}
                      onAnswerChange={() => {}}
                      showResult={true}
                    />
                  ))}

                  {/* 完成学习按钮区域 */}
                  <div style={{
                    textAlign: 'center',
                    padding: '24px',
                    background: '#f8f9fa',
                    borderRadius: '8px',
                    marginTop: '24px'
                  }}>
                    <Space size="large">
                      <Button
                        size="large"
                        onClick={handleRetryPractice}
                        style={{
                          height: '48px',
                          fontSize: '16px',
                          borderRadius: '24px',
                          paddingLeft: '32px',
                          paddingRight: '32px'
                        }}
                      >
                        重新练习
                      </Button>
                      <Button
                        type="primary"
                        size="large"
                        onClick={handleCompleteLearning}
                        icon={<CheckCircleOutlined />}
                        style={{
                          height: '48px',
                          fontSize: '16px',
                          borderRadius: '24px',
                          paddingLeft: '32px',
                          paddingRight: '32px',
                          background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
                          border: 'none'
                        }}
                      >
                        完成学习
                      </Button>
                    </Space>
                    <div style={{ marginTop: '12px', color: '#666', fontSize: '14px' }}>
                      点击"完成学习"返回学习路径，或"重新练习"再次练习
                    </div>
                  </div>
                </>
              )}
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <BookOutlined />
                学习资源
              </span>
            }
            key="resources"
          >
            <div style={{ maxWidth: 900, margin: '0 auto' }}>
              {/* 资源标题区域 */}
              <div style={{
                background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                borderRadius: '12px',
                padding: '24px',
                marginBottom: '24px',
                color: 'white'
              }}>
                <Title level={2} style={{ color: 'white', margin: 0, marginBottom: '8px' }}>
                  <BookOutlined style={{ marginRight: '8px' }} />
                  学习资源
                </Title>
                <div style={{ opacity: 0.9, fontSize: '16px' }}>
                  丰富的学习资源帮助您深入理解知识点
                </div>
              </div>

              <LearningResourceList resources={learningContent.resources} />
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default KnowledgePointLearning;
