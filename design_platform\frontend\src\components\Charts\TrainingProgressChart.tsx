import React from 'react';
import ReactECharts from 'echarts-for-react';

interface TrainingProgressChartProps {
  data: {
    epochs: number[];
    trainLoss: number[];
    valLoss?: number[];
    trainAuc: number[];
    valAuc?: number[];
  };
  title?: string;
  height?: string;
}

const TrainingProgressChart: React.FC<TrainingProgressChartProps> = ({
  data,
  title = '训练进度',
  height = '400px',
}) => {
  const option = {
    title: {
      text: title,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
        },
      },
    },
    legend: {
      data: ['训练损失', '验证损失', '训练AUC', '验证AUC'],
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.epochs,
      name: 'Epoch',
      nameLocation: 'middle',
      nameGap: 30,
    },
    yAxis: [
      {
        type: 'value',
        name: 'Loss',
        position: 'left',
        axisLabel: {
          formatter: '{value}',
        },
      },
      {
        type: 'value',
        name: 'AUC',
        position: 'right',
        min: 0,
        max: 1,
        axisLabel: {
          formatter: '{value}',
        },
      },
    ],
    series: [
      {
        name: '训练损失',
        type: 'line',
        yAxisIndex: 0,
        data: data.trainLoss,
        smooth: true,
        lineStyle: {
          color: '#ff7f0e',
        },
        itemStyle: {
          color: '#ff7f0e',
        },
      },
      ...(data.valLoss
        ? [
            {
              name: '验证损失',
              type: 'line',
              yAxisIndex: 0,
              data: data.valLoss,
              smooth: true,
              lineStyle: {
                color: '#d62728',
              },
              itemStyle: {
                color: '#d62728',
              },
            },
          ]
        : []),
      {
        name: '训练AUC',
        type: 'line',
        yAxisIndex: 1,
        data: data.trainAuc,
        smooth: true,
        lineStyle: {
          color: '#2ca02c',
        },
        itemStyle: {
          color: '#2ca02c',
        },
      },
      ...(data.valAuc
        ? [
            {
              name: '验证AUC',
              type: 'line',
              yAxisIndex: 1,
              data: data.valAuc,
              smooth: true,
              lineStyle: {
                color: '#1f77b4',
              },
              itemStyle: {
                color: '#1f77b4',
              },
            },
          ]
        : []),
    ],
  };

  return <ReactECharts option={option} style={{ height }} />;
};

export default TrainingProgressChart;
