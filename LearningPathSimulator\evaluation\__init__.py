"""
Learning Path Simulator - Evaluation Module

评估模块包含性能指标、统计分析和可视化功能。
"""

from .metrics import (
    LearningEfficiencyMetric, LearningEffectivenessMetric, StabilityMetric,
    SuccessRateMetric, AverageRewardMetric, AverageStepsMetric
)
from .statistics import (
    WelchTTest, CohenDEffect, BonferroniCorrection,
    ConfidenceInterval, StatisticalAnalyzer
)
from .evaluator import Evaluator, ParallelEvaluator
from .visualization import (
    PerformanceVisualizer, LearningCurveVisualizer, 
    ComparisonVisualizer, StatisticalVisualizer
)

__all__ = [
    # Metrics
    'LearningEfficiencyMetric',
    'LearningEffectivenessMetric', 
    'StabilityMetric',
    'SuccessRateMetric',
    'AverageRewardMetric',
    'AverageStepsMetric',
    
    # Statistics
    'WelchTTest',
    'CohenDEffect',
    'BonferroniCorrection',
    'ConfidenceInterval',
    'StatisticalAnalyzer',
    
    # Evaluators
    'Evaluator',
    'ParallelEvaluator',
    
    # Visualization
    'PerformanceVisualizer',
    'LearningCurveVisualizer',
    'ComparisonVisualizer',
    'StatisticalVisualizer'
]
