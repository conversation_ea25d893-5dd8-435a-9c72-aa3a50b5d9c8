"""
EduSim集成评估服务
用于评估学习路径规划的效果
"""

import sys
import os
import numpy as np
import gym
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime

# 添加EduSim路径
sys.path.append(os.path.join(os.path.dirname(__file__), "../../../EduSim"))

try:
    import EduSim
    from EduSim.Envs.TMS import TMSEnv, TMSAgent
    from EduSim.Envs.KSS import KSSEnv, KSSAgent  
    from EduSim.SimOS import train_eval, MetaAgent
    EDUSIM_AVAILABLE = True
except ImportError as e:
    logging.warning(f"EduSim not available: {e}")
    EDUSIM_AVAILABLE = False

logger = logging.getLogger(__name__)

class PathPlanningAgent(MetaAgent):
    """基于路径规划结果的智能体"""
    
    def __init__(self, learning_path: List[Dict], action_space):
        self.learning_path = learning_path
        self.action_space = action_space
        self.current_step = 0
        self.path_sequence = self._convert_path_to_sequence()
        
    def _convert_path_to_sequence(self) -> List[int]:
        """将学习路径转换为动作序列"""
        sequence = []
        for stage in self.learning_path:
            # 根据知识点映射到动作空间
            kc_name = stage.get('knowledge_component', '')
            # 简单映射：根据知识点名称哈希到动作ID
            action_id = hash(kc_name) % self.action_space.n
            sequence.append(action_id)
        return sequence
    
    def begin_episode(self, *args, **kwargs):
        self.current_step = 0
        
    def end_episode(self, observation, reward, done, info):
        pass
        
    def observe(self, observation, reward, done, info):
        pass
        
    def step(self):
        if self.current_step < len(self.path_sequence):
            action = self.path_sequence[self.current_step]
            self.current_step += 1
            return action
        else:
            # 路径结束，随机选择
            return self.action_space.sample()
    
    def n_step(self, max_steps: int):
        return [self.step() for _ in range(min(max_steps, len(self.path_sequence)))]

class EduSimEvaluationService:
    """EduSim评估服务"""
    
    def __init__(self):
        self.available = EDUSIM_AVAILABLE
        self.environments = {}
        
    def is_available(self) -> bool:
        """检查EduSim是否可用"""
        return self.available
    
    def initialize_environment(self, env_type: str = "TMS", env_config: Dict = None) -> bool:
        """初始化仿真环境"""
        if not self.available:
            logger.error("EduSim not available")
            return False
            
        try:
            if env_type == "TMS":
                env = gym.make("TMS-v1", 
                             name="binary", 
                             parameters=env_config or {"test_item_for_each_skill": 4})
            elif env_type == "KSS":
                env = gym.make("KSS-v2", seed=10)
            else:
                logger.error(f"Unsupported environment type: {env_type}")
                return False
                
            self.environments[env_type] = env
            logger.info(f"Successfully initialized {env_type} environment")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize {env_type} environment: {e}")
            return False
    
    def evaluate_learning_path(self, 
                             learning_path: Dict[str, Any], 
                             env_type: str = "TMS",
                             max_episodes: int = 100,
                             max_steps: int = 10) -> Dict[str, Any]:
        """评估学习路径效果"""
        
        if not self.available:
            return {"error": "EduSim not available", "success": False}
            
        if env_type not in self.environments:
            if not self.initialize_environment(env_type):
                return {"error": f"Failed to initialize {env_type} environment", "success": False}
        
        try:
            env = self.environments[env_type]
            
            # 创建基于路径规划的智能体
            path_agent = PathPlanningAgent(
                learning_path.get('learning_path', []), 
                env.action_space
            )
            
            # 创建随机基线智能体
            from EduSim.SimOS import RandomAgent
            random_agent = RandomAgent(env.action_space)
            
            # 评估路径规划智能体
            path_results = self._run_evaluation(env, path_agent, max_episodes, max_steps, "PathPlanning")
            
            # 评估随机基线
            baseline_results = self._run_evaluation(env, random_agent, max_episodes, max_steps, "Random")
            
            # 计算改进效果
            improvement = self._calculate_improvement(path_results, baseline_results)
            
            return {
                "success": True,
                "evaluation_time": datetime.now().isoformat(),
                "environment": env_type,
                "path_planning_results": path_results,
                "baseline_results": baseline_results,
                "improvement": improvement,
                "learning_path_summary": self._summarize_learning_path(learning_path)
            }
            
        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            return {"error": str(e), "success": False}
    
    def _run_evaluation(self, env, agent, max_episodes: int, max_steps: int, agent_type: str) -> Dict:
        """运行单个智能体的评估"""
        
        episode_rewards = []
        episode_lengths = []
        final_scores = []
        
        for episode in range(max_episodes):
            try:
                # 重置环境
                observation = env.reset()
                agent.begin_episode()
                
                total_reward = 0
                steps = 0
                done = False
                
                while not done and steps < max_steps:
                    action = agent.step()
                    observation, reward, done, info = env.step(action)
                    agent.observe(observation, reward, done, info)
                    
                    total_reward += reward
                    steps += 1
                
                # 结束回合
                final_obs, final_reward, final_done, final_info = env.end_episode()
                agent.end_episode(final_obs, final_reward, final_done, final_info)
                
                episode_rewards.append(total_reward)
                episode_lengths.append(steps)
                
                # 记录最终得分
                if final_info and 'final_score' in final_info:
                    final_scores.append(final_info['final_score'])
                
            except Exception as e:
                logger.warning(f"Episode {episode} failed for {agent_type}: {e}")
                continue
        
        return {
            "agent_type": agent_type,
            "episodes_completed": len(episode_rewards),
            "average_reward": np.mean(episode_rewards) if episode_rewards else 0,
            "std_reward": np.std(episode_rewards) if episode_rewards else 0,
            "average_episode_length": np.mean(episode_lengths) if episode_lengths else 0,
            "average_final_score": np.mean(final_scores) if final_scores else 0,
            "success_rate": len([r for r in episode_rewards if r > 0]) / len(episode_rewards) if episode_rewards else 0
        }
    
    def _calculate_improvement(self, path_results: Dict, baseline_results: Dict) -> Dict:
        """计算改进效果"""
        
        improvements = {}
        
        metrics = ['average_reward', 'average_final_score', 'success_rate']
        
        for metric in metrics:
            path_value = path_results.get(metric, 0)
            baseline_value = baseline_results.get(metric, 0)
            
            if baseline_value > 0:
                improvement_pct = ((path_value - baseline_value) / baseline_value) * 100
            else:
                improvement_pct = 0
                
            improvements[f"{metric}_improvement"] = {
                "absolute": path_value - baseline_value,
                "percentage": improvement_pct,
                "path_value": path_value,
                "baseline_value": baseline_value
            }
        
        return improvements
    
    def _summarize_learning_path(self, learning_path: Dict) -> Dict:
        """总结学习路径信息"""
        
        path_stages = learning_path.get('learning_path', [])
        
        return {
            "total_stages": len(path_stages),
            "knowledge_components": [stage.get('knowledge_component', '') for stage in path_stages],
            "estimated_total_time": sum([stage.get('estimated_duration', 0) for stage in path_stages]),
            "difficulty_distribution": self._analyze_difficulty_distribution(path_stages)
        }
    
    def _analyze_difficulty_distribution(self, stages: List[Dict]) -> Dict:
        """分析难度分布"""
        
        difficulty_counts = {"easy": 0, "medium": 0, "hard": 0}
        
        for stage in stages:
            difficulty = stage.get('difficulty_level', 'medium')
            if difficulty in difficulty_counts:
                difficulty_counts[difficulty] += 1
        
        total = len(stages)
        if total > 0:
            difficulty_percentages = {k: (v/total)*100 for k, v in difficulty_counts.items()}
        else:
            difficulty_percentages = difficulty_counts
            
        return {
            "counts": difficulty_counts,
            "percentages": difficulty_percentages
        }
    
    def compare_multiple_paths(self, 
                             paths: List[Dict[str, Any]], 
                             env_type: str = "TMS",
                             max_episodes: int = 50) -> Dict[str, Any]:
        """比较多个学习路径的效果"""
        
        if not self.available:
            return {"error": "EduSim not available", "success": False}
        
        results = []
        
        for i, path in enumerate(paths):
            path_name = path.get('name', f'Path_{i+1}')
            logger.info(f"Evaluating path: {path_name}")
            
            result = self.evaluate_learning_path(
                path, env_type, max_episodes, max_steps=10
            )
            
            if result.get('success'):
                result['path_name'] = path_name
                results.append(result)
        
        if not results:
            return {"error": "No paths successfully evaluated", "success": False}
        
        # 找出最佳路径
        best_path = max(results, 
                       key=lambda x: x['path_planning_results']['average_reward'])
        
        return {
            "success": True,
            "comparison_time": datetime.now().isoformat(),
            "total_paths_evaluated": len(results),
            "best_path": best_path['path_name'],
            "best_path_performance": best_path['path_planning_results'],
            "all_results": results,
            "ranking": sorted(results, 
                            key=lambda x: x['path_planning_results']['average_reward'], 
                            reverse=True)
        }
