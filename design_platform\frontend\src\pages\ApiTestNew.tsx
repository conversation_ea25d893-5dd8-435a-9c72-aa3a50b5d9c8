import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Table, Tag, Divider, message, Row, Col } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, ApiOutlined } from '@ant-design/icons';
import { datasetService, experimentService, apiUtils } from '../services/api';

const { Title, Paragraph, Text } = Typography;

interface ApiTestResult {
  name: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  data?: any;
  timestamp: string;
}

const ApiTestNew: React.FC = () => {
  const [testResults, setTestResults] = useState<ApiTestResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [healthStatus, setHealthStatus] = useState<boolean | null>(null);
  const [datasets, setDatasets] = useState<any[]>([]);
  const [experiments, setExperiments] = useState<any[]>([]);

  useEffect(() => {
    runInitialTests();
  }, []);

  const addTestResult = (result: Omit<ApiTestResult, 'timestamp'>) => {
    const newResult: ApiTestResult = {
      ...result,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [newResult, ...prev.slice(0, 9)]);
  };

  const runInitialTests = async () => {
    setLoading(true);
    await testApiHealth();
    await testDatasetsApi();
    await testExperimentsApi();
    setLoading(false);
  };

  const testApiHealth = async () => {
    try {
      const isHealthy = await apiUtils.checkHealth();
      setHealthStatus(isHealthy);
      addTestResult({
        name: 'API健康检查',
        status: isHealthy ? 'success' : 'error',
        message: isHealthy ? 'API服务正常' : 'API服务异常'
      });
    } catch (error) {
      setHealthStatus(false);
      addTestResult({
        name: 'API健康检查',
        status: 'error',
        message: `健康检查失败: ${error}`
      });
    }
  };

  const testDatasetsApi = async () => {
    try {
      const response = await datasetService.getDatasets();
      setDatasets(response.data);
      addTestResult({
        name: '数据集API',
        status: 'success',
        message: `成功获取 ${response.data.length} 个数据集`,
        data: response.data
      });
    } catch (error) {
      addTestResult({
        name: '数据集API',
        status: 'error',
        message: `数据集API失败: ${error}`
      });
    }
  };

  const testExperimentsApi = async () => {
    try {
      const response = await experimentService.getExperiments();
      setExperiments(response.data);
      addTestResult({
        name: '实验API',
        status: 'success',
        message: `成功获取 ${response.data.length} 个实验`,
        data: response.data
      });
    } catch (error) {
      addTestResult({
        name: '实验API',
        status: 'error',
        message: `实验API失败: ${error}`
      });
    }
  };

  const testSyncApi = async () => {
    try {
      setLoading(true);
      const response = await datasetService.syncDatasets();
      addTestResult({
        name: '数据集同步',
        status: 'success',
        message: response.data.message || '同步成功'
      });
      message.success('数据集同步成功');
      await testDatasetsApi();
    } catch (error) {
      addTestResult({
        name: '数据集同步',
        status: 'error',
        message: `同步失败: ${error}`
      });
      message.error('数据集同步失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'loading':
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
      default:
        return null;
    }
  };

  const testResultColumns = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => getStatusIcon(status)
    },
    {
      title: '测试项目',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '结果',
      dataIndex: 'message',
      key: 'message',
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 100,
    }
  ];

  return (
    <div style={{ padding: '24px', maxWidth: '1400px', margin: '0 auto' }}>
      <Title level={2}>
        <ApiOutlined /> API 连接测试与诊断
      </Title>
      <Paragraph>
        实时测试前端与后端API的连接状态，诊断数据加载问题，确保所有功能正常工作。
      </Paragraph>

      {/* 健康状态指示器 */}
      <Alert
        message={
          <Space>
            <Text strong>API健康状态:</Text>
            {healthStatus === null ? (
              <Tag icon={<SyncOutlined spin />} color="processing">检查中</Tag>
            ) : healthStatus ? (
              <Tag icon={<CheckCircleOutlined />} color="success">正常</Tag>
            ) : (
              <Tag icon={<CloseCircleOutlined />} color="error">异常</Tag>
            )}
          </Space>
        }
        type={healthStatus ? 'success' : 'error'}
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Row gutter={[16, 16]}>
        <Col span={24}>
          {/* 测试控制面板 */}
          <Card title="🔧 测试控制面板" size="small">
            <Space wrap>
              <Button 
                type="primary" 
                onClick={runInitialTests}
                loading={loading}
                icon={<SyncOutlined />}
              >
                运行所有测试
              </Button>
              <Button onClick={testApiHealth} loading={loading}>
                健康检查
              </Button>
              <Button onClick={testDatasetsApi} loading={loading}>
                测试数据集API
              </Button>
              <Button onClick={testExperimentsApi} loading={loading}>
                测试实验API
              </Button>
              <Button onClick={testSyncApi} loading={loading}>
                测试同步API
              </Button>
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          {/* 测试结果 */}
          <Card title="📊 测试结果" size="small">
            <Table
              dataSource={testResults}
              columns={testResultColumns}
              pagination={false}
              size="small"
              rowKey={(record, index) => `${record.name}-${index}`}
            />
          </Card>
        </Col>

        <Col span={12}>
          {/* 数据统计 */}
          <Card title="📈 数据统计" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>数据集数量: </Text>
                <Tag color="blue">{datasets.length}</Tag>
              </div>
              <div>
                <Text strong>实验数量: </Text>
                <Tag color="green">{experiments.length}</Tag>
              </div>
              <div>
                <Text strong>API状态: </Text>
                {healthStatus ? (
                  <Tag color="success">正常</Tag>
                ) : (
                  <Tag color="error">异常</Tag>
                )}
              </div>
            </Space>
          </Card>
        </Col>

        {datasets.length > 0 && (
          <Col span={24}>
            <Card title="📋 数据集详情" size="small">
              <Table
                dataSource={datasets}
                columns={[
                  { title: 'ID', dataIndex: 'id', key: 'id', width: 60 },
                  { title: '名称', dataIndex: 'display_name', key: 'display_name' },
                  { title: '描述', dataIndex: 'description', key: 'description' },
                  { title: '学生数', dataIndex: 'student_num', key: 'student_num', width: 80 },
                  { title: '题目数', dataIndex: 'exercise_num', key: 'exercise_num', width: 80 },
                  { title: '知识点数', dataIndex: 'knowledge_num', key: 'knowledge_num', width: 90 },
                  { 
                    title: '状态', 
                    dataIndex: 'is_active', 
                    key: 'is_active', 
                    width: 80,
                    render: (active: boolean) => (
                      <Tag color={active ? 'success' : 'default'}>
                        {active ? '活跃' : '非活跃'}
                      </Tag>
                    )
                  }
                ]}
                pagination={false}
                size="small"
                rowKey="id"
              />
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default ApiTestNew;
