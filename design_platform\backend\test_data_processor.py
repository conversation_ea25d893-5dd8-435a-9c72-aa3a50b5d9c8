#!/usr/bin/env python3
"""
测试新的数据处理器
"""
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

# 创建简化的设置类
class MockSettings:
    DATASETS_DIR = "data/datasets"

# 模拟设置
import app.core.config
app.core.config.settings = MockSettings()

from app.services.data_processor import DatasetRegistry, DataProcessor, DatasetIntegrator
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_dataset_registry():
    """测试数据集注册表"""
    print("=== 测试数据集注册表 ===")
    
    registry = DatasetRegistry()
    
    # 发现数据集
    datasets = registry.discover_datasets()
    print(f"发现 {len(datasets)} 个数据集:")
    
    for name, adapter in datasets.items():
        print(f"  - {name}: {adapter.metadata.display_name}")
        print(f"    类型: {adapter.metadata.dataset_type.value}")
        print(f"    学生数: {adapter.metadata.student_num}")
        print(f"    题目数: {adapter.metadata.exercise_num}")
        print(f"    知识点数: {adapter.metadata.knowledge_num}")
        print(f"    有Q矩阵: {adapter.metadata.has_q_matrix}")
        print(f"    有嵌入: {adapter.metadata.has_embeddings}")
        print()

def test_data_processor():
    """测试数据处理器"""
    print("=== 测试数据处理器 ===")
    
    try:
        # 测试Assist0910数据集
        processor = DataProcessor(dataset_name='Assist0910')
        print(f"成功加载数据集: {processor.metadata.display_name}")
        
        # 获取统计信息
        stats = processor.get_dataset_statistics()
        print(f"基本信息: {stats['basic_info']}")
        print(f"数据质量: {stats['data_quality']}")
        
        # 准备训练数据
        training_data = processor.prepare_for_training(test_size=0.2, seed=42)
        print(f"训练数据准备完成:")
        print(f"  训练集大小: {training_data['dataset_info']['train_size']}")
        print(f"  验证集大小: {training_data['dataset_info']['val_size']}")
        print(f"  测试集大小: {training_data['dataset_info']['test_size']}")
        
    except Exception as e:
        print(f"测试失败: {e}")

def test_dataset_integrator():
    """测试数据集集成器"""
    print("=== 测试数据集集成器 ===")
    
    integrator = DatasetIntegrator()
    
    # 集成所有数据集
    integrated = integrator.integrate_all_datasets()
    
    print(f"可用数据集: {len(integrated['available_datasets'])}")
    print(f"训练就绪: {integrated['training_ready']}")
    print(f"可视化就绪: {integrated['visualization_ready']}")
    
    # 测试获取训练数据
    if integrated['training_ready']:
        dataset_name = integrated['training_ready'][0]
        print(f"\n测试获取训练数据: {dataset_name}")
        
        try:
            training_data = integrator.get_dataset_for_training(dataset_name)
            print(f"训练数据获取成功")
            print(f"元数据: {training_data['metadata']}")
        except Exception as e:
            print(f"获取训练数据失败: {e}")

if __name__ == "__main__":
    print("开始测试新的数据处理器...")
    
    test_dataset_registry()
    test_data_processor()
    test_dataset_integrator()
    
    print("测试完成!")
