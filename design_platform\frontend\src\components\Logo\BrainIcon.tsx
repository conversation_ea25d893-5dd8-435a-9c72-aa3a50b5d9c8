import React from 'react';

interface BrainIconProps {
  size?: number;
  color?: string;
}

export const BrainIcon: React.FC<BrainIconProps> = ({
  size = 32,
  color = '#1890ff'
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 120 120"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        {/* 主渐变 */}
        <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#1890ff" />
          <stop offset="50%" stopColor="#40a9ff" />
          <stop offset="100%" stopColor="#69c0ff" />
        </linearGradient>

        {/* 次要渐变 */}
        <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#722ed1" />
          <stop offset="50%" stopColor="#9254de" />
          <stop offset="100%" stopColor="#b37feb" />
        </linearGradient>

        {/* 发光效果 */}
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        {/* 阴影效果 */}
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="2" dy="4" stdDeviation="3" floodColor="#000" floodOpacity="0.2"/>
        </filter>
      </defs>

      {/* 外圈装饰环 */}
      <circle
        cx="60"
        cy="60"
        r="55"
        fill="none"
        stroke="url(#primaryGradient)"
        strokeWidth="2"
        opacity="0.2"
        strokeDasharray="8,4"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          from="0 60 60"
          to="360 60 60"
          dur="30s"
          repeatCount="indefinite"
        />
      </circle>

      {/* 背景圆形 */}
      <circle
        cx="60"
        cy="60"
        r="45"
        fill="url(#primaryGradient)"
        opacity="0.1"
        filter="url(#shadow)"
      />

      {/* 大脑主体 - 左半球 */}
      <path
        d="M30 45C30 35 35 25 45 20C50 18 55 20 58 25C58 30 55 35 50 40C45 45 40 50 35 55C32 60 30 65 30 70C30 75 35 80 40 82C45 84 50 82 55 80C58 78 60 75 60 70L60 45C60 40 58 35 55 32C52 28 48 26 45 28C40 30 35 35 32 40C30 42 30 43.5 30 45Z"
        fill="url(#primaryGradient)"
        filter="url(#glow)"
        opacity="0.9"
      />

      {/* 大脑主体 - 右半球 */}
      <path
        d="M90 45C90 35 85 25 75 20C70 18 65 20 62 25C62 30 65 35 70 40C75 45 80 50 85 55C88 60 90 65 90 70C90 75 85 80 80 82C75 84 70 82 65 80C62 78 60 75 60 70L60 45C60 40 62 35 65 32C68 28 72 26 75 28C80 30 85 35 88 40C90 42 90 43.5 90 45Z"
        fill="url(#secondaryGradient)"
        filter="url(#glow)"
        opacity="0.9"
      />

      {/* 中央分割线 */}
      <line
        x1="60"
        y1="25"
        x2="60"
        y2="85"
        stroke="#fff"
        strokeWidth="2"
        opacity="0.3"
      />

      {/* 神经网络节点 */}
      <circle cx="45" cy="35" r="3" fill="#fff" opacity="0.8">
        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="75" cy="35" r="3" fill="#fff" opacity="0.8">
        <animate attributeName="opacity" values="1;0.8;1" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="40" cy="55" r="2.5" fill="#fff" opacity="0.7">
        <animate attributeName="opacity" values="0.7;1;0.7" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="80" cy="55" r="2.5" fill="#fff" opacity="0.7">
        <animate attributeName="opacity" values="1;0.7;1" dur="2.5s" repeatCount="indefinite"/>
      </circle>
      <circle cx="60" cy="45" r="2" fill="#fff" opacity="0.9">
        <animate attributeName="opacity" values="0.9;1;0.9" dur="1.5s" repeatCount="indefinite"/>
      </circle>

      {/* 神经连接线 */}
      <path
        d="M45 35L60 45M75 35L60 45M40 55L60 45M80 55L60 45"
        stroke="#fff"
        strokeWidth="1.5"
        opacity="0.4"
        strokeDasharray="3,2"
      >
        <animate attributeName="stroke-dashoffset" values="0;10;0" dur="3s" repeatCount="indefinite"/>
      </path>

      {/* 智能光环 */}
      <circle
        cx="60"
        cy="60"
        r="38"
        fill="none"
        stroke="url(#primaryGradient)"
        strokeWidth="1.5"
        opacity="0.4"
        strokeDasharray="6,3"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          from="0 60 60"
          to="-360 60 60"
          dur="25s"
          repeatCount="indefinite"
        />
      </circle>

      {/* 数据流粒子 */}
      <circle cx="30" cy="30" r="1.5" fill="url(#primaryGradient)" opacity="0.8">
        <animateMotion dur="4s" repeatCount="indefinite">
          <path d="M0,0 Q30,20 60,0 Q90,-20 120,0"/>
        </animateMotion>
      </circle>
      <circle cx="90" cy="90" r="1.5" fill="url(#secondaryGradient)" opacity="0.8">
        <animateMotion dur="5s" repeatCount="indefinite">
          <path d="M0,0 Q-30,-20 -60,0 Q-90,20 -120,0"/>
        </animateMotion>
      </circle>
    </svg>
  );
};
