import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Typography, Tag, Statistic, message, Spin } from 'antd';
import { DatabaseOutlined, UserOutlined, FileTextOutlined, InteractionOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import './index.css';

const { Title, Paragraph } = Typography;

interface Dataset {
  id: number;
  name: string;
  displayName: string;
  description: string;
  studentCount: number;
  itemCount: number;
  interactionCount: number;
  isMainDataset: boolean;
  status: 'available' | 'demo' | 'processing';
  features: string[];
}

const DatasetSelection: React.FC = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDataset, setSelectedDataset] = useState<number | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // 模拟加载数据集信息
    setTimeout(() => {
      setDatasets([
        {
          id: 1,
          name: 'Assist0910',
          displayName: 'ASSISTments 2009-2010',
          description: '这是我们的主要演示数据集，将展示完整的数据处理和模型训练流程',
          studentCount: 4163,
          itemCount: 17751,
          interactionCount: 325637,
          isMainDataset: true,
          status: 'available',
          features: ['完整流程', '数据清洗', '实时训练', 'ORCDF模型']
        },
        {
          id: 2,
          name: 'Assist17',
          displayName: 'ASSISTments 2017',
          description: '预训练结果展示，包含多种认知诊断模型的对比分析',
          studentCount: 1709,
          itemCount: 3162,
          interactionCount: 942816,
          isMainDataset: false,
          status: 'demo',
          features: ['预训练结果', '模型对比', '可视化展示']
        },
        {
          id: 3,
          name: 'Junyi',
          displayName: 'Junyi Academy',
          description: '数学学习数据集，展示不同学科领域的认知诊断效果',
          studentCount: 10000,
          itemCount: 835,
          interactionCount: 25925,
          isMainDataset: false,
          status: 'demo',
          features: ['数学领域', '预训练结果', '学科特化']
        },
        {
          id: 4,
          name: 'NeurIPS2020',
          displayName: 'NeurIPS 2020 Challenge',
          description: '教育挑战赛数据集，展示竞赛级别的模型性能',
          studentCount: 948,
          itemCount: 948,
          interactionCount: 1382727,
          isMainDataset: false,
          status: 'demo',
          features: ['竞赛数据', '高性能模型', '基准测试']
        }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const handleDatasetSelect = (datasetId: number) => {
    setSelectedDataset(datasetId);
  };

  const handleProceed = () => {
    if (!selectedDataset) {
      message.warning('请先选择一个数据集');
      return;
    }

    const dataset = datasets.find(d => d.id === selectedDataset);
    if (!dataset) return;

    // 保存选择的数据集到本地存储
    localStorage.setItem('selectedDataset', JSON.stringify(dataset));

    if (dataset.isMainDataset) {
      // 主数据集进入完整流程
      message.success('进入完整的数据处理和训练流程');
      navigate('/data-preprocessing');
    } else {
      // 演示数据集直接查看结果
      message.success('查看预训练结果和可视化展示');
      navigate('/demo-results');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'green';
      case 'demo': return 'blue';
      case 'processing': return 'orange';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return '可用于训练';
      case 'demo': return '演示数据';
      case 'processing': return '处理中';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="dataset-selection-loading">
        <Spin size="large" />
        <p>正在加载数据集信息...</p>
      </div>
    );
  }

  return (
    <div className="dataset-selection-container">
      <div className="dataset-selection-header">
        <Title level={2}>
          📊 选择数据集
        </Title>
        <Paragraph>
          请选择一个数据集开始您的认知诊断之旅。主数据集将展示完整的处理流程，演示数据集将直接展示预训练结果。
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        {datasets.map((dataset) => (
          <Col xs={24} lg={12} key={dataset.id}>
            <Card
              className={`dataset-card ${selectedDataset === dataset.id ? 'selected' : ''} ${dataset.isMainDataset ? 'main-dataset' : ''}`}
              hoverable
              onClick={() => handleDatasetSelect(dataset.id)}
              actions={[
                <div key="stats" className="dataset-stats">
                  <Statistic
                    title="学生数"
                    value={dataset.studentCount}
                    prefix={<UserOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic
                    title="题目数"
                    value={dataset.itemCount}
                    prefix={<FileTextOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                  <Statistic
                    title="交互数"
                    value={dataset.interactionCount}
                    prefix={<InteractionOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </div>
              ]}
            >
              <div className="dataset-card-header">
                <div className="dataset-icon">
                  <DatabaseOutlined />
                </div>
                <div className="dataset-title-area">
                  <Title level={4} className="dataset-title">
                    {dataset.displayName}
                    {dataset.isMainDataset && (
                      <Tag color="gold" className="main-tag">主数据集</Tag>
                    )}
                  </Title>
                  <Tag color={getStatusColor(dataset.status)}>
                    {getStatusText(dataset.status)}
                  </Tag>
                </div>
              </div>

              <Paragraph className="dataset-description">
                {dataset.description}
              </Paragraph>

              <div className="dataset-features">
                {dataset.features.map((feature, index) => (
                  <Tag key={index} color="blue" className="feature-tag">
                    {feature}
                  </Tag>
                ))}
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      <div className="dataset-selection-footer">
        <Button
          type="primary"
          size="large"
          onClick={handleProceed}
          disabled={!selectedDataset}
          className="proceed-button"
        >
          {selectedDataset && datasets.find(d => d.id === selectedDataset)?.isMainDataset
            ? '开始数据处理流程'
            : '查看演示结果'
          }
        </Button>
      </div>
    </div>
  );
};

export default DatasetSelection;
