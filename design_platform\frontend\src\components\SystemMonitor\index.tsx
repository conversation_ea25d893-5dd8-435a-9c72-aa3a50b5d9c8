import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Typography, Tag, Divider } from 'antd';
import {
  DatabaseOutlined,
  CloudServerOutlined,
  ThunderboltOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  DesktopOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

// 动态API配置 - 支持内网穿透
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // 检测当前访问域名
  const currentHost = window.location.hostname;

  // 如果是内网穿透域名，使用相对路径或提示用户配置
  if (currentHost.includes('ngrok') || currentHost.includes('cpolar') || currentHost.includes('frp')) {
    // 内网穿透环境，需要用户手动配置后端地址
    const backendUrl = localStorage.getItem('BACKEND_URL');
    if (backendUrl) {
      return `${backendUrl}/api/v1`;
    }

    // 如果没有配置，提示用户配置
    console.warn('🌐 检测到内网穿透环境，请配置后端地址');
    return 'http://localhost:8000/api/v1'; // 默认值
  }

  // 本地开发环境
  return 'http://localhost:8000/api/v1';
};

interface SystemStatus {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  gpu_usage?: number;
  active_connections: number;
  response_time: number;
  uptime: string;
  status: 'healthy' | 'warning' | 'error';
}

interface SystemMonitorProps {
  refreshInterval?: number;
}

const SystemMonitor: React.FC<SystemMonitorProps> = ({ refreshInterval = 30000 }) => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    cpu_usage: 0,
    memory_usage: 0,
    disk_usage: 0,
    gpu_usage: 0,
    active_connections: 0,
    response_time: 0,
    uptime: '0h 0m',
    status: 'healthy'
  });
  const [loading, setLoading] = useState(true);

  const fetchSystemStatus = async () => {
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/system/status`);
      if (response.ok) {
        const data = await response.json();
        setSystemStatus(data);
      } else {
        // 使用模拟数据
        setSystemStatus({
          cpu_usage: Math.random() * 80 + 10,
          memory_usage: Math.random() * 70 + 20,
          disk_usage: Math.random() * 60 + 30,
          gpu_usage: Math.random() * 90 + 5,
          active_connections: Math.floor(Math.random() * 50 + 10),
          response_time: Math.random() * 200 + 50,
          uptime: `${Math.floor(Math.random() * 24)}h ${Math.floor(Math.random() * 60)}m`,
          status: Math.random() > 0.8 ? 'warning' : 'healthy'
        });
      }
    } catch (error) {
      console.error('Failed to fetch system status:', error);
      // 使用模拟数据作为fallback
      setSystemStatus({
        cpu_usage: 45.2,
        memory_usage: 62.8,
        disk_usage: 38.5,
        gpu_usage: 78.3,
        active_connections: 23,
        response_time: 125,
        uptime: '12h 34m',
        status: 'healthy'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStatus();
    const interval = setInterval(fetchSystemStatus, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#52c41a';
      case 'warning': return '#faad14';
      case 'error': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default: return <CheckCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getProgressColor = (value: number) => {
    if (value < 50) return '#52c41a';
    if (value < 80) return '#faad14';
    return '#ff4d4f';
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span>
            <CloudServerOutlined style={{ marginRight: '8px' }} />
            系统监控
          </span>
          <Tag color={getStatusColor(systemStatus.status)} icon={getStatusIcon(systemStatus.status)}>
            {systemStatus.status === 'healthy' ? '运行正常' : 
             systemStatus.status === 'warning' ? '需要关注' : '异常'}
          </Tag>
        </div>
      }
      loading={loading}
      style={{ marginBottom: '16px' }}
    >
      <Row gutter={[16, 16]}>
        {/* CPU使用率 */}
        <Col xs={24} sm={12} md={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <DesktopOutlined style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
            <div>
              <Text strong>CPU使用率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress
                  type="circle"
                  size={80}
                  percent={Math.round(systemStatus.cpu_usage)}
                  strokeColor={getProgressColor(systemStatus.cpu_usage)}
                  format={(percent) => `${percent}%`}
                />
              </div>
            </div>
          </Card>
        </Col>

        {/* 内存使用率 */}
        <Col xs={24} sm={12} md={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <DatabaseOutlined style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
            <div>
              <Text strong>内存使用率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress
                  type="circle"
                  size={80}
                  percent={Math.round(systemStatus.memory_usage)}
                  strokeColor={getProgressColor(systemStatus.memory_usage)}
                  format={(percent) => `${percent}%`}
                />
              </div>
            </div>
          </Card>
        </Col>

        {/* GPU使用率 */}
        <Col xs={24} sm={12} md={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <ThunderboltOutlined style={{ fontSize: '24px', color: '#faad14', marginBottom: '8px' }} />
            <div>
              <Text strong>GPU使用率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress
                  type="circle"
                  size={80}
                  percent={Math.round(systemStatus.gpu_usage || 0)}
                  strokeColor={getProgressColor(systemStatus.gpu_usage || 0)}
                  format={(percent) => `${percent}%`}
                />
              </div>
            </div>
          </Card>
        </Col>

        {/* 磁盘使用率 */}
        <Col xs={24} sm={12} md={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <DatabaseOutlined style={{ fontSize: '24px', color: '#722ed1', marginBottom: '8px' }} />
            <div>
              <Text strong>磁盘使用率</Text>
              <div style={{ marginTop: '8px' }}>
                <Progress
                  type="circle"
                  size={80}
                  percent={Math.round(systemStatus.disk_usage)}
                  strokeColor={getProgressColor(systemStatus.disk_usage)}
                  format={(percent) => `${percent}%`}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Divider />

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={8}>
          <Statistic
            title="活跃连接数"
            value={systemStatus.active_connections}
            suffix="个"
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col xs={24} sm={8}>
          <Statistic
            title="平均响应时间"
            value={systemStatus.response_time}
            suffix="ms"
            precision={1}
            valueStyle={{ color: systemStatus.response_time > 500 ? '#ff4d4f' : '#52c41a' }}
          />
        </Col>
        <Col xs={24} sm={8}>
          <Statistic
            title="系统运行时间"
            value={systemStatus.uptime}
            valueStyle={{ color: '#722ed1' }}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default SystemMonitor;
