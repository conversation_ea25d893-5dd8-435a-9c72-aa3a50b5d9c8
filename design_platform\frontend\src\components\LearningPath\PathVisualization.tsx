import React from 'react';
import { Card, Typography, Steps, Tag, Space, Progress } from 'antd';
import type { StepProps } from 'antd';
import {
  ClockCircleOutlined,
  TrophyOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';

interface KnowledgePoint {
  knowledge_point_id: string;
  knowledge_point_name: string;
  confidence_score: number;
  difficulty_level: string;
  estimated_duration: number;
  category: string;
  prerequisites_met: boolean;
  sequence_order: number;
}

interface PathVisualizationProps {
  learningPath: KnowledgePoint[];
  width?: number;
  height?: number;
}

const PathVisualization: React.FC<PathVisualizationProps> = ({
  learningPath,
  width = 800,
  height = 400
}) => {
  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'green';
      case 'medium': return 'orange';
      case 'hard': return 'red';
      default: return 'blue';
    }
  };

  // 获取置信度颜色
  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return '#52c41a';
    if (score >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  if (!learningPath || learningPath.length === 0) {
    return (
      <Card title="学习路径可视化">
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Typography.Text type="secondary">暂无学习路径数据</Typography.Text>
        </div>
      </Card>
    );
  }

  // 创建步骤数据
  const steps: StepProps[] = learningPath.map((item, index) => ({
    title: item.knowledge_point_name,
    description: (
      <Space direction="vertical" size={4}>
        <Space size={8}>
          <Tag color={getDifficultyColor(item.difficulty_level)}>
            {item.difficulty_level}
          </Tag>
          <Tag color="blue">{item.category}</Tag>
        </Space>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ClockCircleOutlined style={{ color: '#666' }} />
          <Typography.Text type="secondary" style={{ fontSize: 12 }}>
            {item.estimated_duration}分钟
          </Typography.Text>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Typography.Text type="secondary" style={{ fontSize: 12 }}>
            置信度:
          </Typography.Text>
          <Progress
            percent={Math.round(item.confidence_score * 100)}
            size="small"
            strokeColor={getConfidenceColor(item.confidence_score)}
            showInfo={false}
            style={{ width: 60 }}
          />
          <Typography.Text
            style={{
              fontSize: 12,
              color: getConfidenceColor(item.confidence_score)
            }}
          >
            {Math.round(item.confidence_score * 100)}%
          </Typography.Text>
        </div>
      </Space>
    ),
    status: (index === 0 ? 'process' : 'wait') as 'process' | 'wait' | 'finish' | 'error',
    icon: (
      <div
        style={{
          width: 32,
          height: 32,
          borderRadius: '50%',
          backgroundColor: getConfidenceColor(item.confidence_score),
          color: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: 14,
          fontWeight: 'bold'
        }}
      >
        {item.sequence_order}
      </div>
    )
  }));

  return (
    <Card
      title={
        <Space>
          <TrophyOutlined />
          <span>学习路径可视化</span>
        </Space>
      }
      extra={
        <Space>
          <Typography.Text type="secondary">
            共{learningPath.length}个知识点
          </Typography.Text>
        </Space>
      }
    >
      <div style={{ padding: '20px 0' }}>
        <Steps
          direction="vertical"
          current={0}
          items={steps}
          style={{ maxHeight: height - 100, overflowY: 'auto' }}
        />
      </div>

      {/* 图例 */}
      <div style={{
        marginTop: 20,
        padding: 16,
        backgroundColor: '#fafafa',
        borderRadius: 6
      }}>
        <Typography.Title level={5} style={{ margin: 0, marginBottom: 12 }}>
          图例说明
        </Typography.Title>
        <Space wrap>
          <Space>
            <Typography.Text strong>难度等级:</Typography.Text>
            <Tag color="green">简单</Tag>
            <Tag color="orange">中等</Tag>
            <Tag color="red">困难</Tag>
          </Space>
          <Space>
            <Typography.Text strong>置信度:</Typography.Text>
            <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
              <div style={{
                width: 12,
                height: 12,
                backgroundColor: '#ff4d4f',
                borderRadius: 2
              }} />
              <Typography.Text style={{ fontSize: 12 }}>低</Typography.Text>
              <ArrowRightOutlined style={{ fontSize: 10, margin: '0 4px' }} />
              <div style={{
                width: 12,
                height: 12,
                backgroundColor: '#52c41a',
                borderRadius: 2
              }} />
              <Typography.Text style={{ fontSize: 12 }}>高</Typography.Text>
            </div>
          </Space>
        </Space>
      </div>
    </Card>
  );
};

export default PathVisualization;
