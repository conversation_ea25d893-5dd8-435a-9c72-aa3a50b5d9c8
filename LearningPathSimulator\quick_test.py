#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本

验证学习路径规划模拟器的核心功能是否正常工作。
"""

import sys
import os
import traceback
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心模块
        from core.base import BaseAgent, BaseEnvironment
        from core.mdp import MarkovDecisionProcess
        from core.cognitive_models import EbbinghausForgettingCurve
        
        # 测试环境模块
        from environments.learning_env import LearningEnvironment
        
        # 测试智能体模块
        from agents.rule_based import RandomAgent, GreedyAgent, SmartGreedyAgent
        
        # 测试评估模块
        from evaluation.evaluator import Evaluator
        from evaluation.metrics import MetricCalculator
        from evaluation.statistics import StatisticalAnalyzer
        
        print("✅ 所有模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False


def test_basic_functionality():
    """测试基础功能"""
    print("\n🧪 测试基础功能...")
    
    try:
        from environments.learning_env import LearningEnvironment
        from agents.rule_based import GreedyAgent
        from evaluation.evaluator import Evaluator
        
        # 1. 创建环境
        env = LearningEnvironment(
            num_knowledge_points=3,
            max_steps=10
        )
        print(f"✅ 环境创建成功: {env.num_kps}个知识点")
        
        # 2. 创建智能体
        agent = GreedyAgent(env.action_space_size)
        print(f"✅ 智能体创建成功: {agent.name}")
        
        # 3. 运行单个回合
        observation = env.reset()
        agent.reset()
        
        total_reward = 0
        for step in range(5):
            action = agent.get_action(observation)
            next_observation, reward, done, info = env.step(action)
            agent.update(observation, action, reward, next_observation, done)
            
            total_reward += reward
            observation = next_observation
            
            if done:
                break
        
        print(f"✅ 单回合运行成功: 总奖励 {total_reward:.3f}")
        
        # 4. 评估器测试
        evaluator = Evaluator()
        result = evaluator.evaluate(agent, env, num_episodes=5)
        
        print(f"✅ 评估器运行成功: 平均奖励 {result['metrics']['AverageReward']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        traceback.print_exc()
        return False


def test_cognitive_models():
    """测试认知模型"""
    print("\n🧠 测试认知模型...")
    
    try:
        from core.cognitive_models import (
            EbbinghausForgettingCurve, VygotskyZPD, SwellerCognitiveLoad
        )
        import numpy as np
        
        # 1. 遗忘曲线模型
        forgetting_model = EbbinghausForgettingCurve(tau=24.0)
        retention = forgetting_model.apply(None, None, time_elapsed=12.0)
        print(f"✅ 遗忘曲线模型: 12小时后保持率 {retention:.3f}")
        
        # 2. ZPD模型
        zpd_model = VygotskyZPD(optimal_challenge=0.2)
        state = np.array([0.5, 0.3, 0.7])
        zpd_effect = zpd_model.apply(state, 0, task_difficulty=0.6)
        print(f"✅ ZPD模型: ZPD效应 {zpd_effect:.3f}")
        
        # 3. 认知负荷模型
        cognitive_load_model = SwellerCognitiveLoad()
        load_effect = cognitive_load_model.apply(state, 0, task_complexity=1.0)
        print(f"✅ 认知负荷模型: 负荷效应 {load_effect:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 认知模型测试失败: {e}")
        traceback.print_exc()
        return False


def test_statistical_analysis():
    """测试统计分析"""
    print("\n📊 测试统计分析...")
    
    try:
        from evaluation.statistics import WelchTTest, CohenDEffect, StatisticalAnalyzer
        import numpy as np
        
        # 生成测试数据
        group1 = np.random.normal(0.6, 0.1, 20).tolist()
        group2 = np.random.normal(0.5, 0.1, 20).tolist()
        
        # 1. t检验
        t_test = WelchTTest()
        t_result = t_test.test(group1, group2)
        print(f"✅ t检验: p值 {t_result['p_value']:.4f}")
        
        # 2. 效应量
        effect_test = CohenDEffect()
        effect_result = effect_test.test(group1, group2)
        print(f"✅ Cohen's d: {effect_result['cohens_d']:.3f} ({effect_result['effect_size']})")
        
        # 3. 统计分析器
        analyzer = StatisticalAnalyzer()
        comparison = analyzer.compare_two_groups(group1, group2, "算法A", "算法B")
        print(f"✅ 统计分析器运行成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 统计分析测试失败: {e}")
        traceback.print_exc()
        return False


def test_agent_comparison():
    """测试智能体比较"""
    print("\n🏁 测试智能体比较...")
    
    try:
        from environments.learning_env import LearningEnvironment
        from agents.rule_based import RandomAgent, GreedyAgent, SmartGreedyAgent
        from evaluation.evaluator import Evaluator
        
        # 创建环境
        env = LearningEnvironment(num_knowledge_points=3, max_steps=15)
        
        # 创建多个智能体
        agents = [
            RandomAgent(env.action_space_size),
            GreedyAgent(env.action_space_size),
            SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
        ]
        
        # 比较智能体
        evaluator = Evaluator()
        result = evaluator.compare_agents(agents, env, num_episodes=10, save_results=False)
        
        print(f"✅ 智能体比较成功:")
        for agent_info in result['ranking']:
            name = agent_info['agent_name']
            score = agent_info['composite_score']
            print(f"  {agent_info['rank']}. {name}: {score:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能体比较测试失败: {e}")
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 学习路径规划模拟器快速测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("基础功能", test_basic_functionality),
        ("认知模型", test_cognitive_models),
        ("统计分析", test_statistical_analysis),
        ("智能体比较", test_agent_comparison)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results[test_name] = success
            if success:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results[test_name] = False
    
    # 总结
    print(f"\n" + "=" * 60)
    print(f"📋 测试总结:")
    print(f"  通过: {passed}/{total}")
    print(f"  成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print(f"🎉 所有测试通过！模拟器运行正常。")
        return True
    else:
        print(f"⚠️  部分测试失败，请检查相关模块。")
        for test_name, success in results.items():
            status = "✅" if success else "❌"
            print(f"  {status} {test_name}")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
