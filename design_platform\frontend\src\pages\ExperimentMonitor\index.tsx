import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Progress,
  Button,
  Space,
  Modal,
  Descriptions,
  Alert,
  Row,
  Col,
  Statistic,
  message,
  Spin,
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  BulbOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import ReactECharts from 'echarts-for-react';
import { experimentService } from '../../services/api';
import CognitiveDiagnosisModal from '../../components/CognitiveDiagnosisModal';

interface Experiment {
  id: number;
  name: string;
  description: string;
  dataset_type: string;
  model_type: string;
  status: string;
  progress: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  metrics?: Record<string, any>;
  results?: Record<string, any>;
}

interface ExperimentProgress {
  experiment_id: number;
  status: string;
  progress: number;
  current_epoch?: number;
  total_epochs?: number;
  current_loss?: number;
  best_metric?: number;
  estimated_time_remaining?: number;
  last_updated: string;
}

const ExperimentMonitor: React.FC = () => {
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [progressData, setProgressData] = useState<ExperimentProgress | null>(null);
  const [diagnosisModalVisible, setDiagnosisModalVisible] = useState(false);
  const [diagnosisLoading, setDiagnosisLoading] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadExperiments();
    
    // 设置自动刷新
    const interval = setInterval(() => {
      loadExperiments();
    }, 5000);
    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  const loadExperiments = async () => {
    setLoading(true);
    try {
      const response = await experimentService.getExperiments();
      setExperiments(response.data);
    } catch (error) {
      message.error('加载实验列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleStartExperiment = async (id: number) => {
    try {
      await experimentService.startExperiment(id);
      message.success('实验启动成功');
      loadExperiments();
    } catch (error) {
      message.error('启动实验失败');
    }
  };

  const handleStopExperiment = async (id: number) => {
    try {
      await experimentService.stopExperiment(id);
      message.success('实验停止成功');
      loadExperiments();
    } catch (error) {
      message.error('停止实验失败');
    }
  };

  const handleDeleteExperiment = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个实验吗？此操作不可恢复。',
      onOk: async () => {
        try {
          await experimentService.deleteExperiment(id);
          message.success('实验删除成功');
          loadExperiments();
        } catch (error) {
          message.error('删除实验失败');
        }
      },
    });
  };

  const handleViewDetail = async (experiment: Experiment) => {
    setSelectedExperiment(experiment);
    setDetailModalVisible(true);
    
    if (experiment.status === 'running') {
      try {
        const response = await experimentService.getExperimentProgress(experiment.id);
        setProgressData(response.data);
      } catch (error) {
        console.error('获取进度信息失败:', error);
      }
    }
  };

  const handleCognitiveDiagnosis = (experiment: Experiment) => {
    setSelectedExperiment(experiment);
    setDiagnosisModalVisible(true);
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'warning', text: '已取消' },
    };
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getProgressChart = (experiment: Experiment) => {
    // 检查多种可能的数据结构
    let history = null;

    if (experiment.metrics?.training_history) {
      history = experiment.metrics.training_history;
    } else if (experiment.results?.training_history) {
      history = experiment.results.training_history;
    } else if (experiment.results?.metrics?.training_history) {
      history = experiment.results.metrics.training_history;
    }

    if (!history || (!history.loss && !history.auc)) {
      return (
        <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
          暂无训练历史数据
        </div>
      );
    }

    // 处理对象格式的数据
    const lossData = history.loss || {};
    const aucData = history.auc || {};

    // 获取所有epoch并排序
    const allEpochs = new Set([
      ...Object.keys(lossData),
      ...Object.keys(aucData)
    ]);
    const epochs = Array.from(allEpochs).map(Number).sort((a, b) => a - b);

    const losses = epochs.map(epoch => lossData[epoch.toString()]).filter(v => v !== undefined);
    const aucs = epochs.map(epoch => aucData[epoch.toString()]).filter(v => v !== undefined);

    const option = {
      title: {
        text: '训练进度',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['Loss', 'AUC'],
        bottom: 0,
      },
      xAxis: {
        type: 'category',
        data: epochs.map(e => `Epoch ${e}`),
        name: 'Epoch',
      },
      yAxis: [
        {
          type: 'value',
          name: 'Loss',
          position: 'left',
        },
        {
          type: 'value',
          name: 'AUC',
          position: 'right',
          min: 0,
          max: 1,
        },
      ],
      series: [
        {
          name: 'Loss',
          type: 'line',
          data: losses,
          yAxisIndex: 0,
          smooth: true,
        },
        {
          name: 'AUC',
          type: 'line',
          data: aucs,
          yAxisIndex: 1,
          smooth: true,
        },
      ],
    };

    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  const columns: ColumnsType<Experiment> = [
    {
      title: '实验名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Experiment) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.model_type.toUpperCase()} on {record.dataset_type}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: Experiment) => (
        <div>
          <Progress percent={Math.round(progress)} size="small" />
          {record.status === 'running' && progressData?.current_epoch && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              Epoch {progressData.current_epoch}/{progressData.total_epochs}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Experiment) => (
        <Space>
          {record.status === 'pending' && (
            <Button
              type="link"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartExperiment(record.id)}
            >
              启动
            </Button>
          )}
          {record.status === 'running' && (
            <Button
              type="link"
              icon={<PauseCircleOutlined />}
              onClick={() => handleStopExperiment(record.id)}
            >
              停止
            </Button>
          )}
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.status === 'completed' && (
            <Button
              type="link"
              icon={<BulbOutlined />}
              onClick={() => handleCognitiveDiagnosis(record)}
            >
              认知诊断
            </Button>
          )}
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteExperiment(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="实验监控"
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={loadExperiments}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={experiments}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个实验`,
          }}
        />
      </Card>

      <Modal
        title={`实验详情 - ${selectedExperiment?.name}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={1000}
      >
        {selectedExperiment && (
          <div>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Statistic
                  title="状态"
                  value={selectedExperiment.status}
                  valueRender={() => getStatusTag(selectedExperiment.status)}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="进度"
                  value={selectedExperiment.progress}
                  suffix="%"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="模型类型"
                  value={selectedExperiment.model_type.toUpperCase()}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="数据集"
                  value={selectedExperiment.dataset_type}
                />
              </Col>
            </Row>

            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="实验ID">
                {selectedExperiment.id}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedExperiment.created_at).toLocaleString()}
              </Descriptions.Item>
              {selectedExperiment.started_at && (
                <Descriptions.Item label="开始时间">
                  {new Date(selectedExperiment.started_at).toLocaleString()}
                </Descriptions.Item>
              )}
              {selectedExperiment.completed_at && (
                <Descriptions.Item label="完成时间">
                  {new Date(selectedExperiment.completed_at).toLocaleString()}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="描述" span={2}>
                {selectedExperiment.description || '无描述'}
              </Descriptions.Item>
            </Descriptions>

            {selectedExperiment.error_message && (
              <Alert
                message="错误信息"
                description={selectedExperiment.error_message}
                type="error"
                showIcon
                style={{ margin: '16px 0' }}
              />
            )}

            {progressData && selectedExperiment.status === 'running' && (
              <Card title="实时进度" style={{ marginTop: 16 }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="当前轮次"
                      value={`${progressData.current_epoch}/${progressData.total_epochs}`}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="当前损失"
                      value={progressData.current_loss?.toFixed(4) || 'N/A'}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="最佳指标"
                      value={progressData.best_metric?.toFixed(4) || 'N/A'}
                    />
                  </Col>
                </Row>
              </Card>
            )}

            {/* 训练曲线 - 只要有训练数据就显示 */}
            <Card title="训练曲线" style={{ marginTop: 16 }}>
              {getProgressChart(selectedExperiment)}
            </Card>
          </div>
        )}
      </Modal>

      {/* 认知诊断模态框 */}
      <Modal
        title={`认知诊断 - ${selectedExperiment?.name}`}
        open={diagnosisModalVisible}
        onCancel={() => setDiagnosisModalVisible(false)}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedExperiment && (
          <CognitiveDiagnosisModal
            experimentId={selectedExperiment.id}
            onClose={() => setDiagnosisModalVisible(false)}
          />
        )}
      </Modal>
    </div>
  );
};

export default ExperimentMonitor;
