"""
数据处理服务 - 真实数据集版本
"""
import os
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, Tuple, Optional, List, Union
from pathlib import Path
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class DatasetType(Enum):
    """数据集类型枚举"""
    ASSISTMENTS = "assistments"
    JUNYI = "junyi"
    NEURIPS = "neurips"
    CUSTOM = "custom"


@dataclass
class DatasetMetadata:
    """数据集元数据"""
    name: str
    display_name: str
    description: str
    dataset_type: DatasetType
    student_num: int
    exercise_num: int
    knowledge_num: int
    response_num: int
    has_q_matrix: bool
    has_hierarchy: bool = False
    has_embeddings: bool = False
    version: str = "1.0"
    source: str = ""
    created_at: str = ""


class DatasetAdapter(ABC):
    """数据集适配器抽象基类"""

    def __init__(self, dataset_path: Path):
        self.dataset_path = dataset_path
        self.config = self._load_config()
        self.metadata = self._create_metadata()

    @abstractmethod
    def _load_config(self) -> Dict[str, Any]:
        """加载数据集配置"""
        pass

    @abstractmethod
    def _create_metadata(self) -> DatasetMetadata:
        """创建数据集元数据"""
        pass

    @abstractmethod
    def load_response_data(self) -> pd.DataFrame:
        """加载响应数据"""
        pass

    @abstractmethod
    def load_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载Q矩阵"""
        pass

    def validate_data(self) -> bool:
        """验证数据完整性"""
        try:
            response_df = self.load_response_data()
            if response_df.empty:
                return False

            # 检查必要列
            required_columns = ['student_id', 'exercise_id', 'score']
            if not all(col in response_df.columns for col in required_columns):
                return False

            # 检查数据类型
            if not pd.api.types.is_numeric_dtype(response_df['score']):
                return False

            return True
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False


class AssistmentsAdapter(DatasetAdapter):
    """ASSISTments数据集适配器"""

    def _load_config(self) -> Dict[str, Any]:
        """加载数据集配置"""
        config_path = self.dataset_path / "config.json"
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _create_metadata(self) -> DatasetMetadata:
        """创建数据集元数据"""
        info = self.config.get('info', {})
        return DatasetMetadata(
            name=self.config.get('dataset', self.dataset_path.name),
            display_name=f"ASSISTments {self.dataset_path.name}",
            description=f"ASSISTments教育数据集 - {self.dataset_path.name}",
            dataset_type=DatasetType.ASSISTMENTS,
            student_num=info.get('student_num', 0),
            exercise_num=info.get('exercise_num', 0),
            knowledge_num=info.get('knowledge_num', 0),
            response_num=0,  # 将在加载数据时更新
            has_q_matrix=True,
            has_embeddings=(self.dataset_path / "stu_emb").exists() and (self.dataset_path / "exer_emb").exists()
        )

    def load_response_data(self) -> pd.DataFrame:
        """加载响应数据"""
        response_file = self.dataset_path / "response.csv"
        if not response_file.exists():
            raise FileNotFoundError(f"响应数据文件不存在: {response_file}")

        df = pd.read_csv(response_file, header=None)
        df.columns = ['student_id', 'exercise_id', 'score']

        # 更新元数据中的响应数量
        self.metadata.response_num = len(df)

        return df

    def load_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载Q矩阵"""
        q_matrix_file = self.dataset_path / "q_matrix.csv"
        if not q_matrix_file.exists():
            return None

        df = pd.read_csv(q_matrix_file, header=None)
        return df

    def load_embeddings(self) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """加载预训练嵌入"""
        stu_emb_dir = self.dataset_path / "stu_emb"
        exer_emb_dir = self.dataset_path / "exer_emb"

        stu_emb = None
        exer_emb = None

        if stu_emb_dir.exists():
            # 查找嵌入文件
            emb_files = list(stu_emb_dir.glob("*.npy"))
            if emb_files:
                stu_emb = np.load(emb_files[0])

        if exer_emb_dir.exists():
            emb_files = list(exer_emb_dir.glob("*.npy"))
            if emb_files:
                exer_emb = np.load(emb_files[0])

        return stu_emb, exer_emb


class JunyiAdapter(DatasetAdapter):
    """Junyi数据集适配器"""

    def _load_config(self) -> Dict[str, Any]:
        """加载数据集配置"""
        config_path = self.dataset_path / "config.json"
        if not config_path.exists():
            # 为Junyi数据集创建默认配置
            return self._create_default_config()

        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _create_default_config(self) -> Dict[str, Any]:
        """为Junyi数据集创建默认配置"""
        # 先加载数据获取基本信息
        response_df = self._load_raw_response_data()
        q_matrix_df = self._load_raw_q_matrix()

        config = {
            "dataset": "Junyi",
            "files": {
                "q_matrix": "q_matrix.csv",
                "response": "response.csv"
            },
            "info": {
                "student_num": response_df['student_id'].nunique() if response_df is not None else 0,
                "exercise_num": response_df['exercise_id'].nunique() if response_df is not None else 0,
                "knowledge_num": q_matrix_df.shape[1] if q_matrix_df is not None else 0
            }
        }

        # 保存配置文件
        config_path = self.dataset_path / "config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        return config

    def _load_raw_response_data(self) -> Optional[pd.DataFrame]:
        """加载原始响应数据"""
        response_file = self.dataset_path / "response.csv"
        if not response_file.exists():
            return None

        try:
            df = pd.read_csv(response_file, header=None)
            df.columns = ['student_id', 'exercise_id', 'score']
            return df
        except Exception:
            return None

    def _load_raw_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载原始Q矩阵"""
        q_matrix_file = self.dataset_path / "q_matrix.csv"
        if not q_matrix_file.exists():
            return None

        try:
            return pd.read_csv(q_matrix_file, header=None)
        except Exception:
            return None

    def _create_metadata(self) -> DatasetMetadata:
        """创建数据集元数据"""
        info = self.config.get('info', {})
        return DatasetMetadata(
            name="Junyi",
            display_name="Junyi Academy",
            description="均一教育平台数据集",
            dataset_type=DatasetType.JUNYI,
            student_num=info.get('student_num', 0),
            exercise_num=info.get('exercise_num', 0),
            knowledge_num=info.get('knowledge_num', 0),
            response_num=0,
            has_q_matrix=True
        )

    def load_response_data(self) -> pd.DataFrame:
        """加载响应数据"""
        df = self._load_raw_response_data()
        if df is None:
            raise FileNotFoundError(f"响应数据文件不存在: {self.dataset_path / 'response.csv'}")

        self.metadata.response_num = len(df)
        return df

    def load_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载Q矩阵"""
        return self._load_raw_q_matrix()
    
class NeurIPSAdapter(DatasetAdapter):
    """NeurIPS 2020数据集适配器"""

    def _load_config(self) -> Dict[str, Any]:
        """加载数据集配置"""
        config_path = self.dataset_path / "config.json"
        if not config_path.exists():
            return self._create_default_config()

        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        response_df = self._load_raw_response_data()
        q_matrix_df = self._load_raw_q_matrix()

        config = {
            "dataset": "NeurIPS2020",
            "files": {
                "q_matrix": "q_matrix.csv",
                "response": "response.csv",
                "hierarchy": "hierarchy.csv",
                "graph": "graph.csv"
            },
            "info": {
                "student_num": response_df['student_id'].nunique() if response_df is not None else 0,
                "exercise_num": response_df['exercise_id'].nunique() if response_df is not None else 0,
                "knowledge_num": q_matrix_df.shape[1] if q_matrix_df is not None else 0
            }
        }

        config_path = self.dataset_path / "config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        return config

    def _load_raw_response_data(self) -> Optional[pd.DataFrame]:
        """加载原始响应数据"""
        response_file = self.dataset_path / "response.csv"
        if not response_file.exists():
            return None

        try:
            df = pd.read_csv(response_file, header=None)
            df.columns = ['student_id', 'exercise_id', 'score']
            return df
        except Exception:
            return None

    def _load_raw_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载原始Q矩阵"""
        q_matrix_file = self.dataset_path / "q_matrix.csv"
        if not q_matrix_file.exists():
            return None

        try:
            return pd.read_csv(q_matrix_file, header=None)
        except Exception:
            return None

    def _create_metadata(self) -> DatasetMetadata:
        """创建数据集元数据"""
        info = self.config.get('info', {})
        return DatasetMetadata(
            name="NeurIPS2020",
            display_name="NeurIPS 2020 Education Challenge",
            description="NeurIPS 2020教育挑战赛数据集，包含层次结构信息",
            dataset_type=DatasetType.NEURIPS,
            student_num=info.get('student_num', 0),
            exercise_num=info.get('exercise_num', 0),
            knowledge_num=info.get('knowledge_num', 0),
            response_num=0,
            has_q_matrix=True,
            has_hierarchy=True
        )

    def load_response_data(self) -> pd.DataFrame:
        """加载响应数据"""
        df = self._load_raw_response_data()
        if df is None:
            raise FileNotFoundError(f"响应数据文件不存在: {self.dataset_path / 'response.csv'}")

        self.metadata.response_num = len(df)
        return df

    def load_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载Q矩阵"""
        return self._load_raw_q_matrix()

    def load_hierarchy(self) -> Optional[pd.DataFrame]:
        """加载层次结构数据"""
        hierarchy_file = self.dataset_path / "hierarchy.csv"
        if not hierarchy_file.exists():
            return None

        try:
            return pd.read_csv(hierarchy_file)
        except Exception:
            return None

    def load_graph(self) -> Optional[pd.DataFrame]:
        """加载图结构数据"""
        graph_file = self.dataset_path / "graph.csv"
        if not graph_file.exists():
            return None

        try:
            return pd.read_csv(graph_file)
        except Exception:
            return None


class DatasetRegistry:
    """数据集注册表"""

    def __init__(self):
        self.adapters = {
            DatasetType.ASSISTMENTS: AssistmentsAdapter,
            DatasetType.JUNYI: JunyiAdapter,
            DatasetType.NEURIPS: NeurIPSAdapter
        }
        self.datasets_dir = Path(settings.DATASETS_DIR)
        self._discovered_datasets = {}

    def discover_datasets(self) -> Dict[str, DatasetAdapter]:
        """自动发现数据集"""
        discovered = {}

        if not self.datasets_dir.exists():
            logger.warning(f"数据集目录不存在: {self.datasets_dir}")
            return discovered

        for dataset_dir in self.datasets_dir.iterdir():
            if not dataset_dir.is_dir():
                continue

            try:
                adapter = self._create_adapter(dataset_dir)
                if adapter and adapter.validate_data():
                    discovered[dataset_dir.name] = adapter
                    logger.info(f"发现数据集: {dataset_dir.name}")
                else:
                    logger.warning(f"数据集验证失败: {dataset_dir.name}")
            except Exception as e:
                logger.error(f"处理数据集 {dataset_dir.name} 失败: {e}")

        self._discovered_datasets = discovered
        return discovered

    def _create_adapter(self, dataset_dir: Path) -> Optional[DatasetAdapter]:
        """为数据集创建适配器"""
        dataset_name = dataset_dir.name.lower()

        # 根据数据集名称推断类型
        if 'assist' in dataset_name:
            adapter_class = self.adapters[DatasetType.ASSISTMENTS]
        elif 'junyi' in dataset_name:
            adapter_class = self.adapters[DatasetType.JUNYI]
        elif 'neurips' in dataset_name:
            adapter_class = self.adapters[DatasetType.NEURIPS]
        else:
            # 尝试通用适配器
            adapter_class = self.adapters[DatasetType.ASSISTMENTS]

        try:
            return adapter_class(dataset_dir)
        except Exception as e:
            logger.error(f"创建适配器失败: {e}")
            return None

    def get_dataset(self, dataset_name: str) -> Optional[DatasetAdapter]:
        """获取数据集适配器"""
        if not self._discovered_datasets:
            self.discover_datasets()

        return self._discovered_datasets.get(dataset_name)

    def list_datasets(self) -> List[DatasetMetadata]:
        """列出所有可用数据集"""
        if not self._discovered_datasets:
            self.discover_datasets()

        return [adapter.metadata for adapter in self._discovered_datasets.values()]

    def register_custom_adapter(self, dataset_type: DatasetType, adapter_class: type):
        """注册自定义适配器"""
        self.adapters[dataset_type] = adapter_class


class DataProcessor:
    """增强的数据处理器"""

    def __init__(self, dataset_name: str = None, dataset_path: str = None):
        self.registry = DatasetRegistry()

        if dataset_name:
            self.adapter = self.registry.get_dataset(dataset_name)
            if not self.adapter:
                raise ValueError(f"数据集 {dataset_name} 不存在")
        elif dataset_path:
            # 兼容旧版本API
            dataset_dir = Path(dataset_path)
            self.adapter = self.registry._create_adapter(dataset_dir)
            if not self.adapter:
                raise ValueError(f"无法为路径 {dataset_path} 创建适配器")
        else:
            raise ValueError("必须提供 dataset_name 或 dataset_path")

    @property
    def metadata(self) -> DatasetMetadata:
        """获取数据集元数据"""
        return self.adapter.metadata

    def load_response_data(self) -> pd.DataFrame:
        """加载响应数据"""
        return self.adapter.load_response_data()

    def load_q_matrix(self) -> Optional[pd.DataFrame]:
        """加载Q矩阵"""
        return self.adapter.load_q_matrix()

    def get_dataset_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        response_df = self.load_response_data()
        q_matrix_df = self.load_q_matrix()

        stats = {
            'basic_info': {
                'student_num': response_df['student_id'].nunique(),
                'exercise_num': response_df['exercise_id'].nunique(),
                'response_num': len(response_df),
                'knowledge_num': self.metadata.knowledge_num
            },
            'metadata': {
                'name': self.metadata.name,
                'display_name': self.metadata.display_name,
                'description': self.metadata.description,
                'dataset_type': self.metadata.dataset_type.value,
                'has_q_matrix': self.metadata.has_q_matrix,
                'has_hierarchy': self.metadata.has_hierarchy,
                'has_embeddings': self.metadata.has_embeddings
            }
        }

        # 计算平均响应数
        stats['response_stats'] = {
            'avg_responses_per_student': len(response_df) / stats['basic_info']['student_num'],
            'avg_responses_per_exercise': len(response_df) / stats['basic_info']['exercise_num'],
            'response_distribution': response_df['score'].value_counts().to_dict()
        }

        # 计算稀疏度
        total_possible = stats['basic_info']['student_num'] * stats['basic_info']['exercise_num']
        stats['sparsity'] = 1 - (len(response_df) / total_possible)

        # 计算难度分布
        difficulty_stats = response_df.groupby('exercise_id')['score'].agg([
            'mean', 'std', 'count'
        ]).describe()
        stats['difficulty_distribution'] = difficulty_stats.to_dict()

        # Q矩阵统计
        if q_matrix_df is not None:
            stats['q_matrix_stats'] = {
                'shape': q_matrix_df.shape,
                'concepts_per_exercise': q_matrix_df.sum(axis=1).describe().to_dict(),
                'exercises_per_concept': q_matrix_df.sum(axis=0).describe().to_dict(),
                'sparsity': 1 - (q_matrix_df.sum().sum() / q_matrix_df.size)
            }

        # 数据质量检查
        stats['data_quality'] = self._assess_data_quality(response_df, q_matrix_df)

        return stats

    def _assess_data_quality(self, response_df: pd.DataFrame, q_matrix_df: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """评估数据质量"""
        quality = {
            'completeness': 1.0,  # 完整性
            'consistency': 1.0,   # 一致性
            'validity': 1.0,      # 有效性
            'issues': []
        }

        # 检查缺失值
        missing_ratio = response_df.isnull().sum().sum() / response_df.size
        if missing_ratio > 0:
            quality['completeness'] = 1 - missing_ratio
            quality['issues'].append(f"存在 {missing_ratio:.2%} 的缺失值")

        # 检查分数范围
        score_min, score_max = response_df['score'].min(), response_df['score'].max()
        if score_min < 0 or score_max > 1:
            quality['validity'] *= 0.8
            quality['issues'].append(f"分数范围异常: [{score_min}, {score_max}]")

        # 检查ID一致性
        if response_df['student_id'].dtype != response_df['exercise_id'].dtype:
            quality['consistency'] *= 0.9
            quality['issues'].append("学生ID和题目ID数据类型不一致")

        # 检查Q矩阵一致性
        if q_matrix_df is not None:
            max_exercise_id = response_df['exercise_id'].max()
            if len(q_matrix_df) <= max_exercise_id:
                quality['consistency'] *= 0.7
                quality['issues'].append("Q矩阵行数与题目数量不匹配")

        return quality
    
    def prepare_for_training(self, test_size: float = 0.2, seed: int = 42,
                           validation_size: float = 0.1) -> Dict[str, Any]:
        """为训练准备数据 - 增强版本"""
        response_df = self.load_response_data()
        q_matrix_df = self.load_q_matrix()

        # 设置随机种子
        np.random.seed(seed)

        # 数据预处理
        response_df = self._preprocess_response_data(response_df)

        # 按学生分割数据集
        unique_students = response_df['student_id'].unique()
        np.random.shuffle(unique_students)

        # 计算分割点
        n_students = len(unique_students)
        test_split = int(n_students * (1 - test_size))
        val_split = int(test_split * (1 - validation_size))

        train_students = unique_students[:val_split]
        val_students = unique_students[val_split:test_split]
        test_students = unique_students[test_split:]

        # 分割数据
        train_df = response_df[response_df['student_id'].isin(train_students)]
        val_df = response_df[response_df['student_id'].isin(val_students)]
        test_df = response_df[response_df['student_id'].isin(test_students)]

        # 重新编码ID（保证连续性）
        all_students = response_df['student_id'].unique()
        all_exercises = response_df['exercise_id'].unique()

        student_id_map = {old_id: new_id for new_id, old_id in enumerate(sorted(all_students))}
        exercise_id_map = {old_id: new_id for new_id, old_id in enumerate(sorted(all_exercises))}

        # 应用ID映射
        for df in [train_df, val_df, test_df]:
            df = df.copy()
            df['student_id'] = df['student_id'].map(student_id_map)
            df['exercise_id'] = df['exercise_id'].map(exercise_id_map)

        result = {
            'train_data': train_df.values.tolist(),
            'val_data': val_df.values.tolist(),
            'test_data': test_df.values.tolist(),
            'student_num': len(student_id_map),
            'exercise_num': len(exercise_id_map),
            'knowledge_num': self.metadata.knowledge_num,
            'student_id_map': {str(k): int(v) for k, v in student_id_map.items()},
            'exercise_id_map': {str(k): int(v) for k, v in exercise_id_map.items()},
            'dataset_info': {
                'name': self.metadata.name,
                'type': self.metadata.dataset_type.value,
                'train_size': len(train_df),
                'val_size': len(val_df),
                'test_size': len(test_df)
            }
        }

        # 处理Q矩阵
        if q_matrix_df is not None:
            q_matrix_reordered = self._reorder_q_matrix(q_matrix_df, exercise_id_map)
            result['q_matrix'] = q_matrix_reordered.tolist()

        # 加载嵌入（如果可用）
        if hasattr(self.adapter, 'load_embeddings'):
            stu_emb, exer_emb = self.adapter.load_embeddings()
            if stu_emb is not None:
                result['student_embeddings'] = stu_emb.tolist()
            if exer_emb is not None:
                result['exercise_embeddings'] = exer_emb.tolist()

        # 加载层次结构（如果可用）
        if hasattr(self.adapter, 'load_hierarchy'):
            hierarchy = self.adapter.load_hierarchy()
            if hierarchy is not None:
                result['hierarchy'] = hierarchy

        return result

    def _preprocess_response_data(self, response_df: pd.DataFrame) -> pd.DataFrame:
        """预处理响应数据"""
        df = response_df.copy()

        # 移除缺失值
        df = df.dropna()

        # 确保分数在[0,1]范围内
        df['score'] = df['score'].clip(0, 1)

        # 移除异常值（可选）
        # 这里可以添加更多的数据清洗逻辑

        return df

    def _reorder_q_matrix(self, q_matrix_df: pd.DataFrame, exercise_id_map: Dict[int, int]) -> np.ndarray:
        """重新排序Q矩阵"""
        q_matrix_reordered = np.zeros((len(exercise_id_map), q_matrix_df.shape[1]))

        for old_id, new_id in exercise_id_map.items():
            # 确保old_id是整数类型
            old_id = int(old_id)
            new_id = int(new_id)

            if 0 <= old_id < len(q_matrix_df):
                q_matrix_reordered[new_id] = q_matrix_df.iloc[old_id].values

        return q_matrix_reordered
    
    def generate_visualization_data(self) -> Dict[str, Any]:
        """生成可视化数据 - 增强版本"""
        response_df = self.load_response_data()
        q_matrix_df = self.load_q_matrix()

        viz_data = {
            'dataset_info': {
                'name': self.metadata.name,
                'display_name': self.metadata.display_name,
                'type': self.metadata.dataset_type.value
            }
        }

        # 学生能力分布
        student_scores = response_df.groupby('student_id')['score'].agg(['mean', 'count'])
        viz_data['student_ability_distribution'] = {
            'bins': np.histogram(student_scores['mean'], bins=20)[0].tolist(),
            'bin_edges': np.histogram(student_scores['mean'], bins=20)[1].tolist(),
            'statistics': student_scores['mean'].describe().to_dict()
        }

        # 题目难度分布
        exercise_difficulty = response_df.groupby('exercise_id')['score'].agg(['mean', 'count'])
        viz_data['exercise_difficulty_distribution'] = {
            'bins': np.histogram(exercise_difficulty['mean'], bins=20)[0].tolist(),
            'bin_edges': np.histogram(exercise_difficulty['mean'], bins=20)[1].tolist(),
            'statistics': exercise_difficulty['mean'].describe().to_dict()
        }

        # 响应数量分布
        response_counts = response_df.groupby('student_id').size()
        viz_data['response_count_distribution'] = {
            'bins': np.histogram(response_counts, bins=20)[0].tolist(),
            'bin_edges': np.histogram(response_counts, bins=20)[1].tolist(),
            'statistics': response_counts.describe().to_dict()
        }

        # 知识点覆盖情况
        if q_matrix_df is not None:
            concept_coverage = q_matrix_df.sum(axis=0)
            viz_data['concept_coverage'] = {
                'concept_ids': list(range(len(concept_coverage))),
                'exercise_counts': concept_coverage.tolist(),
                'statistics': concept_coverage.describe().to_dict()
            }

            # 题目-知识点关联矩阵热图数据（采样以提高性能）
            if q_matrix_df.shape[0] > 1000:
                # 对大数据集进行采样
                sample_size = min(1000, q_matrix_df.shape[0])
                sampled_indices = np.random.choice(q_matrix_df.shape[0], sample_size, replace=False)
                sampled_q_matrix = q_matrix_df.iloc[sampled_indices]
            else:
                sampled_q_matrix = q_matrix_df

            viz_data['q_matrix_heatmap'] = {
                'data': sampled_q_matrix.values.tolist(),
                'exercise_ids': list(range(sampled_q_matrix.shape[0])),
                'concept_ids': list(range(sampled_q_matrix.shape[1])),
                'is_sampled': q_matrix_df.shape[0] > 1000,
                'original_size': q_matrix_df.shape[0]
            }

        # 学习进度分析
        viz_data['learning_progress'] = self._analyze_learning_progress(response_df)

        # 时间序列数据（如果有时间戳）
        if 'timestamp' in response_df.columns:
            response_df['timestamp'] = pd.to_datetime(response_df['timestamp'])
            daily_responses = response_df.groupby(response_df['timestamp'].dt.date).size()
            viz_data['daily_response_trend'] = {
                'dates': [str(date) for date in daily_responses.index],
                'counts': daily_responses.tolist()
            }

        return viz_data

    def _analyze_learning_progress(self, response_df: pd.DataFrame) -> Dict[str, Any]:
        """分析学习进度"""
        # 按学生分析答题序列
        progress_data = {}

        # 计算每个学生的平均正确率变化趋势
        student_progress = []
        for student_id in response_df['student_id'].unique()[:100]:  # 限制分析数量
            student_data = response_df[response_df['student_id'] == student_id].sort_values('exercise_id')
            if len(student_data) >= 10:  # 至少10次答题
                # 计算滑动平均正确率
                window_size = min(10, len(student_data) // 3)
                rolling_accuracy = student_data['score'].rolling(window=window_size).mean()

                if not rolling_accuracy.isna().all():
                    progress_data[f'student_{student_id}'] = {
                        'accuracy_trend': rolling_accuracy.dropna().tolist(),
                        'total_responses': len(student_data),
                        'final_accuracy': student_data['score'].tail(window_size).mean()
                    }

        return {
            'individual_progress': progress_data,
            'summary': {
                'students_analyzed': len(progress_data),
                'avg_improvement': np.mean([
                    data['final_accuracy'] - data['accuracy_trend'][0]
                    for data in progress_data.values()
                    if len(data['accuracy_trend']) > 0
                ]) if progress_data else 0
            }
        }
    
    def export_processed_data(self, output_dir: str, format: str = 'csv') -> Dict[str, str]:
        """导出处理后的数据"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        response_df = self.load_response_data()
        q_matrix_df = self.load_q_matrix()

        exported_files = {}

        if format == 'csv':
            # 导出响应数据
            response_file = output_path / 'response_processed.csv'
            response_df.to_csv(response_file, index=False)
            exported_files['response'] = str(response_file)

            # 导出Q矩阵
            if q_matrix_df is not None:
                q_matrix_file = output_path / 'q_matrix_processed.csv'
                q_matrix_df.to_csv(q_matrix_file, index=False, header=False)
                exported_files['q_matrix'] = str(q_matrix_file)

            # 导出统计信息
            stats = self.get_dataset_statistics()
            stats_file = output_path / 'statistics.json'
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, indent=2, ensure_ascii=False, default=str)
            exported_files['statistics'] = str(stats_file)

            # 导出元数据
            metadata_file = output_path / 'metadata.json'
            with open(metadata_file, 'w', encoding='utf-8') as f:
                metadata_dict = {
                    'name': self.metadata.name,
                    'display_name': self.metadata.display_name,
                    'description': self.metadata.description,
                    'dataset_type': self.metadata.dataset_type.value,
                    'student_num': self.metadata.student_num,
                    'exercise_num': self.metadata.exercise_num,
                    'knowledge_num': self.metadata.knowledge_num,
                    'response_num': self.metadata.response_num,
                    'has_q_matrix': self.metadata.has_q_matrix,
                    'has_hierarchy': self.metadata.has_hierarchy,
                    'has_embeddings': self.metadata.has_embeddings
                }
                json.dump(metadata_dict, f, indent=2, ensure_ascii=False)
            exported_files['metadata'] = str(metadata_file)

        elif format == 'numpy':
            # 导出为numpy格式
            response_file = output_path / 'response.npy'
            np.save(response_file, response_df.values)
            exported_files['response'] = str(response_file)

            if q_matrix_df is not None:
                q_matrix_file = output_path / 'q_matrix.npy'
                np.save(q_matrix_file, q_matrix_df.values)
                exported_files['q_matrix'] = str(q_matrix_file)

        return exported_files


class DatasetIntegrator:
    """数据集集成器 - 真实数据版本"""

    def __init__(self):
        self.registry = DatasetRegistry()
        self.datasets_dir = Path(settings.DATASETS_DIR)

    def integrate_all_datasets(self) -> Dict[str, Any]:
        """集成所有可用数据集"""
        # 发现所有数据集
        datasets = self.registry.discover_datasets()

        integrated_data = {
            'available_datasets': [],
            'training_ready': [],
            'visualization_ready': []
        }

        for dataset_name, adapter in datasets.items():
            try:
                processor = DataProcessor(dataset_name=dataset_name)

                # 获取基本信息
                metadata = processor.metadata
                stats = processor.get_dataset_statistics()

                dataset_info = {
                    'name': metadata.name,
                    'display_name': metadata.display_name,
                    'description': metadata.description,
                    'type': metadata.dataset_type.value,
                    'student_num': metadata.student_num,
                    'exercise_num': metadata.exercise_num,
                    'knowledge_num': metadata.knowledge_num,
                    'response_num': metadata.response_num,
                    'has_q_matrix': metadata.has_q_matrix,
                    'has_hierarchy': metadata.has_hierarchy,
                    'has_embeddings': metadata.has_embeddings,
                    'data_quality': stats.get('data_quality', {}),
                    'sparsity': stats.get('sparsity', 0)
                }

                integrated_data['available_datasets'].append(dataset_info)

                # 检查是否适合训练
                if self._is_training_ready(metadata, stats):
                    integrated_data['training_ready'].append(dataset_name)

                # 所有数据集都可以用于可视化
                integrated_data['visualization_ready'].append(dataset_name)

            except Exception as e:
                logger.error(f"集成数据集 {dataset_name} 失败: {e}")

        return integrated_data

    def _is_training_ready(self, metadata: DatasetMetadata, stats: Dict[str, Any]) -> bool:
        """检查数据集是否适合训练"""
        # 基本要求
        if metadata.student_num < 100 or metadata.exercise_num < 100:
            return False

        if metadata.response_num < 1000:
            return False

        # 数据质量要求
        quality = stats.get('data_quality', {})
        if quality.get('completeness', 0) < 0.9:
            return False

        if quality.get('validity', 0) < 0.8:
            return False

        # 稀疏度要求
        if stats.get('sparsity', 1) > 0.99:
            return False

        return True

    def get_dataset_for_training(self, dataset_name: str) -> Dict[str, Any]:
        """获取用于训练的数据集"""
        processor = DataProcessor(dataset_name=dataset_name)

        # 准备训练数据
        training_data = processor.prepare_for_training(test_size=0.2, seed=42)

        # 获取统计信息
        stats = processor.get_dataset_statistics()

        return {
            'training_data': training_data,
            'statistics': stats,
            'metadata': {
                'name': processor.metadata.name,
                'display_name': processor.metadata.display_name,
                'description': processor.metadata.description,
                'type': processor.metadata.dataset_type.value
            }
        }

    def get_dataset_for_visualization(self, dataset_name: str) -> Dict[str, Any]:
        """获取用于可视化的数据集"""
        processor = DataProcessor(dataset_name=dataset_name)

        # 生成可视化数据
        viz_data = processor.generate_visualization_data()

        # 获取统计信息
        stats = processor.get_dataset_statistics()

        return {
            'visualization_data': viz_data,
            'statistics': stats,
            'metadata': {
                'name': processor.metadata.name,
                'display_name': processor.metadata.display_name,
                'description': processor.metadata.description,
                'type': processor.metadata.dataset_type.value
            }
        }

    # 保持向后兼容性的方法
    def integrate_assist_dataset(self) -> Dict[str, Any]:
        """集成Assist数据集用于实际演示（向后兼容）"""
        return self.get_dataset_for_training('Assist0910')

    def integrate_visualization_datasets(self) -> Dict[str, Dict[str, Any]]:
        """集成其他数据集用于可视化（向后兼容）"""
        datasets = self.registry.discover_datasets()
        viz_datasets = {}

        for dataset_name in datasets.keys():
            if dataset_name != 'Assist0910':  # 排除主训练数据集
                try:
                    viz_datasets[dataset_name] = self.get_dataset_for_visualization(dataset_name)
                except Exception as e:
                    logger.error(f"处理数据集 {dataset_name} 失败: {e}")

        return viz_datasets
