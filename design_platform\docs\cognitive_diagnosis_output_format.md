# 认知诊断输出格式说明

## 1. 单个学生诊断结果

### API端点
```
POST /api/v1/diagnosis/student?student_id={student_id}&training_id={training_id}
```

### 输出格式
```json
{
  "success": true,
  "student_id": "student_001",
  "diagnosis": {
    "student_id": "student_001",
    "diagnosis_time": "2024-01-17T10:30:00.123456",
    "knowledge_diagnosis": {
      "代数运算": {
        "mastery_probability": 0.856,
        "mastery_level": "掌握",
        "confidence": 0.923
      },
      "几何推理": {
        "mastery_probability": 0.634,
        "mastery_level": "部分掌握", 
        "confidence": 0.887
      },
      "函数理解": {
        "mastery_probability": 0.423,
        "mastery_level": "未掌握",
        "confidence": 0.756
      },
      "概率统计": {
        "mastery_probability": 0.789,
        "mastery_level": "掌握",
        "confidence": 0.912
      },
      "逻辑推理": {
        "mastery_probability": 0.567,
        "mastery_level": "部分掌握",
        "confidence": 0.834
      }
    },
    "overall_ability": 0.654
  }
}
```

## 2. 批量学生诊断结果

### API端点
```
POST /api/v1/diagnosis/batch-diagnosis
```

### 输入格式
```json
{
  "experiment_id": 123,
  "student_ids": ["student_001", "student_002", "student_003"]
}
```

### 输出格式
```json
{
  "experiment_id": 123,
  "total_students": 3,
  "successful_diagnoses": 3,
  "diagnoses": [
    {
      "student_id": "student_001",
      "success": true,
      "diagnosis": {
        "student_id": "student_001",
        "diagnosis_time": "2024-01-17T10:30:00.123456",
        "knowledge_diagnosis": {
          "代数运算": {
            "mastery_probability": 0.856,
            "mastery_level": "掌握",
            "confidence": 0.923
          }
          // ... 其他知识点
        },
        "overall_ability": 0.654
      }
    }
    // ... 其他学生
  ]
}
```

## 3. 模型训练结果

### API端点
```
POST /api/v1/diagnosis/train
```

### 输出格式
```json
{
  "success": true,
  "message": "模型训练完成",
  "experiment_id": 123,
  "results": {
    "model_type": "ORCDF",
    "training_history": {
      "loss": [0.8, 0.75, 0.7, ..., 0.2],
      "accuracy": [0.5, 0.55, 0.6, ..., 0.8],
      "auc": [0.6, 0.65, 0.7, ..., 0.85]
    },
    "knowledge_states": [
      [1, 0, 1, 0, 1],  // 学生1的知识状态
      [0, 1, 1, 1, 0],  // 学生2的知识状态
      // ... 更多学生
    ],
    "final_accuracy": 0.8,
    "final_auc": 0.85,
    "final_loss": 0.2,
    "config": {
      "epochs": 100,
      "learning_rate": 0.001,
      "batch_size": 256
    }
  }
}
```

## 4. 学习路径生成结果

### API端点
```
POST /api/v1/diagnosis/learning-path
```

### 输出格式
```json
{
  "success": true,
  "student_id": "student_001",
  "learning_path": {
    "student_id": "student_001",
    "generated_at": "2024-01-17T10:30:00",
    "learning_path": [
      {
        "stage": 1,
        "knowledge_component": "函数理解",
        "current_mastery": 0.423,
        "target_mastery": 0.8,
        "learning_strategy": "基础概念强化",
        "activities": [
          {
            "activity_id": "func_basic_001",
            "type": "concept_learning",
            "title": "函数基础概念学习",
            "description": "学习函数的定义和基本性质",
            "duration_minutes": 30,
            "difficulty_level": "初级",
            "resources": [
              {
                "type": "video",
                "title": "函数概念讲解",
                "url": "/resources/videos/func_basic.mp4"
              }
            ]
          }
        ],
        "estimated_duration": 120,
        "prerequisites": [],
        "success_criteria": ["完成基础练习", "通过概念测试"]
      }
    ],
    "recommendations": {
      "priority_knowledge_components": ["函数理解", "几何推理"],
      "suggested_study_time": "每天30分钟",
      "learning_style": "视觉化学习"
    },
    "estimated_time": {
      "total_hours": 15,
      "daily_minutes": 30,
      "completion_days": 30
    },
    "weak_knowledge_components": [
      {
        "name": "函数理解",
        "mastery_probability": 0.423,
        "priority": "高"
      }
    ],
    "strong_knowledge_components": [
      {
        "name": "代数运算", 
        "mastery_probability": 0.856,
        "can_help_with": ["函数理解"]
      }
    ],
    "overall_ability_level": "中等"
  },
  "diagnosis": {
    // 诊断结果（同上）
  }
}
```

## 5. 字段说明

### 知识点掌握度字段
- `mastery_probability`: 掌握概率 (0-1之间的浮点数)
- `mastery_level`: 掌握水平
  - "掌握": mastery_probability > 0.7
  - "部分掌握": 0.5 < mastery_probability <= 0.7  
  - "未掌握": mastery_probability <= 0.5
- `confidence`: 诊断置信度 (0-1之间的浮点数)

### 整体能力字段
- `overall_ability`: 总体能力水平，所有知识点掌握概率的平均值

### 训练历史字段
- `loss`: 训练损失历史
- `accuracy`: 准确率历史
- `auc`: AUC指标历史

### 知识状态矩阵
- `knowledge_states`: 二维数组，每行代表一个学生，每列代表一个知识点
- 值为0或1，表示是否掌握该知识点

## 6. ORCDF模型特色输出

ORCDF模型相比其他模型的特色：

1. **过度平滑抗性**: 学生间的诊断结果差异更明显
2. **更高精度**: 通常比NCDM、KANCD等模型有更高的准确率和AUC
3. **响应信号利用**: 在学习部分也利用了响应信号，不仅仅作为标签

### 模型对比结果示例
```json
{
  "dataset_id": 1,
  "model_count": 3,
  "best_model": {
    "experiment_id": 123,
    "model_type": "ORCDF",
    "accuracy": 0.8456,
    "auc": 0.8923,
    "rmse": 0.2134
  },
  "comparison_data": [
    {
      "model_type": "ORCDF",
      "accuracy": 0.8456,
      "auc": 0.8923,
      "rmse": 0.2134
    },
    {
      "model_type": "NCDM", 
      "accuracy": 0.7834,
      "auc": 0.8234,
      "rmse": 0.2567
    },
    {
      "model_type": "KANCD",
      "accuracy": 0.7923,
      "auc": 0.8345,
      "rmse": 0.2456
    }
  ]
}
```
