import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Spin, Alert, Table, Tag, Divider, message } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, SyncOutlined, ApiOutlined } from '@ant-design/icons';
import { datasetService, experimentService, apiUtils } from '../services/api';

const { Title, Paragraph, Text } = Typography;

// 动态API配置 - 支持内网穿透
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // 检测当前访问域名
  const currentHost = window.location.hostname;

  // 如果是内网穿透域名，使用相对路径或提示用户配置
  if (currentHost.includes('ngrok') || currentHost.includes('cpolar') || currentHost.includes('frp')) {
    // 内网穿透环境，需要用户手动配置后端地址
    const backendUrl = localStorage.getItem('BACKEND_URL');
    if (backendUrl) {
      return `${backendUrl}/api/v1`;
    }

    // 如果没有配置，提示用户配置
    console.warn('🌐 检测到内网穿透环境，请配置后端地址');
    return 'http://localhost:8000/api/v1'; // 默认值
  }

  // 本地开发环境
  return 'http://localhost:8000/api/v1';
};

interface ApiTestResult {
  name: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  data?: any;
  timestamp: string;
}

const ApiTest: React.FC = () => {
  const [testResults, setTestResults] = useState<ApiTestResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [healthStatus, setHealthStatus] = useState<boolean | null>(null);
  const [datasets, setDatasets] = useState<any[]>([]);
  const [experiments, setExperiments] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [datasetsData, setDatasetsData] = useState<any>(null);
  const [modelsData, setModelsData] = useState<any>(null);

  useEffect(() => {
    runInitialTests();
  }, []);

  const addTestResult = (result: Omit<ApiTestResult, 'timestamp'>) => {
    const newResult: ApiTestResult = {
      ...result,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [newResult, ...prev.slice(0, 9)]); // 保留最近10条记录
  };

  const runInitialTests = async () => {
    setLoading(true);
    await testApiHealth();
    await testDatasetsApi();
    await testExperimentsApi();
    setLoading(false);
  };

  const testApiHealth = async () => {
    try {
      const isHealthy = await apiUtils.checkHealth();
      setHealthStatus(isHealthy);
      addTestResult({
        name: 'API健康检查',
        status: isHealthy ? 'success' : 'error',
        message: isHealthy ? 'API服务正常' : 'API服务异常'
      });
    } catch (error) {
      setHealthStatus(false);
      addTestResult({
        name: 'API健康检查',
        status: 'error',
        message: `健康检查失败: ${error}`
      });
    }
  };

  const testDatasetsApi = async () => {
    try {
      const response = await datasetService.getDatasets();
      setDatasets(response.data);
      setDatasetsData(response.data);
      addTestResult({
        name: '数据集API',
        status: 'success',
        message: `成功获取 ${response.data.length} 个数据集`,
        data: response.data
      });
    } catch (error) {
      addTestResult({
        name: '数据集API',
        status: 'error',
        message: `数据集API失败: ${error}`
      });
    }
  };

  const testExperimentsApi = async () => {
    try {
      const response = await experimentService.getExperiments();
      setExperiments(response.data);
      addTestResult({
        name: '实验API',
        status: 'success',
        message: `成功获取 ${response.data.length} 个实验`,
        data: response.data
      });
    } catch (error) {
      addTestResult({
        name: '实验API',
        status: 'error',
        message: `实验API失败: ${error}`
      });
    }
  };

  const testModelsApi = async () => {
    try {
      // 模拟模型API测试，因为可能没有专门的模型服务
      const mockModelsData = [
        { id: 1, name: 'ORCDF', type: 'cognitive_diagnosis', status: 'available' },
        { id: 2, name: 'NCD', type: 'neural_cognitive_diagnosis', status: 'available' },
        { id: 3, name: 'IRT', type: 'item_response_theory', status: 'available' }
      ];
      setModelsData(mockModelsData);
      addTestResult({
        name: '模型API',
        status: 'success',
        message: `成功获取 ${mockModelsData.length} 个模型`,
        data: mockModelsData
      });
    } catch (error) {
      addTestResult({
        name: '模型API',
        status: 'error',
        message: `模型API失败: ${error}`
      });
    }
  };

  const testSyncApi = async () => {
    try {
      setLoading(true);
      const response = await datasetService.syncDatasets();
      addTestResult({
        name: '数据集同步',
        status: 'success',
        message: response.data.message || '同步成功'
      });
      message.success('数据集同步成功');
      // 重新加载数据集
      await testDatasetsApi();
    } catch (error) {
      addTestResult({
        name: '数据集同步',
        status: 'error',
        message: `同步失败: ${error}`
      });
      message.error('数据集同步失败');
    } finally {
      setLoading(false);
    }
  };

  const testAllEndpoints = async () => {
    setLoading(true);
    const results = await apiUtils.testAllEndpoints();

    Object.entries(results).forEach(([name, success]) => {
      addTestResult({
        name: `${name} 端点测试`,
        status: success ? 'success' : 'error',
        message: success ? '端点正常' : '端点异常'
      });
    });

    setLoading(false);
  };

  const syncDatasets = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/datasets/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        console.log('同步结果:', result);
        alert('同步成功！');
        // 重新测试数据集API
        await testDatasetsApi();
      } else {
        throw new Error(`同步失败: ${response.status}`);
      }

    } catch (err) {
      console.error('同步错误:', err);
      setError(`同步错误: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const testLLMDiagnosis = async () => {
    try {
      setLoading(true);
      setError(null);

      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/cognitive-diagnosis/diagnose-with-report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          student_id: 'student_001',
          experiment_id: 8
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('LLM诊断结果:', result);
        alert('LLM诊断测试成功！查看控制台获取详细结果');
      } else {
        throw new Error(`LLM诊断失败: ${response.status}`);
      }

    } catch (err) {
      console.error('LLM诊断错误:', err);
      setError(`LLM诊断错误: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testDatasetsApi();
    testModelsApi();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>API 测试页面</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Card title="操作">
          <Space>
            <Button onClick={testDatasetsApi} loading={loading}>
              测试数据集API
            </Button>
            <Button onClick={testModelsApi} loading={loading}>
              测试模型API
            </Button>
            <Button onClick={syncDatasets} loading={loading} type="primary">
              同步数据集
            </Button>
            <Button onClick={testLLMDiagnosis} loading={loading} type="primary" style={{ backgroundColor: '#722ed1' }}>
              测试LLM诊断
            </Button>
          </Space>
        </Card>

        {error && (
          <Alert message="错误" description={error} type="error" showIcon />
        )}

        {loading && (
          <Card>
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="large" />
              <div style={{ marginTop: '16px' }}>加载中...</div>
            </div>
          </Card>
        )}

        <Card title="数据集API响应">
          <Paragraph>
            <Text strong>数据集数量:</Text> {datasetsData ? (Array.isArray(datasetsData) ? datasetsData.length : '未知格式') : '未加载'}
          </Paragraph>
          <Paragraph>
            <Text strong>原始响应:</Text>
          </Paragraph>
          <pre style={{ background: '#f5f5f5', padding: '16px', borderRadius: '4px', overflow: 'auto' }}>
            {datasetsData ? JSON.stringify(datasetsData, null, 2) : '未加载'}
          </pre>
        </Card>

        <Card title="模型API响应">
          <Paragraph>
            <Text strong>模型数量:</Text> {modelsData ? (Array.isArray(modelsData) ? modelsData.length : '未知格式') : '未加载'}
          </Paragraph>
          <Paragraph>
            <Text strong>原始响应:</Text>
          </Paragraph>
          <pre style={{ background: '#f5f5f5', padding: '16px', borderRadius: '4px', overflow: 'auto' }}>
            {modelsData ? JSON.stringify(modelsData, null, 2) : '未加载'}
          </pre>
        </Card>
      </Space>
    </div>
  );
};

export default ApiTest;
