#!/usr/bin/env python3
"""
测试数据预处理管道
"""
import sys
import os
from pathlib import Path
import numpy as np
import pandas as pd

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

# 创建简化的设置类
class MockSettings:
    DATASETS_DIR = "data/datasets"

# 模拟设置
import app.core.config
app.core.config.settings = MockSettings()

from app.services.data_processor import DataProcessor, DatasetIntegrator

def test_data_processor_pipeline():
    """测试数据处理管道"""
    print("=== 测试数据处理管道 ===")
    
    try:
        # 测试Assist0910数据集
        processor = DataProcessor(dataset_name='Assist0910')
        print(f"✓ 成功创建数据处理器: {processor.metadata.display_name}")
        
        # 测试数据加载
        response_df = processor.load_response_data()
        print(f"✓ 响应数据加载成功: {len(response_df)} 条记录")
        
        q_matrix_df = processor.load_q_matrix()
        if q_matrix_df is not None:
            print(f"✓ Q矩阵加载成功: {q_matrix_df.shape}")
        else:
            print("✗ Q矩阵加载失败")
        
        # 测试统计信息
        stats = processor.get_dataset_statistics()
        print(f"✓ 统计信息生成成功")
        print(f"  基本信息: {stats['basic_info']}")
        print(f"  数据质量: {stats['data_quality']}")
        
        # 测试训练数据准备
        training_data = processor.prepare_for_training(test_size=0.2, seed=42)
        print(f"✓ 训练数据准备成功")
        print(f"  训练集: {training_data['dataset_info']['train_size']} 条")
        print(f"  验证集: {training_data['dataset_info']['val_size']} 条")
        print(f"  测试集: {training_data['dataset_info']['test_size']} 条")
        print(f"  学生数: {training_data['student_num']}")
        print(f"  题目数: {training_data['exercise_num']}")
        print(f"  知识点数: {training_data['knowledge_num']}")
        
        # 测试可视化数据
        viz_data = processor.generate_visualization_data()
        print(f"✓ 可视化数据生成成功")
        print(f"  包含组件: {list(viz_data.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理管道测试失败: {e}")
        return False

def test_dataset_integrator():
    """测试数据集集成器"""
    print("\n=== 测试数据集集成器 ===")
    
    try:
        integrator = DatasetIntegrator()
        
        # 测试集成所有数据集
        integrated = integrator.integrate_all_datasets()
        print(f"✓ 数据集集成成功")
        print(f"  可用数据集: {len(integrated['available_datasets'])}")
        print(f"  训练就绪: {len(integrated['training_ready'])}")
        print(f"  可视化就绪: {len(integrated['visualization_ready'])}")
        
        # 显示数据集详情
        for dataset in integrated['available_datasets']:
            print(f"  - {dataset['name']}: {dataset['student_num']}学生, {dataset['exercise_num']}题目")
            print(f"    质量分数: {dataset['data_quality'].get('validity', 'N/A')}")
            print(f"    稀疏度: {dataset['sparsity']:.3f}")
        
        # 测试获取训练数据
        if integrated['training_ready']:
            dataset_name = integrated['training_ready'][0]
            print(f"\n测试获取训练数据: {dataset_name}")
            
            training_data = integrator.get_dataset_for_training(dataset_name)
            if training_data:
                print(f"✓ 训练数据获取成功")
                metadata = training_data['metadata']
                print(f"  数据集: {metadata['display_name']}")
                print(f"  类型: {metadata['type']}")
            else:
                print("✗ 训练数据获取失败")
        
        # 测试获取可视化数据
        if integrated['visualization_ready']:
            dataset_name = integrated['visualization_ready'][0]
            print(f"\n测试获取可视化数据: {dataset_name}")
            
            viz_data = integrator.get_dataset_for_visualization(dataset_name)
            if viz_data:
                print(f"✓ 可视化数据获取成功")
                viz_components = viz_data['visualization_data']
                print(f"  可视化组件: {list(viz_components.keys())}")
            else:
                print("✗ 可视化数据获取失败")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据集集成器测试失败: {e}")
        return False

def test_data_quality_assessment():
    """测试数据质量评估"""
    print("\n=== 测试数据质量评估 ===")
    
    try:
        processor = DataProcessor(dataset_name='Assist0910')
        stats = processor.get_dataset_statistics()
        
        quality = stats.get('data_quality', {})
        print(f"数据质量评估结果:")
        print(f"  完整性: {quality.get('completeness', 0):.3f}")
        print(f"  一致性: {quality.get('consistency', 0):.3f}")
        print(f"  有效性: {quality.get('validity', 0):.3f}")
        
        issues = quality.get('issues', [])
        if issues:
            print(f"  发现问题: {len(issues)} 个")
            for issue in issues:
                print(f"    - {issue}")
        else:
            print(f"  ✓ 未发现数据质量问题")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据质量评估失败: {e}")
        return False

def test_multiple_datasets():
    """测试多个数据集"""
    print("\n=== 测试多个数据集 ===")
    
    dataset_names = ['Assist0910', 'Assist17', 'Junyi', 'NeurIPS2020']
    results = {}
    
    for dataset_name in dataset_names:
        try:
            processor = DataProcessor(dataset_name=dataset_name)
            stats = processor.get_dataset_statistics()
            
            results[dataset_name] = {
                'success': True,
                'student_num': stats['basic_info']['student_num'],
                'exercise_num': stats['basic_info']['exercise_num'],
                'knowledge_num': stats['basic_info']['knowledge_num'],
                'response_num': stats['basic_info']['response_num'],
                'sparsity': stats.get('sparsity', 0),
                'quality': stats['data_quality'].get('validity', 0)
            }
            print(f"✓ {dataset_name}: {results[dataset_name]['student_num']}学生, {results[dataset_name]['exercise_num']}题目")
            
        except Exception as e:
            results[dataset_name] = {'success': False, 'error': str(e)}
            print(f"✗ {dataset_name}: {e}")
    
    # 汇总结果
    successful = [name for name, result in results.items() if result.get('success', False)]
    print(f"\n成功处理 {len(successful)}/{len(dataset_names)} 个数据集")
    
    return len(successful) > 0

if __name__ == "__main__":
    print("开始测试数据预处理管道...")
    
    success_count = 0
    total_tests = 4
    
    # 运行所有测试
    if test_data_processor_pipeline():
        success_count += 1
    
    if test_dataset_integrator():
        success_count += 1
    
    if test_data_quality_assessment():
        success_count += 1
    
    if test_multiple_datasets():
        success_count += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ 所有测试通过！数据预处理管道工作正常")
    else:
        print("✗ 部分测试失败，需要检查问题")
    
    print("测试完成!")
