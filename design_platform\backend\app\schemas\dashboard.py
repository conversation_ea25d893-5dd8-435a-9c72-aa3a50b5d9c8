"""
仪表板相关的数据模型
"""
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from enum import Enum


class ActivityType(str, Enum):
    """活动类型枚举"""
    EXPERIMENT_CREATED = "experiment_created"
    EXPERIMENT_STARTED = "experiment_started"
    EXPERIMENT_COMPLETED = "experiment_completed"
    EXPERIMENT_FAILED = "experiment_failed"
    DATASET_UPLOADED = "dataset_uploaded"
    MODEL_TRAINED = "model_trained"


class DashboardStats(BaseModel):
    """仪表板统计数据"""
    total_datasets: int
    total_experiments: int
    running_experiments: int
    completed_experiments: int
    failed_experiments: int
    success_rate: float
    
    class Config:
        from_attributes = True


class RecentActivity(BaseModel):
    """最近活动"""
    id: str
    type: ActivityType
    title: str
    description: str
    timestamp: datetime
    status: str
    
    class Config:
        from_attributes = True


class SystemStatus(BaseModel):
    """系统状态"""
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    completion_rate: float
    active_tasks: int
    uptime_hours: int
    
    class Config:
        from_attributes = True


class PerformanceMetrics(BaseModel):
    """性能指标"""
    daily_experiments: List[int]
    success_rate: List[float]
    dates: List[str]
    
    class Config:
        from_attributes = True


class ModelComparison(BaseModel):
    """模型对比数据"""
    model_type: str
    total_experiments: int
    avg_accuracy: float
    avg_auc: float
    
    class Config:
        from_attributes = True
