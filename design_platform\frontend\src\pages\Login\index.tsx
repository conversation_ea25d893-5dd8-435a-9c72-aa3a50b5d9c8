import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, message, Typography, Alert } from 'antd';
import { UserOutlined, LockOutlined, ExperimentOutlined, SettingOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { authService } from '../../services/api';
import BackendConfig from '../../components/BackendConfig';
import './index.css';

const { Title, Paragraph } = Typography;

interface LoginForm {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [showBackendConfig, setShowBackendConfig] = useState(false);
  const [isExternalAccess, setIsExternalAccess] = useState(false);
  const [configCompleted, setConfigCompleted] = useState(false);
  const navigate = useNavigate();

  // 检测是否为内网穿透环境
  useEffect(() => {
    const currentHost = window.location.hostname;
    const isExternal = currentHost.includes('ngrok') ||
                      currentHost.includes('cpolar') ||
                      currentHost.includes('frp') ||
                      currentHost.includes('tunnel');

    setIsExternalAccess(isExternal);

    // 如果是外网访问且没有配置后端地址，显示配置弹窗
    if (isExternal && !localStorage.getItem('BACKEND_URL')) {
      setShowBackendConfig(true);
    }
  }, []);

  const onFinish = async (values: LoginForm) => {
    setLoading(true);

    try {
      // 使用统一的API服务进行登录验证
      const response = await authService.login({
        username: values.username,
        password: values.password,
      });

      const data = response.data;

      if (data.success) {
        message.success('登录成功！欢迎使用知擎EduBrain系统');

        // 保存用户信息和token
        localStorage.setItem('user', JSON.stringify(data.user));
        localStorage.setItem('token', data.token);

        // 直接跳转到主面板
        navigate('/dashboard');
      } else {
        message.error(data.message || '登录失败');
      }
    } catch (error: any) {
      console.error('登录错误:', error);

      // 处理不同类型的错误
      if (error.response) {
        // 服务器返回错误状态码
        const status = error.response.status;
        const errorData = error.response.data;

        if (status === 401) {
          message.error('用户名或密码错误');
        } else if (status === 404) {
          message.error('登录服务不可用，请检查后端配置');
        } else {
          message.error(errorData?.detail || errorData?.message || '登录失败');
        }
      } else if (error.request) {
        // 网络错误
        message.error('网络连接失败，请检查网络或后端服务状态');
      } else {
        // 其他错误
        message.error('登录过程中发生未知错误');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="brain-animation">
          <ExperimentOutlined className="brain-icon" />
        </div>
      </div>
      
      <Card className="login-card" bordered={false}>
        <div className="login-header">
          <Title level={2} className="login-title">
            🧠 知擎EduBrain
          </Title>
          <Paragraph className="login-subtitle">
            双驱协同认知诊断与规划系统
          </Paragraph>
        </div>

        <Form
          name="login"
          onFinish={onFinish}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="用户名"
              className="login-input"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="密码"
              className="login-input"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="login-button"
              block
            >
              登录系统
            </Button>
          </Form.Item>
        </Form>

        {/* 外网访问提示 */}
        {isExternalAccess && (
          <Alert
            message="外网访问检测"
            description={
              <div>
                检测到您正在通过内网穿透访问系统。
                <Button
                  type="link"
                  size="small"
                  icon={<SettingOutlined />}
                  onClick={() => setShowBackendConfig(true)}
                  style={{ padding: 0, marginLeft: 8 }}
                >
                  配置后端地址
                </Button>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <div className="login-demo-info">
          <Paragraph type="secondary" style={{ textAlign: 'center', fontSize: '12px' }}>
            测试账号：test1~test5 / 123456 | admin / secret
          </Paragraph>
          {process.env.NODE_ENV === 'development' && (
            <Paragraph style={{ textAlign: 'center', fontSize: '12px' }}>
              <Button
                type="link"
                size="small"
                onClick={() => window.open('/config-test', '_blank')}
              >
                🔧 配置测试工具
              </Button>
            </Paragraph>
          )}
        </div>

        <div className="login-features">
          <div className="feature-item">
            <span className="feature-icon">📊</span>
            <span>认知诊断</span>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🎯</span>
            <span>学习规划</span>
          </div>
          <div className="feature-item">
            <span className="feature-icon">🤖</span>
            <span>AI驱动</span>
          </div>
        </div>
      </Card>

      {/* 后端配置弹窗 */}
      <BackendConfig
        visible={showBackendConfig}
        onClose={() => setShowBackendConfig(false)}
        onConfigured={(backendUrl) => {
          console.log('后端地址已配置:', backendUrl);
          setConfigCompleted(true);
          // 显示刷新提示
          message.info({
            content: (
              <div>
                配置已保存！请
                <Button
                  type="link"
                  size="small"
                  onClick={() => window.location.reload()}
                  style={{ padding: '0 4px' }}
                >
                  点击刷新
                </Button>
                页面以应用新配置
              </div>
            ),
            duration: 0,
            key: 'refresh-reminder'
          });
        }}
      />
    </div>
  );
};

export default Login;
