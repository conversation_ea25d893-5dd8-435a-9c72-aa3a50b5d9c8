import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  InputNumber,
  Button,
  Space,
  Divider,
  Row,
  Col,
  Input,
  Switch,
  message,
  Collapse,
  Tooltip,
  Alert,
} from 'antd';
import {
  ExperimentOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { datasetService, experimentService } from '../../services/api';

const { Option } = Select;
const { Panel } = Collapse;
const { TextArea } = Input;

interface Dataset {
  id: number;
  name: string;
  display_name: string;
  is_demo: boolean;
}

const ExperimentConfig: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    loadDatasets();
  }, []);

  const loadDatasets = async () => {
    setLoading(true);
    try {
      const response = await datasetService.getDatasets();
      setDatasets(response.data);
    } catch (error) {
      message.error('加载数据集失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      const experimentData = {
        name: values.name,
        description: values.description,
        dataset_type: values.dataset_type,
        model_type: values.model_type,
        config: {
          batch_size: values.batch_size,
          epochs: values.epochs,
          learning_rate: values.learning_rate,
          weight_decay: values.weight_decay,
          latent_dim: values.latent_dim,
          gcn_layers: values.gcn_layers,
          keep_prob: values.keep_prob,
          ssl_weight: values.ssl_weight,
          ssl_temp: values.ssl_temp,
          flip_ratio: values.flip_ratio,
          test_size: values.test_size,
          seed: values.seed,
          device: values.device,
        },
        auto_start: values.auto_start,
      };

      const response = await experimentService.createExperiment(experimentData);
      message.success('实验创建成功');
      
      if (values.auto_start) {
        navigate('/experiment/monitor');
      } else {
        navigate(`/experiment/monitor?id=${response.data.id}`);
      }
    } catch (error) {
      message.error('创建实验失败');
    } finally {
      setSubmitting(false);
    }
  };

  const modelOptions = [
    { value: 'orcdf', label: 'EduBrain抗过平滑特征丰富方法', description: 'EduBrain抗过平滑特征丰富方法，提供更丰富的特征表示' },
    { value: 'ncdm', label: 'NCDM', description: '神经认知诊断模型' },
    { value: 'kancd', label: 'KANCD', description: '知识感知神经认知诊断' },
    { value: 'kscd', label: 'KSCD', description: '知识结构认知诊断' },
    { value: 'cdmfkc', label: 'CDMFKC', description: '模糊知识概念认知诊断' },
    { value: 'mirt', label: 'MIRT', description: '多维项目反应理论' },
  ];

  const deviceOptions = [
    { value: 'cpu', label: 'CPU' },
    { value: 'cuda:0', label: 'GPU (CUDA:0)' },
    { value: 'cuda:1', label: 'GPU (CUDA:1)' },
  ];

  return (
    <div>
      <Card title="🧪 实验配置">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            batch_size: 256,
            epochs: 10,
            learning_rate: 0.001,
            weight_decay: 0.0,
            latent_dim: 32,
            gcn_layers: 3,
            keep_prob: 1.0,
            ssl_weight: 0.001,
            ssl_temp: 0.5,
            flip_ratio: 0.15,
            test_size: 0.2,
            seed: 42,
            device: 'cuda:0',
            auto_start: false,
          }}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="实验名称"
                name="name"
                rules={[{ required: true, message: '请输入实验名称' }]}
              >
                <Input placeholder="输入实验名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="实验描述" name="description">
                <TextArea rows={3} placeholder="输入实验描述（可选）" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="数据集"
                name="dataset_type"
                rules={[{ required: true, message: '请选择数据集' }]}
              >
                <Select placeholder="选择数据集" loading={loading}>
                  {datasets.map((dataset) => (
                    <Option key={dataset.name} value={dataset.name}>
                      <Space>
                        {dataset.display_name}
                        {dataset.is_demo && (
                          <span style={{ color: '#1890ff' }}>(演示)</span>
                        )}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="模型类型"
                name="model_type"
                rules={[{ required: true, message: '请选择模型类型' }]}
              >
                <Select placeholder="选择模型类型">
                  {modelOptions.map((model) => (
                    <Option key={model.value} value={model.value}>
                      <Tooltip title={model.description}>
                        <Space>
                          {model.label}
                          <InfoCircleOutlined />
                        </Space>
                      </Tooltip>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Alert
            message="数据集说明"
            description="Assist0910数据集将用于实际训练演示，其他数据集仅用于可视化展示。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Collapse defaultActiveKey={['basic']} style={{ marginBottom: 24 }}>
            <Panel header="基础训练参数" key="basic">
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="批次大小" name="batch_size">
                    <InputNumber min={1} max={2048} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="训练轮数" name="epochs">
                    <InputNumber min={1} max={100} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="学习率" name="learning_rate">
                    <InputNumber
                      min={0.0001}
                      max={0.1}
                      step={0.0001}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="权重衰减" name="weight_decay">
                    <InputNumber
                      min={0}
                      max={0.01}
                      step={0.0001}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="测试集比例" name="test_size">
                    <InputNumber
                      min={0.1}
                      max={0.5}
                      step={0.1}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="随机种子" name="seed">
                    <InputNumber min={0} max={9999} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>

            <Panel header="模型参数" key="model">
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="潜在维度" name="latent_dim">
                    <InputNumber min={8} max={128} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="GCN层数" name="gcn_layers">
                    <InputNumber min={1} max={5} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="保持概率" name="keep_prob">
                    <InputNumber
                      min={0.1}
                      max={1.0}
                      step={0.1}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>

            <Panel header="ORCDF特定参数" key="orcdf">
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="SSL权重" name="ssl_weight">
                    <InputNumber
                      min={0.0001}
                      max={0.01}
                      step={0.0001}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="SSL温度" name="ssl_temp">
                    <InputNumber
                      min={0.1}
                      max={5.0}
                      step={0.1}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="翻转比例" name="flip_ratio">
                    <InputNumber
                      min={0.01}
                      max={0.5}
                      step={0.01}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>

            <Panel header="系统配置" key="system">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="计算设备" name="device">
                    <Select>
                      {deviceOptions.map((device) => (
                        <Option key={device.value} value={device.value}>
                          {device.label}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="自动开始训练" name="auto_start" valuePropName="checked">
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>
            </Panel>
          </Collapse>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<PlayCircleOutlined />}
                loading={submitting}
                size="large"
              >
                创建实验
              </Button>
              <Button size="large" onClick={() => form.resetFields()}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ExperimentConfig;
