#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的学习路径规划模拟器演示

展示如何使用完整的模拟器进行算法评估。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
from typing import Dict, Any

# 导入核心模块
from environments.learning_env import LearningEnvironment, AdvancedLearningEnvironment
from agents.rule_based import (
    RandomAgent, GreedyAgent, SmartGreedyAgent, 
    ZPDAgent, ProgressBalancedAgent, MultiCriteriaAgent
)
from evaluation.evaluator import Evaluator
from evaluation.metrics import MetricCalculator
from evaluation.statistics import StatisticalAnalyzer


def basic_demo():
    """基础演示：单个智能体评估"""
    print("🎯 基础演示：单个智能体评估")
    print("=" * 60)
    
    # 1. 创建环境
    env = LearningEnvironment(
        num_knowledge_points=5,
        learning_rate=0.3,
        forgetting_rate=0.02,
        success_threshold=0.6,
        min_success_kps=3,
        max_steps=25
    )
    
    print(f"📚 环境配置:")
    print(f"  知识点数量: {env.num_kps}")
    print(f"  成功标准: {env.min_success_kps}个知识点达到{env.success_threshold}")
    print(f"  最大步数: {env.max_steps}")
    
    # 2. 创建智能体
    agent = SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
    
    # 3. 创建评估器
    evaluator = Evaluator(save_detailed_logs=True, random_seed=42)
    
    # 4. 评估性能
    print(f"\n🧪 开始评估智能体: {agent.name}")
    result = evaluator.evaluate(agent, env, num_episodes=20)
    
    # 5. 显示结果
    print(f"\n📊 评估结果:")
    metrics = result['metrics']
    for metric_name, value in metrics.items():
        print(f"  {metric_name}: {value:.4f}")
    
    # 6. 置信区间
    print(f"\n📈 置信区间:")
    ci_info = result['confidence_intervals']
    for metric, ci in ci_info.items():
        if 'error' not in ci:
            print(f"  {metric}: {ci['mean']:.4f} [{ci['lower_bound']:.4f}, {ci['upper_bound']:.4f}]")
    
    return result


def advanced_demo():
    """高级演示：多智能体比较"""
    print(f"\n🔬 高级演示：多智能体比较")
    print("=" * 60)
    
    # 1. 创建高级环境
    env = AdvancedLearningEnvironment(
        num_knowledge_points=5,
        learning_rate=0.3,
        success_threshold=0.6,
        min_success_kps=3,
        max_steps=30
    )
    
    # 2. 创建多个智能体
    agents = [
        RandomAgent(env.action_space_size),
        GreedyAgent(env.action_space_size),
        SmartGreedyAgent(env.action_space_size, env.dependency_matrix),
        ZPDAgent(env.action_space_size),
        ProgressBalancedAgent(env.action_space_size),
        MultiCriteriaAgent(env.action_space_size)
    ]
    
    print(f"🤖 待比较的智能体:")
    for i, agent in enumerate(agents, 1):
        print(f"  {i}. {agent.name}")
    
    # 3. 创建评估器
    evaluator = Evaluator(
        save_detailed_logs=False,  # 多智能体比较时不保存详细日志
        random_seed=42
    )
    
    # 4. 比较智能体
    print(f"\n🏁 开始比较评估...")
    comparison_result = evaluator.compare_agents(agents, env, num_episodes=30)
    
    # 5. 显示排名
    print(f"\n🏆 智能体性能排名:")
    ranking = comparison_result['ranking']
    for agent_info in ranking:
        name = agent_info['agent_name']
        score = agent_info['composite_score']
        final_score = agent_info['final_score']
        success_rate = agent_info['success_rate']
        print(f"  {agent_info['rank']}. {name}")
        print(f"     综合得分: {score:.4f}")
        print(f"     最终掌握度: {final_score:.4f}")
        print(f"     成功率: {success_rate:.1%}")
    
    # 6. 统计分析结果
    if comparison_result['statistical_comparison']:
        print(f"\n📊 统计分析:")
        print(comparison_result['statistical_comparison']['summary'])
    
    return comparison_result


def cognitive_models_demo():
    """认知模型演示：展示高级认知科学特性"""
    print(f"\n🧠 认知模型演示")
    print("=" * 60)
    
    # 1. 创建高级环境（包含认知模型）
    env = AdvancedLearningEnvironment(
        num_knowledge_points=5,
        learning_rate=0.3,
        max_steps=25
    )
    
    # 2. 创建基于ZPD理论的智能体
    zpd_agent = ZPDAgent(env.action_space_size, optimal_challenge=0.2)
    
    # 3. 运行一个演示回合
    print(f"🎮 运行认知模型演示回合...")
    observation = env.reset()
    zpd_agent.reset()
    
    print(f"初始状态: {[f'{x:.3f}' for x in observation]}")
    
    for step in range(10):
        # 智能体决策
        action = zpd_agent.get_action(observation)
        
        # 获取决策解释
        explanation = zpd_agent.explain_decision(observation, action)
        
        print(f"\n步骤 {step + 1}:")
        print(f"  选择动作: {action}")
        print(f"  决策解释: {explanation['explanation']}")
        
        # 执行动作
        next_observation, reward, done, info = env.step(action)
        
        print(f"  获得奖励: {reward:.3f}")
        print(f"  新状态: {[f'{x:.3f}' for x in next_observation]}")
        
        # 显示高级环境信息
        state_info = env.get_state_info()
        if 'cognitive_load_avg' in state_info:
            print(f"  认知负荷: {state_info['cognitive_load_avg']:.3f}")
            print(f"  动机水平: {state_info['motivation_avg']:.3f}")
            print(f"  心流状态: {state_info['flow_state_avg']:.3f}")
        
        observation = next_observation
        
        if done:
            print(f"\n✅ 任务完成！")
            break
    
    # 4. 显示最终状态信息
    final_info = env.get_state_info()
    print(f"\n📋 最终状态信息:")
    print(f"  成功知识点: {final_info['success_kps']}/{env.min_success_kps}")
    print(f"  完成率: {final_info['completion_rate']:.1%}")
    print(f"  任务状态: {'完成' if final_info['is_completed'] else '未完成'}")


def custom_metrics_demo():
    """自定义指标演示"""
    print(f"\n📊 自定义指标演示")
    print("=" * 60)
    
    from evaluation.metrics import BaseMetric
    
    # 1. 定义自定义指标
    class CustomLearningSpeedMetric(BaseMetric):
        def __init__(self):
            super().__init__("CustomLearningSpeed")
        
        def calculate(self, data):
            if not data:
                return 0.0
            
            # 计算平均每步的掌握度提升
            improvements = []
            for episode in data:
                if 'learning_curve' in episode and episode['learning_curve']:
                    curve = episode['learning_curve']
                    if len(curve) > 1:
                        total_improvement = curve[-1] - curve[0]
                        steps = len(curve)
                        improvements.append(total_improvement / steps)
            
            return np.mean(improvements) if improvements else 0.0
        
        def get_description(self):
            return "自定义学习速度：平均每步的掌握度提升"
    
    # 2. 创建包含自定义指标的计算器
    custom_metric = CustomLearningSpeedMetric()
    metric_calculator = MetricCalculator()
    metric_calculator.add_metric(custom_metric)
    
    # 3. 使用自定义指标评估
    env = LearningEnvironment(num_knowledge_points=5, max_steps=20)
    agent = SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
    
    evaluator = Evaluator(
        metric_calculator=metric_calculator,
        save_detailed_logs=True
    )
    
    result = evaluator.evaluate(agent, env, num_episodes=15)
    
    print(f"📈 评估结果（包含自定义指标）:")
    for metric_name, value in result['metrics'].items():
        print(f"  {metric_name}: {value:.4f}")


def main():
    """主演示函数"""
    print("🎯 学习路径规划模拟器完整演示")
    print("=" * 80)
    
    try:
        # 1. 基础演示
        basic_result = basic_demo()
        
        # 2. 高级演示
        advanced_result = advanced_demo()
        
        # 3. 认知模型演示
        cognitive_models_demo()
        
        # 4. 自定义指标演示
        custom_metrics_demo()
        
        print(f"\n" + "=" * 80)
        print(f"🎉 所有演示完成！")
        
        print(f"\n💡 使用建议:")
        print(f"1. 根据具体需求选择合适的环境和智能体")
        print(f"2. 调整评估参数以获得更稳定的结果")
        print(f"3. 使用统计分析验证算法改进的显著性")
        print(f"4. 利用认知模型提升仿真的真实性")
        print(f"5. 定义自定义指标以满足特定评估需求")
        
        return {
            'basic_result': basic_result,
            'advanced_result': advanced_result
        }
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = main()
