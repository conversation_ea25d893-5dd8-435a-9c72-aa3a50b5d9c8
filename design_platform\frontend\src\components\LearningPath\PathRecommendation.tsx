import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  List,
  Button,
  Tag,
  Progress,
  Space,
  Typography,
  Divider,
  Alert,
  Spin,
  message,
  Tooltip,
  Row,
  Col,
  Modal
} from 'antd';
import {
  BookOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  RightOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import { learningPathService } from '../../services/learningPathService';
import KnowledgePointLearning from '../learning/KnowledgePointLearning';
import { LearningProgress } from '../../types/learning';

const { Title, Text, Paragraph } = Typography;

interface KnowledgePoint {
  knowledge_point_id: string;
  knowledge_point_name: string;
  confidence_score: number;
  difficulty_level: string;
  estimated_duration: number;
  category: string;
  prerequisites_met: boolean;
  sequence_order: number;
  learning_suggestion: string;
}

interface LearningPathData {
  total_duration: number;
  overall_difficulty: string;
  sequence: KnowledgePoint[];
  created_at: string;
}

interface UserProfile {
  overall_ability: number;
  learning_style: string;
  preferred_difficulty: string;
}

interface PathRecommendationProps {
  userId: string;
  diagnosisData: any;
  onPathSelect?: (knowledgePoint: KnowledgePoint) => void;
}

const PathRecommendation: React.FC<PathRecommendationProps> = ({
  userId,
  diagnosisData,
  onPathSelect
}) => {
  const [loading, setLoading] = useState(false);
  const [learningPath, setLearningPath] = useState<LearningPathData | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [learningModalVisible, setLearningModalVisible] = useState(false);
  const [selectedKnowledgePoint, setSelectedKnowledgePoint] = useState<KnowledgePoint | null>(null);

  // 获取学习路径推荐
  const fetchRecommendations = async () => {
    if (!userId || !diagnosisData) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await learningPathService.getRecommendations({
        user_id: userId,
        diagnosis_data: diagnosisData,
        preferences: {
          max_items: 10,
          difficulty_preference: 'medium'
        }
      });

      if (response.success && response.data) {
        setLearningPath(response.data.learning_path);
        setUserProfile(response.data.user_profile);
      } else {
        setError(response.error || '获取推荐失败');
      }
    } catch (err: any) {
      console.error('获取学习路径推荐失败:', err);
      setError(err.message || '网络请求失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecommendations();
  }, [userId, diagnosisData]);

  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'green';
      case 'medium': return 'orange';
      case 'hard': return 'red';
      default: return 'blue';
    }
  };

  // 获取置信度颜色
  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return '#52c41a';
    if (score >= 0.6) return '#faad14';
    return '#ff4d4f';
  };

  // 处理知识点点击
  const handleKnowledgePointClick = (item: KnowledgePoint) => {
    setSelectedKnowledgePoint(item);
    setLearningModalVisible(true);
    if (onPathSelect) {
      onPathSelect(item);
    }
  };

  // 处理学习完成
  const handleLearningComplete = (progress: LearningProgress) => {
    console.log('学习完成:', progress);
    message.success(`恭喜！您已完成"${selectedKnowledgePoint?.knowledge_point_name}"的学习，得分：${progress.score}分`);
    setLearningModalVisible(false);
    setSelectedKnowledgePoint(null);
  };

  // 关闭学习模态框
  const handleLearningModalClose = () => {
    setLearningModalVisible(false);
    setSelectedKnowledgePoint(null);
  };

  // 渲染知识点卡片
  const renderKnowledgePoint = (item: KnowledgePoint) => (
    <List.Item key={item.knowledge_point_id}>
      <Card
        size="small"
        hoverable
        className="knowledge-point-card"
        style={{ width: '100%' }}
        onClick={() => handleKnowledgePointClick(item)}
      >
        <Row gutter={16} align="middle">
          <Col span={1}>
            <div
              style={{
                width: 24,
                height: 24,
                borderRadius: '50%',
                backgroundColor: '#1890ff',
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: 12,
                fontWeight: 'bold'
              }}
            >
              {item.sequence_order}
            </div>
          </Col>
          
          <Col span={12}>
            <Space direction="vertical" size={4}>
              <Text strong>{item.knowledge_point_name}</Text>
              <Space size={8}>
                <Tag color={getDifficultyColor(item.difficulty_level)}>
                  {item.difficulty_level}
                </Tag>
                <Tag color="blue">{item.category}</Tag>
                {!item.prerequisites_met && (
                  <Tag color="red">前置条件不足</Tag>
                )}
              </Space>
            </Space>
          </Col>
          
          <Col span={6}>
            <Space direction="vertical" size={4} style={{ width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <Text type="secondary">置信度:</Text>
                <Progress
                  percent={Math.round(item.confidence_score * 100)}
                  size="small"
                  strokeColor={getConfidenceColor(item.confidence_score)}
                  showInfo={false}
                  style={{ flex: 1 }}
                />
                <Text style={{ color: getConfidenceColor(item.confidence_score) }}>
                  {Math.round(item.confidence_score * 100)}%
                </Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                <ClockCircleOutlined style={{ color: '#666' }} />
                <Text type="secondary">{item.estimated_duration}分钟</Text>
              </div>
            </Space>
          </Col>
          
          <Col span={4} style={{ textAlign: 'right' }}>
            <Space>
              <Tooltip title={item.learning_suggestion}>
                <Button
                  type="text"
                  icon={<InfoCircleOutlined />}
                  size="small"
                />
              </Tooltip>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleKnowledgePointClick(item);
                }}
              >
                开始学习
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>
    </List.Item>
  );

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在生成个性化学习路径...</Text>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="获取学习路径失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchRecommendations}>
              重试
            </Button>
          }
        />
      </Card>
    );
  }

  if (!learningPath) {
    return (
      <Card>
        <Alert
          message="暂无推荐数据"
          description="请先完成认知诊断"
          type="info"
          showIcon
        />
      </Card>
    );
  }

  return (
    <>
    <Card
      title={
        <Space>
          <BookOutlined />
          <span>个性化学习路径推荐</span>
          <Button
            type="text"
            icon={<ReloadOutlined />}
            size="small"
            onClick={fetchRecommendations}
            loading={loading}
          />
        </Space>
      }
      extra={
        <Space>
          <Tag color="blue">
            总时长: {learningPath.total_duration}分钟
          </Tag>
          <Tag color={getDifficultyColor(learningPath.overall_difficulty)}>
            难度: {learningPath.overall_difficulty}
          </Tag>
        </Space>
      }
    >
      {/* 用户画像摘要 */}
      {userProfile && (
        <div style={{ marginBottom: 16 }}>
          <Alert
            message={
              <Space>
                <TrophyOutlined />
                <Text>
                  学习画像: {userProfile.learning_style} | 
                  整体能力: {Math.round(userProfile.overall_ability * 100)}% |
                  偏好难度: {userProfile.preferred_difficulty}
                </Text>
              </Space>
            }
            type="info"
            showIcon={false}
            style={{ border: '1px solid #d9d9d9' }}
          />
        </div>
      )}

      <Divider orientation="left">推荐学习序列</Divider>
      
      <List
        dataSource={learningPath.sequence}
        renderItem={renderKnowledgePoint}
        split={false}
        style={{ maxHeight: 600, overflowY: 'auto' }}
      />

      {learningPath.sequence.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">暂无推荐的学习内容</Text>
        </div>
      )}
    </Card>

    {/* 学习模态框 */}
    <Modal
      title={null}
      open={learningModalVisible}
      onCancel={handleLearningModalClose}
      footer={null}
      width="90%"
      style={{ top: 20 }}
      bodyStyle={{ padding: 0 }}
      destroyOnClose
    >
      {selectedKnowledgePoint && (
        <KnowledgePointLearning
          knowledgePointId={selectedKnowledgePoint.knowledge_point_id}
          knowledgePointName={selectedKnowledgePoint.knowledge_point_name}
          onComplete={handleLearningComplete}
          onBack={handleLearningModalClose}
        />
      )}
    </Modal>
  </>
  );
};

export default PathRecommendation;
