"""
基于规则的智能体实现

包含各种基于规则和启发式的路径规划算法。
"""

import numpy as np
import random
from typing import Dict, List, Any, Optional
from .base_agent import BaseAgent, AdaptiveAgent


class RandomAgent(BaseAgent):
    """随机智能体：完全随机选择动作"""
    
    def __init__(self, action_space_size: int):
        super().__init__(action_space_size, "RandomAgent")
    
    def get_action(self, observation: np.ndarray) -> int:
        return random.randint(0, self.action_space_size - 1)
    
    def get_action_probabilities(self, observation: np.ndarray) -> np.ndarray:
        return np.ones(self.action_space_size) / self.action_space_size
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"随机选择动作 {action}",
            'strategy': 'random_selection'
        }


class GreedyAgent(BaseAgent):
    """贪心智能体：总是选择掌握程度最低的知识点"""
    
    def __init__(self, action_space_size: int):
        super().__init__(action_space_size, "GreedyAgent")
    
    def get_action(self, observation: np.ndarray) -> int:
        return np.argmin(observation)
    
    def get_action_probabilities(self, observation: np.ndarray) -> np.ndarray:
        probs = np.zeros(self.action_space_size)
        probs[np.argmin(observation)] = 1.0
        return probs
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        min_mastery = observation[action]
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"选择掌握程度最低的知识点 {action} (掌握度: {min_mastery:.3f})",
            'strategy': 'greedy_lowest_mastery',
            'mastery_level': min_mastery
        }


class SmartGreedyAgent(BaseAgent):
    """智能贪心智能体：考虑知识点依赖关系的贪心策略"""
    
    def __init__(self, action_space_size: int, dependency_matrix: Optional[np.ndarray] = None):
        super().__init__(action_space_size, "SmartGreedyAgent")
        
        if dependency_matrix is not None:
            self.dependency_matrix = dependency_matrix
        else:
            # 生成默认依赖矩阵
            self.dependency_matrix = self._generate_default_dependencies()
    
    def _generate_default_dependencies(self) -> np.ndarray:
        """生成默认的依赖关系矩阵"""
        matrix = np.zeros((self.action_space_size, self.action_space_size))
        
        # 简单的线性依赖
        for i in range(1, self.action_space_size):
            for j in range(i):
                dependency_strength = 0.3 * np.exp(-(i-j-1) * 0.5)
                matrix[i][j] = dependency_strength
        
        return matrix
    
    def get_action(self, observation: np.ndarray) -> int:
        scores = []
        
        for action in range(self.action_space_size):
            # 基础需求：1 - 当前掌握程度
            need = 1.0 - observation[action]
            
            # 依赖满足度
            dependency_satisfaction = self._calculate_dependency_satisfaction(action, observation)
            
            # 综合得分
            score = need * dependency_satisfaction
            scores.append(score)
        
        return np.argmax(scores)
    
    def _calculate_dependency_satisfaction(self, action: int, observation: np.ndarray) -> float:
        """计算依赖满足度"""
        dependencies = self.dependency_matrix[action]
        satisfaction = 1.0
        
        for i, dep_strength in enumerate(dependencies):
            if dep_strength > 0:
                if observation[i] < 0.5:  # 依赖未满足
                    satisfaction *= (1.0 - dep_strength)
        
        return satisfaction
    
    def get_action_probabilities(self, observation: np.ndarray) -> np.ndarray:
        scores = []
        for action in range(self.action_space_size):
            need = 1.0 - observation[action]
            dependency_satisfaction = self._calculate_dependency_satisfaction(action, observation)
            score = need * dependency_satisfaction
            scores.append(score)
        
        # 转换为概率分布
        scores = np.array(scores)
        if np.sum(scores) > 0:
            return scores / np.sum(scores)
        else:
            return np.ones(self.action_space_size) / self.action_space_size
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        need = 1.0 - observation[action]
        dependency_satisfaction = self._calculate_dependency_satisfaction(action, observation)
        score = need * dependency_satisfaction
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"选择知识点 {action}，学习需求: {need:.3f}，依赖满足度: {dependency_satisfaction:.3f}",
            'strategy': 'smart_greedy_with_dependencies',
            'learning_need': need,
            'dependency_satisfaction': dependency_satisfaction,
            'combined_score': score
        }


class ZPDAgent(BaseAgent):
    """基于最近发展区(ZPD)理论的智能体"""
    
    def __init__(self, action_space_size: int, 
                 optimal_challenge: float = 0.2,
                 zpd_width: float = 0.3):
        super().__init__(action_space_size, "ZPDAgent")
        self.optimal_challenge = optimal_challenge
        self.zpd_width = zpd_width
    
    def get_action(self, observation: np.ndarray) -> int:
        zpd_scores = []
        
        for action in range(self.action_space_size):
            current_mastery = observation[action]
            
            # 计算最优难度
            optimal_difficulty = current_mastery + self.optimal_challenge
            
            # 假设任务难度与当前掌握程度相关
            task_difficulty = current_mastery + 0.3  # 简化假设
            
            # ZPD效应（高斯分布）
            zpd_effect = np.exp(-((task_difficulty - optimal_difficulty) ** 2) / 
                               (2 * self.zpd_width ** 2))
            
            # 结合学习需求
            learning_need = 1.0 - current_mastery
            
            score = zpd_effect * learning_need
            zpd_scores.append(score)
        
        return np.argmax(zpd_scores)
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        current_mastery = observation[action]
        optimal_difficulty = current_mastery + self.optimal_challenge
        task_difficulty = current_mastery + 0.3
        
        zpd_effect = np.exp(-((task_difficulty - optimal_difficulty) ** 2) / 
                           (2 * self.zpd_width ** 2))
        learning_need = 1.0 - current_mastery
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"基于ZPD理论选择知识点 {action}，当前掌握度: {current_mastery:.3f}，ZPD效应: {zpd_effect:.3f}",
            'strategy': 'zpd_based_selection',
            'current_mastery': current_mastery,
            'optimal_difficulty': optimal_difficulty,
            'zpd_effect': zpd_effect,
            'learning_need': learning_need
        }


class ProgressBalancedAgent(BaseAgent):
    """进度平衡智能体：保持各知识点学习进度相对均衡"""
    
    def __init__(self, action_space_size: int, balance_factor: float = 0.5):
        super().__init__(action_space_size, "ProgressBalancedAgent")
        self.balance_factor = balance_factor
    
    def get_action(self, observation: np.ndarray) -> int:
        scores = []
        mean_mastery = np.mean(observation)
        
        for action in range(self.action_space_size):
            current_mastery = observation[action]
            
            # 学习需求
            learning_need = 1.0 - current_mastery
            
            # 平衡需求：偏离平均水平的程度
            balance_need = max(0, mean_mastery - current_mastery)
            
            # 综合得分
            score = (1 - self.balance_factor) * learning_need + self.balance_factor * balance_need
            scores.append(score)
        
        return np.argmax(scores)
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        mean_mastery = np.mean(observation)
        current_mastery = observation[action]
        learning_need = 1.0 - current_mastery
        balance_need = max(0, mean_mastery - current_mastery)
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"平衡策略选择知识点 {action}，学习需求: {learning_need:.3f}，平衡需求: {balance_need:.3f}",
            'strategy': 'progress_balanced',
            'mean_mastery': mean_mastery,
            'learning_need': learning_need,
            'balance_need': balance_need
        }


class AdaptiveGreedyAgent(AdaptiveAgent):
    """自适应贪心智能体：根据性能调整贪心策略的参数"""
    
    def __init__(self, action_space_size: int, 
                 initial_exploration: float = 0.1,
                 adaptation_rate: float = 0.1):
        super().__init__(action_space_size, "AdaptiveGreedyAgent", adaptation_rate)
        self.exploration_rate = initial_exploration
        self.min_exploration = 0.01
        self.max_exploration = 0.5
    
    def get_action(self, observation: np.ndarray) -> int:
        if random.random() < self.exploration_rate:
            # 探索：随机选择
            return random.randint(0, self.action_space_size - 1)
        else:
            # 利用：贪心选择
            return np.argmin(observation)
    
    def _adjust_parameters(self, trend: float):
        """根据性能趋势调整探索率"""
        if trend > 0:  # 性能提升，减少探索
            self.exploration_rate *= (1 - self.adaptation_rate)
        else:  # 性能下降，增加探索
            self.exploration_rate *= (1 + self.adaptation_rate)
        
        # 限制探索率范围
        self.exploration_rate = np.clip(self.exploration_rate, 
                                       self.min_exploration, 
                                       self.max_exploration)
    
    def get_exploration_rate(self) -> float:
        return self.exploration_rate
    
    def set_exploration_rate(self, rate: float):
        self.exploration_rate = np.clip(rate, self.min_exploration, self.max_exploration)
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        is_exploration = random.random() < self.exploration_rate
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"{'探索' if is_exploration else '利用'}策略选择动作 {action}",
            'strategy': 'adaptive_epsilon_greedy',
            'exploration_rate': self.exploration_rate,
            'is_exploration': is_exploration
        }


class MultiCriteriaAgent(BaseAgent):
    """多准则决策智能体：综合考虑多个决策因素"""
    
    def __init__(self, action_space_size: int,
                 criteria_weights: Optional[Dict[str, float]] = None):
        super().__init__(action_space_size, "MultiCriteriaAgent")
        
        # 默认准则权重
        self.criteria_weights = criteria_weights or {
            'learning_need': 0.4,      # 学习需求
            'difficulty_match': 0.3,   # 难度匹配
            'progress_balance': 0.2,   # 进度平衡
            'recent_performance': 0.1  # 最近表现
        }
        
        self.recent_rewards = {}  # 记录各动作的最近奖励
    
    def get_action(self, observation: np.ndarray) -> int:
        scores = []
        mean_mastery = np.mean(observation)
        
        for action in range(self.action_space_size):
            current_mastery = observation[action]
            
            # 1. 学习需求
            learning_need = 1.0 - current_mastery
            
            # 2. 难度匹配（基于ZPD理论）
            optimal_mastery = 0.5  # 假设最优掌握程度
            difficulty_match = 1.0 - abs(current_mastery - optimal_mastery)
            
            # 3. 进度平衡
            progress_balance = max(0, mean_mastery - current_mastery)
            
            # 4. 最近表现
            recent_performance = self.recent_rewards.get(action, 0.5)
            
            # 综合得分
            score = (
                self.criteria_weights['learning_need'] * learning_need +
                self.criteria_weights['difficulty_match'] * difficulty_match +
                self.criteria_weights['progress_balance'] * progress_balance +
                self.criteria_weights['recent_performance'] * recent_performance
            )
            
            scores.append(score)
        
        return np.argmax(scores)
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        super().update(observation, action, reward, next_observation, done)
        
        # 更新最近表现记录
        if action not in self.recent_rewards:
            self.recent_rewards[action] = reward
        else:
            # 指数移动平均
            alpha = 0.3
            self.recent_rewards[action] = (
                alpha * reward + (1 - alpha) * self.recent_rewards[action]
            )
    
    def update_criteria_weights(self, new_weights: Dict[str, float]):
        """更新准则权重"""
        total_weight = sum(new_weights.values())
        if abs(total_weight - 1.0) > 1e-6:
            # 归一化权重
            for key in new_weights:
                new_weights[key] /= total_weight
        
        self.criteria_weights.update(new_weights)
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        current_mastery = observation[action]
        mean_mastery = np.mean(observation)
        
        learning_need = 1.0 - current_mastery
        difficulty_match = 1.0 - abs(current_mastery - 0.5)
        progress_balance = max(0, mean_mastery - current_mastery)
        recent_performance = self.recent_rewards.get(action, 0.5)
        
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"多准则决策选择知识点 {action}",
            'strategy': 'multi_criteria_decision',
            'criteria_scores': {
                'learning_need': learning_need,
                'difficulty_match': difficulty_match,
                'progress_balance': progress_balance,
                'recent_performance': recent_performance
            },
            'criteria_weights': self.criteria_weights
        }
