"""
可视化API端点
"""
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.services.visualization_service import VisualizationService
from app.services.dataset_service import DatasetService
from app.services.experiment_service import ExperimentService

router = APIRouter()


class ModelComparisonRequest(BaseModel):
    """模型对比请求模型"""
    experiment_ids: List[int]


@router.get("/dataset/{dataset_id}/overview")
async def get_dataset_overview(dataset_id: int, db: Session = Depends(get_db)):
    """获取数据集概览可视化数据"""
    dataset_service = DatasetService(db)
    dataset = dataset_service.get_dataset(dataset_id)
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    
    viz_service = VisualizationService()
    try:
        overview_data = viz_service.get_dataset_overview(dataset.data_path)
        return overview_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成数据集概览失败: {str(e)}"
        )


@router.get("/dataset/{dataset_id}/distribution")
async def get_dataset_distribution(
    dataset_id: int, 
    chart_type: str = Query(..., description="图表类型: student_ability, exercise_difficulty, response_count"),
    db: Session = Depends(get_db)
):
    """获取数据集分布可视化数据"""
    dataset_service = DatasetService(db)
    dataset = dataset_service.get_dataset(dataset_id)
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    
    viz_service = VisualizationService()
    try:
        distribution_data = viz_service.get_dataset_distribution(
            dataset.data_path, 
            chart_type
        )
        return distribution_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成分布图表失败: {str(e)}"
        )


@router.get("/dataset/{dataset_id}/q-matrix")
async def get_q_matrix_visualization(dataset_id: int, db: Session = Depends(get_db)):
    """获取Q矩阵可视化数据"""
    dataset_service = DatasetService(db)
    dataset = dataset_service.get_dataset(dataset_id)
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    
    viz_service = VisualizationService()
    try:
        q_matrix_data = viz_service.get_q_matrix_visualization(dataset.data_path)
        return q_matrix_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成Q矩阵可视化失败: {str(e)}"
        )


@router.get("/experiment/{experiment_id}/training-progress")
async def get_training_progress(experiment_id: int, db: Session = Depends(get_db)):
    """获取训练进度可视化数据"""
    experiment_service = ExperimentService(db)
    experiment = experiment_service.get_experiment(experiment_id)
    if not experiment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="实验不存在"
        )
    
    viz_service = VisualizationService()
    try:
        progress_data = viz_service.get_training_progress_chart(experiment)
        return progress_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成训练进度图表失败: {str(e)}"
        )


@router.get("/experiment/{experiment_id}/metrics")
async def get_experiment_metrics(experiment_id: int, db: Session = Depends(get_db)):
    """获取实验指标可视化数据"""
    experiment_service = ExperimentService(db)
    experiment = experiment_service.get_experiment(experiment_id)
    if not experiment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="实验不存在"
        )
    
    viz_service = VisualizationService()
    try:
        metrics_data = viz_service.get_experiment_metrics_chart(experiment)
        return metrics_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成实验指标图表失败: {str(e)}"
        )


@router.get("/experiment/{experiment_id}/oversmoothing")
async def get_oversmoothing_analysis(experiment_id: int, db: Session = Depends(get_db)):
    """获取过度平滑分析可视化数据"""
    experiment_service = ExperimentService(db)
    experiment = experiment_service.get_experiment(experiment_id)
    if not experiment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="实验不存在"
        )
    
    viz_service = VisualizationService()
    try:
        oversmoothing_data = viz_service.get_oversmoothing_analysis(experiment)
        return oversmoothing_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成过度平滑分析失败: {str(e)}"
        )


@router.post("/model-comparison")
async def get_model_comparison(
    request: ModelComparisonRequest,
    db: Session = Depends(get_db)
):
    """获取模型对比可视化数据"""
    experiment_service = ExperimentService(db)

    # 验证所有实验是否存在
    experiments = []
    for exp_id in request.experiment_ids:
        experiment = experiment_service.get_experiment(exp_id)
        if not experiment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"实验 {exp_id} 不存在"
            )
        experiments.append(experiment)

    viz_service = VisualizationService()
    try:
        comparison_data = viz_service.get_model_comparison_chart(experiments)
        return comparison_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成模型对比图表失败: {str(e)}"
        )


@router.get("/dashboard/summary")
async def get_dashboard_summary(db: Session = Depends(get_db)):
    """获取仪表板汇总数据"""
    viz_service = VisualizationService()
    dataset_service = DatasetService(db)
    experiment_service = ExperimentService(db)
    
    try:
        # 获取基础统计数据
        datasets = dataset_service.get_datasets()
        experiments = experiment_service.get_experiments()
        
        summary_data = viz_service.get_dashboard_summary(datasets, experiments)
        return summary_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成仪表板汇总失败: {str(e)}"
        )


@router.get("/dataset-comparison")
async def get_dataset_comparison(
    dataset_ids: List[int] = Query(..., description="要对比的数据集ID列表"),
    db: Session = Depends(get_db)
):
    """获取数据集对比可视化数据"""
    dataset_service = DatasetService(db)
    
    # 验证所有数据集是否存在
    datasets = []
    for dataset_id in dataset_ids:
        dataset = dataset_service.get_dataset(dataset_id)
        if not dataset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"数据集 {dataset_id} 不存在"
            )
        datasets.append(dataset)
    
    viz_service = VisualizationService()
    try:
        comparison_data = viz_service.get_dataset_comparison_chart(datasets)
        return comparison_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成数据集对比图表失败: {str(e)}"
        )


@router.get("/knowledge-tracing/{experiment_id}")
async def get_knowledge_tracing_visualization(
    experiment_id: int,
    student_id: int = Query(..., description="学生ID"),
    db: Session = Depends(get_db)
):
    """获取知识追踪可视化数据"""
    experiment_service = ExperimentService(db)
    experiment = experiment_service.get_experiment(experiment_id)
    if not experiment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="实验不存在"
        )
    
    viz_service = VisualizationService()
    try:
        tracing_data = viz_service.get_knowledge_tracing_visualization(
            experiment, 
            student_id
        )
        return tracing_data
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成知识追踪可视化失败: {str(e)}"
        )
