import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  Button,
  Space,
  Tabs,
  Table,
  Tag,
  Statistic,
  Alert,
  Spin,
  message,
} from 'antd';
import {
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined,
  Pie<PERSON><PERSON>Outlined,
  Radar<PERSON>hartOutlined,
  CompressOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import ReactECharts from 'echarts-for-react';
import { experimentService, visualizationService } from '../../services/api';

const { Option } = Select;
const { TabPane } = Tabs;

interface Experiment {
  id: number;
  name: string;
  model_type: string;
  dataset_type: string;
  status: string;
  metrics?: Record<string, number>;
  completed_at?: string;
}

const ResultAnalysis: React.FC = () => {
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const [selectedExperiments, setSelectedExperiments] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadCompletedExperiments();
  }, []);

  const loadCompletedExperiments = async () => {
    setLoading(true);
    try {
      const response = await experimentService.getExperiments({
        status_filter: 'completed',
      });
      setExperiments(response.data);
    } catch (error) {
      message.error('加载实验数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExperimentSelect = (experimentIds: number[]) => {
    setSelectedExperiments(experimentIds);
  };

  const handleGenerateComparison = async () => {
    if (selectedExperiments.length === 0) {
      message.warning('请选择要对比的实验');
      return;
    }

    setLoading(true);
    try {
      const response = await visualizationService.getModelComparison(selectedExperiments);
      setChartData(response.data);
      setActiveTab('comparison');
    } catch (error) {
      message.error('生成对比图表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewTrainingProgress = async (experimentId: number) => {
    setLoading(true);
    try {
      const response = await visualizationService.getExperimentChartData(
        experimentId,
        'training-progress'
      );
      setChartData(response.data);
      setActiveTab('progress');
    } catch (error) {
      message.error('获取训练进度失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewOversmoothingAnalysis = async (experimentId: number) => {
    setLoading(true);
    try {
      const response = await visualizationService.getOversmoothingAnalysis(experimentId);
      setChartData(response.data);
      setActiveTab('oversmoothing');
    } catch (error) {
      message.error('获取过度平滑分析失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      running: { color: 'processing', text: '运行中' },
      pending: { color: 'default', text: '等待中' },
    };
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ColumnsType<Experiment> = [
    {
      title: '实验名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Experiment) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.model_type.toUpperCase()} on {record.dataset_type}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: 'AUC',
      key: 'auc',
      render: (_, record: Experiment) => (
        <span>{record.metrics?.auc?.toFixed(4) || 'N/A'}</span>
      ),
    },
    {
      title: 'ACC',
      key: 'accuracy',
      render: (_, record: Experiment) => (
        <span>{record.metrics?.accuracy ? `${(record.metrics.accuracy * 100).toFixed(1)}%` : 'N/A'}</span>
      ),
    },
    {
      title: 'RMSE',
      key: 'rmse',
      render: (_, record: Experiment) => (
        <span>{record.metrics?.rmse?.toFixed(4) || 'N/A'}</span>
      ),
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (time: string) => time ? new Date(time).toLocaleString() : 'N/A',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Experiment) => (
        <Space>
          <Button
            type="link"
            icon={<LineChartOutlined />}
            onClick={() => handleViewTrainingProgress(record.id)}
          >
            训练曲线
          </Button>
          <Button
            type="link"
            icon={<BarChartOutlined />}
            onClick={() => handleViewOversmoothingAnalysis(record.id)}
          >
            过度平滑分析
          </Button>
        </Space>
      ),
    },
  ];

  const renderChart = (data: any) => {
    if (!data) return null;

    // 如果数据已经是ECharts格式，直接使用
    if (data.title || data.xAxis || data.series) {
      return <ReactECharts option={data} style={{ height: '400px' }} />;
    }

    // 否则按照原来的格式处理
    const option = {
      title: {
        text: data.options?.title || '图表',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: data.options?.legend?.data || [],
        bottom: 0,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.data?.categories || [],
        name: data.options?.xAxis?.name || '',
      },
      yAxis: data.options?.yAxis || {
        type: 'value',
        name: data.options?.yAxis?.name || '',
      },
      series: data.data?.series || [],
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  const renderOversmoothingAnalysis = (data: any) => {
    if (!data) return null;

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="改善倍数"
                value={data.improvement_ratio}
                suffix="倍"
                precision={1}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={16}>
            <Alert
              message="分析结论"
              description={data.analysis?.conclusion}
              type="success"
              showIcon
            />
          </Col>
        </Row>

        <Card title="MND值对比">
          {renderChart(data.mnd_comparison)}
        </Card>

        <Card title="分析说明" style={{ marginTop: 16 }}>
          <p>{data.analysis?.description}</p>
        </Card>
      </div>
    );
  };

  return (
    <div>
      <Card
        title="结果分析"
        extra={
          <Space>
            <Select
              mode="multiple"
              placeholder="选择要对比的实验"
              style={{ width: 300 }}
              value={selectedExperiments}
              onChange={handleExperimentSelect}
            >
              {experiments.map((exp) => (
                <Option key={exp.id} value={exp.id}>
                  {exp.name} ({exp.model_type.toUpperCase()})
                </Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<CompressOutlined />}
              onClick={handleGenerateComparison}
              disabled={selectedExperiments.length === 0}
            >
              生成对比
            </Button>
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="实验概览" key="overview">
            <Table
              columns={columns}
              dataSource={experiments}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个实验`,
              }}
            />
          </TabPane>

          <TabPane tab="模型对比" key="comparison">
            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
              </div>
            ) : chartData ? (
              <Card title="模型性能对比">
                {renderChart(chartData)}
              </Card>
            ) : (
              <Alert
                message="请选择实验并生成对比图表"
                type="info"
                showIcon
              />
            )}
          </TabPane>

          <TabPane tab="训练进度" key="progress">
            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
              </div>
            ) : chartData ? (
              <Card title="训练进度曲线">
                {renderChart(chartData)}
              </Card>
            ) : (
              <Alert
                message="请选择实验查看训练进度"
                type="info"
                showIcon
              />
            )}
          </TabPane>

          <TabPane tab="过度平滑分析" key="oversmoothing">
            {loading ? (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
              </div>
            ) : chartData ? (
              renderOversmoothingAnalysis(chartData)
            ) : (
              <Alert
                message="请选择实验查看过度平滑分析"
                type="info"
                showIcon
              />
            )}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ResultAnalysis;
