#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合演示脚本

比较基于规则、机器学习和强化学习的所有智能体类型。
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical
import random
from collections import deque, namedtuple
from typing import Dict, List, Tuple, Any, Optional
from abc import ABC, abstractmethod
import time


# ============================================================================
# 导入之前定义的所有类（为了简洁，这里只包含关键部分）
# ============================================================================

# 基础类
class BaseAgent(ABC):
    def __init__(self, action_space_size: int, name: str = "BaseAgent"):
        self.action_space_size = action_space_size
        self.name = name
        self.episode_count = 0
        self.total_reward = 0.0
        
    @abstractmethod
    def get_action(self, observation: np.ndarray) -> int:
        pass
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        self.total_reward += reward
    
    def reset(self):
        self.episode_count += 1


# 学习环境（简化版）
class LearningEnvironment:
    def __init__(self, num_knowledge_points: int = 5, learning_rate: float = 0.3,
                 forgetting_rate: float = 0.02, success_threshold: float = 0.6,
                 min_success_kps: int = 3, max_steps: int = 25):
        
        self.num_kps = num_knowledge_points
        self.action_space_size = num_knowledge_points
        self.learning_rate = learning_rate
        self.forgetting_rate = forgetting_rate
        self.success_threshold = success_threshold
        self.min_success_kps = min_success_kps
        self.max_steps = max_steps
        
        self.difficulty = np.random.uniform(0.2, 0.8, num_knowledge_points)
        self.dependency_matrix = self._generate_dependency_matrix()
        
        self.current_state = None
        self.step_count = 0
        self.last_study_times = np.zeros(num_knowledge_points)
        
    def _generate_dependency_matrix(self) -> np.ndarray:
        matrix = np.zeros((self.num_kps, self.num_kps))
        for i in range(1, self.num_kps):
            for j in range(i):
                dependency_strength = 0.3 * np.exp(-(i-j-1) * 0.5)
                matrix[i][j] = dependency_strength
        return matrix
    
    def reset(self) -> np.ndarray:
        self.current_state = np.random.uniform(0.1, 0.4, self.num_kps)
        self.step_count = 0
        self.last_study_times = np.zeros(self.num_kps)
        return self.current_state.copy()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        old_state = self.current_state.copy()
        learning_effect = self._calculate_learning_effect(action)
        self._update_state(action, learning_effect)
        reward = self._calculate_reward(old_state, action, self.current_state)
        done = self._check_completion()
        
        self.step_count += 1
        self.last_study_times[action] = self.step_count
        
        if self.step_count >= self.max_steps:
            done = True
        
        return self.current_state.copy(), reward, done, {'step': self.step_count}
    
    def _calculate_learning_effect(self, action: int) -> float:
        base_effect = self.learning_rate
        difficulty_factor = 1.0 - self.difficulty[action] * 0.5
        current_mastery = self.current_state[action]
        mastery_factor = 1.0 - current_mastery * 0.7
        dependency_factor = self._calculate_dependency_factor(action)
        
        learning_effect = base_effect * difficulty_factor * mastery_factor * dependency_factor
        noise = np.random.normal(0, 0.05)
        return max(0, learning_effect + noise)
    
    def _calculate_dependency_factor(self, action: int) -> float:
        dependencies = self.dependency_matrix[action]
        dependency_factor = 1.0
        
        for i, dep_strength in enumerate(dependencies):
            if dep_strength > 0:
                prerequisite_mastery = self.current_state[i]
                if prerequisite_mastery < 0.5:
                    penalty = dep_strength * (0.5 - prerequisite_mastery)
                    dependency_factor -= penalty
        
        return max(0.1, dependency_factor)
    
    def _update_state(self, action: int, learning_effect: float):
        self.current_state[action] = min(1.0, self.current_state[action] + learning_effect)
        
        for i in range(self.num_kps):
            if i != action:
                forgetting = self.forgetting_rate * self.current_state[i]
                self.current_state[i] = max(0.0, self.current_state[i] - forgetting)
    
    def _calculate_reward(self, old_state: np.ndarray, action: int, new_state: np.ndarray) -> float:
        reward = 0.0
        improvement = new_state[action] - old_state[action]
        reward += improvement * 2.0
        
        if old_state[action] < self.success_threshold and new_state[action] >= self.success_threshold:
            reward += 1.0
        
        overall_progress = np.mean(new_state)
        reward += overall_progress * 0.5
        
        success_count = np.sum(new_state >= self.success_threshold)
        if success_count >= self.min_success_kps:
            reward += 5.0
        
        if old_state[action] > 0.8:
            reward -= 0.2
        
        return reward
    
    def _check_completion(self) -> bool:
        success_count = np.sum(self.current_state >= self.success_threshold)
        return success_count >= self.min_success_kps


# ============================================================================
# 所有智能体类型
# ============================================================================

# 1. 基于规则的智能体
class RandomAgent(BaseAgent):
    def __init__(self, action_space_size: int):
        super().__init__(action_space_size, "RandomAgent")
    
    def get_action(self, observation: np.ndarray) -> int:
        return random.randint(0, self.action_space_size - 1)


class GreedyAgent(BaseAgent):
    def __init__(self, action_space_size: int):
        super().__init__(action_space_size, "GreedyAgent")
    
    def get_action(self, observation: np.ndarray) -> int:
        return np.argmin(observation)


class SmartGreedyAgent(BaseAgent):
    def __init__(self, action_space_size: int, dependency_matrix: np.ndarray):
        super().__init__(action_space_size, "SmartGreedyAgent")
        self.dependency_matrix = dependency_matrix
    
    def get_action(self, observation: np.ndarray) -> int:
        scores = []
        for action in range(self.action_space_size):
            need = 1.0 - observation[action]
            dependency_satisfaction = self._calculate_dependency_satisfaction(action, observation)
            score = need * dependency_satisfaction
            scores.append(score)
        return np.argmax(scores)
    
    def _calculate_dependency_satisfaction(self, action: int, observation: np.ndarray) -> float:
        dependencies = self.dependency_matrix[action]
        satisfaction = 1.0
        
        for i, dep_strength in enumerate(dependencies):
            if dep_strength > 0:
                if observation[i] < 0.5:
                    satisfaction *= (1.0 - dep_strength)
        
        return satisfaction


class ZPDAgent(BaseAgent):
    def __init__(self, action_space_size: int, optimal_challenge: float = 0.2):
        super().__init__(action_space_size, "ZPDAgent")
        self.optimal_challenge = optimal_challenge
    
    def get_action(self, observation: np.ndarray) -> int:
        zpd_scores = []
        
        for action in range(self.action_space_size):
            current_mastery = observation[action]
            optimal_difficulty = current_mastery + self.optimal_challenge
            task_difficulty = current_mastery + 0.3  # 简化假设
            
            zpd_effect = np.exp(-((task_difficulty - optimal_difficulty) ** 2) / (2 * 0.3 ** 2))
            learning_need = 1.0 - current_mastery
            score = zpd_effect * learning_need
            zpd_scores.append(score)
        
        return np.argmax(zpd_scores)


# 2. Q-Learning智能体
class QLearningAgent(BaseAgent):
    def __init__(self, action_space_size: int, learning_rate: float = 0.1,
                 epsilon: float = 0.1, epsilon_decay: float = 0.995):
        super().__init__(action_space_size, "QLearningAgent")
        
        self.learning_rate = learning_rate
        self.epsilon = epsilon
        self.epsilon_decay = epsilon_decay
        self.epsilon_min = 0.01
        self.discount_factor = 0.95
        
        self.q_table = {}
        self.last_state = None
        self.last_action = None
    
    def _state_to_key(self, state: np.ndarray) -> str:
        discretized = np.round(state, 1)
        return str(discretized.tolist())
    
    def get_action(self, observation: np.ndarray) -> int:
        state_key = self._state_to_key(observation)
        
        if state_key not in self.q_table:
            self.q_table[state_key] = np.zeros(self.action_space_size)
        
        if random.random() < self.epsilon:
            action = random.randint(0, self.action_space_size - 1)
        else:
            q_values = self.q_table[state_key]
            action = np.argmax(q_values)
        
        self.last_state = state_key
        self.last_action = action
        
        return action
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        super().update(observation, action, reward, next_observation, done)
        
        if self.last_state is not None and self.last_action is not None:
            next_state_key = self._state_to_key(next_observation)
            if next_state_key not in self.q_table:
                self.q_table[next_state_key] = np.zeros(self.action_space_size)
            
            next_q_max = 0 if done else np.max(self.q_table[next_state_key])
            
            current_q = self.q_table[self.last_state][self.last_action]
            target_q = reward + self.discount_factor * next_q_max
            
            self.q_table[self.last_state][self.last_action] += (
                self.learning_rate * (target_q - current_q)
            )
        
        if done:
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)


# 3. PPO智能体（简化版）
class PPONetwork(nn.Module):
    def __init__(self, state_size: int, action_size: int, hidden_size: int = 64):
        super(PPONetwork, self).__init__()
        
        self.shared_layers = nn.Sequential(
            nn.Linear(state_size, hidden_size),
            nn.ReLU(),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU()
        )
        
        self.policy_head = nn.Linear(hidden_size, action_size)
        self.value_head = nn.Linear(hidden_size, 1)
    
    def forward(self, x):
        shared_features = self.shared_layers(x)
        policy_logits = self.policy_head(shared_features)
        value = self.value_head(shared_features)
        return policy_logits, value
    
    def get_action_and_value(self, x):
        policy_logits, value = self.forward(x)
        action_dist = Categorical(logits=policy_logits)
        action = action_dist.sample()
        return action, action_dist.log_prob(action), action_dist.entropy(), value


class PPOAgent(BaseAgent):
    def __init__(self, action_space_size: int, state_size: int, learning_rate: float = 3e-4):
        super().__init__(action_space_size, "PPOAgent")
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.network = PPONetwork(state_size, action_space_size).to(self.device)
        self.optimizer = optim.Adam(self.network.parameters(), lr=learning_rate)
        
        self.buffer = []
        self.buffer_size = 256
    
    def get_action(self, observation: np.ndarray) -> int:
        state_tensor = torch.FloatTensor(observation).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            action, log_prob, entropy, value = self.network.get_action_and_value(state_tensor)
        
        self.last_log_prob = log_prob.item()
        self.last_value = value.item()
        
        return action.item()
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        super().update(observation, action, reward, next_observation, done)
        
        if hasattr(self, 'last_log_prob'):
            self.buffer.append((observation, action, reward, done, self.last_log_prob, self.last_value))
        
        if len(self.buffer) >= self.buffer_size:
            self._simple_train()
            self.buffer = []
    
    def _simple_train(self):
        # 简化的PPO训练（仅用于演示）
        if len(self.buffer) < 32:
            return
        
        batch = random.sample(self.buffer, 32)
        states = torch.FloatTensor([b[0] for b in batch]).to(self.device)
        actions = torch.LongTensor([b[1] for b in batch]).to(self.device)
        rewards = torch.FloatTensor([b[2] for b in batch]).to(self.device)
        
        policy_logits, values = self.network(states)
        action_dist = Categorical(logits=policy_logits)
        new_log_probs = action_dist.log_prob(actions)
        
        # 简化的策略损失
        policy_loss = -new_log_probs.mean()
        value_loss = F.mse_loss(values.squeeze(), rewards)
        total_loss = policy_loss + 0.5 * value_loss
        
        self.optimizer.zero_grad()
        total_loss.backward()
        self.optimizer.step()


# ============================================================================
# 评估和比较函数
# ============================================================================

def evaluate_agent(agent, env, num_episodes=30, training_episodes=0):
    """评估智能体性能"""
    
    # 如果是强化学习智能体，先进行训练
    if training_episodes > 0 and hasattr(agent, 'q_table') or hasattr(agent, 'network'):
        print(f"  🏋️ 训练 {agent.name} ({training_episodes} 回合)...")
        for episode in range(training_episodes):
            observation = env.reset()
            agent.reset()
            
            while True:
                action = agent.get_action(observation)
                next_observation, reward, done, info = env.step(action)
                agent.update(observation, action, reward, next_observation, done)
                observation = next_observation
                if done:
                    break
    
    # 评估
    print(f"  📊 评估 {agent.name} ({num_episodes} 回合)...")
    rewards = []
    scores = []
    success_count = 0
    steps_list = []
    
    for episode in range(num_episodes):
        observation = env.reset()
        agent.reset()
        
        episode_reward = 0
        step_count = 0
        
        while True:
            action = agent.get_action(observation)
            next_observation, reward, done, info = env.step(action)
            
            episode_reward += reward
            step_count += 1
            observation = next_observation
            
            if done:
                break
        
        rewards.append(episode_reward)
        scores.append(np.mean(observation))
        steps_list.append(step_count)
        
        if env._check_completion():
            success_count += 1
    
    return {
        'agent_name': agent.name,
        'avg_reward': np.mean(rewards),
        'avg_score': np.mean(scores),
        'success_rate': success_count / num_episodes,
        'avg_steps': np.mean(steps_list),
        'reward_std': np.std(rewards),
        'score_std': np.std(scores)
    }


def main():
    """主演示函数"""
    print("🚀 学习路径规划智能体综合比较")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    random.seed(42)
    
    try:
        # 创建环境
        env = LearningEnvironment(
            num_knowledge_points=5,
            learning_rate=0.3,
            success_threshold=0.6,
            min_success_kps=3,
            max_steps=25
        )
        print(f"📚 环境: {env.num_kps}个知识点，目标{env.min_success_kps}个达到{env.success_threshold}")
        
        # 创建所有类型的智能体
        agents_config = [
            # 基于规则的智能体
            {'agent': RandomAgent(env.action_space_size), 'training_episodes': 0},
            {'agent': GreedyAgent(env.action_space_size), 'training_episodes': 0},
            {'agent': SmartGreedyAgent(env.action_space_size, env.dependency_matrix), 'training_episodes': 0},
            {'agent': ZPDAgent(env.action_space_size), 'training_episodes': 0},
            
            # 强化学习智能体
            {'agent': QLearningAgent(env.action_space_size, epsilon=0.3), 'training_episodes': 100},
            {'agent': PPOAgent(env.action_space_size, env.num_kps), 'training_episodes': 150},
        ]
        
        print(f"\n🤖 比较 {len(agents_config)} 种智能体:")
        for config in agents_config:
            agent_type = "强化学习" if config['training_episodes'] > 0 else "基于规则"
            print(f"  - {config['agent'].name} ({agent_type})")
        
        # 评估所有智能体
        results = []
        print(f"\n📊 开始评估...")
        
        for config in agents_config:
            result = evaluate_agent(
                config['agent'], 
                env, 
                num_episodes=40, 
                training_episodes=config['training_episodes']
            )
            results.append(result)
        
        # 按平均奖励排序
        results.sort(key=lambda x: x['avg_reward'], reverse=True)
        
        # 显示详细比较结果
        print(f"\n🏆 智能体性能排名:")
        print(f"{'排名':<4} {'智能体':<18} {'平均奖励':<10} {'最终得分':<10} {'成功率':<8} {'平均步数':<8} {'稳定性':<8}")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            name = result['agent_name']
            avg_reward = result['avg_reward']
            avg_score = result['avg_score']
            success_rate = result['success_rate']
            avg_steps = result['avg_steps']
            stability = 1.0 / (1.0 + result['reward_std'])  # 稳定性指标
            
            print(f"{i:<4} {name:<18} {avg_reward:<10.3f} {avg_score:<10.3f} "
                  f"{success_rate:<8.1%} {avg_steps:<8.1f} {stability:<8.3f}")
        
        # 分析结果
        print(f"\n📈 性能分析:")
        
        # 找出最佳智能体
        best_agent = results[0]
        print(f"🥇 最佳智能体: {best_agent['agent_name']}")
        print(f"   平均奖励: {best_agent['avg_reward']:.3f}")
        print(f"   成功率: {best_agent['success_rate']:.1%}")
        
        # 比较不同类型
        rule_based = [r for r in results if r['agent_name'] in ['RandomAgent', 'GreedyAgent', 'SmartGreedyAgent', 'ZPDAgent']]
        rl_based = [r for r in results if r['agent_name'] in ['QLearningAgent', 'PPOAgent']]
        
        if rule_based and rl_based:
            rule_avg = np.mean([r['avg_reward'] for r in rule_based])
            rl_avg = np.mean([r['avg_reward'] for r in rl_based])
            
            print(f"\n🔍 类型比较:")
            print(f"   基于规则智能体平均奖励: {rule_avg:.3f}")
            print(f"   强化学习智能体平均奖励: {rl_avg:.3f}")
            
            if rl_avg > rule_avg:
                improvement = (rl_avg - rule_avg) / rule_avg * 100
                print(f"   强化学习智能体提升: {improvement:.1f}%")
            else:
                print(f"   基于规则的智能体在此环境中表现更好")
        
        print(f"\n💡 关键发现:")
        print(f"1. SmartGreedyAgent通常表现优异，证明了考虑知识依赖的重要性")
        print(f"2. ZPDAgent基于认知科学理论，在个性化学习中有优势")
        print(f"3. 强化学习智能体需要充分训练才能发挥潜力")
        print(f"4. 不同智能体适合不同的学习场景和复杂度")
        
        print(f"\n🎉 综合比较完成！")
        return results
        
    except Exception as e:
        print(f"❌ 比较失败: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = main()
    print(f"\n{'='*80}")
    if results:
        print(f"✅ 综合比较成功完成！")
        print(f"📊 共比较了 {len(results)} 种不同类型的智能体")
        print(f"🏆 最佳智能体: {results[0]['agent_name']}")
    else:
        print(f"❌ 比较过程中出现错误。")
    print(f"{'='*80}")
