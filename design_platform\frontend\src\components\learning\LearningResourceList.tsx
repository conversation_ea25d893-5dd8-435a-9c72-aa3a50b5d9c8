import React from 'react';
import { <PERSON>, List, Button, Tag, Space } from 'antd';
import { 
  PlayCircleOutlined, 
  FileTextOutlined, 
  InteractionOutlined,
  DownloadOutlined,
  EyeOutlined 
} from '@ant-design/icons';

interface LearningResource {
  id: number;
  title: string;
  type: 'video' | 'document' | 'interactive' | 'link';
  url: string;
  duration?: string;
  description: string;
}

interface LearningResourceListProps {
  resources: LearningResource[];
}

const LearningResourceList: React.FC<LearningResourceListProps> = ({ resources }) => {
  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
      case 'document':
        return <FileTextOutlined style={{ color: '#52c41a' }} />;
      case 'interactive':
        return <InteractionOutlined style={{ color: '#722ed1' }} />;
      case 'link':
        return <FileTextOutlined style={{ color: '#fa8c16' }} />;
      default:
        return <FileTextOutlined />;
    }
  };

  const getResourceTypeText = (type: string) => {
    switch (type) {
      case 'video':
        return '视频';
      case 'document':
        return '文档';
      case 'interactive':
        return '交互';
      case 'link':
        return '链接';
      default:
        return '其他';
    }
  };

  const getResourceTypeColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'blue';
      case 'document':
        return 'green';
      case 'interactive':
        return 'purple';
      default:
        return 'default';
    }
  };

  const handleResourceClick = (resource: LearningResource) => {
    // 这里可以实现实际的资源访问逻辑
    console.log('访问资源:', resource);
    // 模拟打开资源
    if (resource.url !== '#') {
      window.open(resource.url, '_blank');
    } else {
      // 模拟资源访问
      alert(`正在打开资源: ${resource.title}`);
    }
  };

  return (
    <div style={{ maxWidth: 900 }}>
      <List
        grid={{ gutter: [24, 24], xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 2 }}
        dataSource={resources}
        renderItem={(resource) => (
          <List.Item>
            <Card
              hoverable
              style={{
                height: '100%',
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                border: '1px solid #e8e8e8',
                overflow: 'hidden'
              }}
              bodyStyle={{ padding: 0 }}
            >
              {/* 资源头部 */}
              <div style={{
                background: resource.type === 'video' ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' :
                           resource.type === 'document' ? 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' :
                           'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                padding: '20px',
                color: 'white'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                  <div style={{ fontSize: '24px', marginRight: '12px' }}>
                    {getResourceIcon(resource.type)}
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '4px' }}>
                      {resource.title}
                    </div>
                    <Tag
                      color={getResourceTypeColor(resource.type)}
                      style={{ margin: 0, fontSize: '12px' }}
                    >
                      {getResourceTypeText(resource.type)}
                    </Tag>
                  </div>
                </div>
                {resource.duration && (
                  <div style={{ fontSize: '14px', opacity: 0.9 }}>
                    时长: {resource.duration}
                  </div>
                )}
              </div>

              {/* 资源内容 */}
              <div style={{ padding: '20px' }}>
                <div style={{
                  color: '#666',
                  fontSize: '14px',
                  lineHeight: '1.6',
                  marginBottom: '20px',
                  minHeight: '60px'
                }}>
                  {resource.description}
                </div>

                {/* 操作按钮 */}
                <div style={{ display: 'flex', gap: '12px' }}>
                  <Button
                    type="primary"
                    icon={<EyeOutlined />}
                    onClick={() => handleResourceClick(resource)}
                    style={{
                      flex: 1,
                      height: '40px',
                      borderRadius: '20px'
                    }}
                  >
                    查看
                  </Button>
                  <Button
                    icon={<DownloadOutlined />}
                    onClick={() => handleResourceClick(resource)}
                    style={{
                      flex: 1,
                      height: '40px',
                      borderRadius: '20px'
                    }}
                  >
                    下载
                  </Button>
                </div>
              </div>
            </Card>
          </List.Item>
        )}
      />
      
      {resources.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
          暂无学习资源
        </div>
      )}
    </div>
  );
};

export default LearningResourceList;
