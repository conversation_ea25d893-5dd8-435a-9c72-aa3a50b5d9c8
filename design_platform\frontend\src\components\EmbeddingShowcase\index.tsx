import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Typography, Tag, Button, Space, Divider, Alert } from 'antd';
import { ThunderboltOutlined, BulbOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Title, Paragraph, Text } = Typography;

// 动态API配置 - 支持内网穿透
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // 检测当前访问域名
  const currentHost = window.location.hostname;

  // 如果是内网穿透域名，使用相对路径或提示用户配置
  if (currentHost.includes('ngrok') || currentHost.includes('cpolar') || currentHost.includes('frp')) {
    // 内网穿透环境，需要用户手动配置后端地址
    const backendUrl = localStorage.getItem('BACKEND_URL');
    if (backendUrl) {
      return `${backendUrl}/api/v1`;
    }

    // 如果没有配置，提示用户配置
    console.warn('🌐 检测到内网穿透环境，请配置后端地址');
    return 'http://localhost:8000/api/v1'; // 默认值
  }

  // 本地开发环境
  return 'http://localhost:8000/api/v1';
};

interface EmbeddingExample {
  original: string;
  embedding_description: string;
  semantic_features: string[];
}

interface EmbeddingData {
  text_examples: EmbeddingExample[];
  embedding_visualization: {
    dimensions: number;
    clustering_info: string;
    applications: string[];
  };
  data_source?: string;
  experiment_count?: number;
}

const EmbeddingShowcase: React.FC = () => {
  const [embeddingData, setEmbeddingData] = useState<EmbeddingData | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadEmbeddingExamples();
  }, []);

  const loadEmbeddingExamples = async () => {
    setLoading(true);
    try {
      const apiBaseUrl = getApiBaseUrl();
      const response = await fetch(`${apiBaseUrl}/embedding/examples`);
      const data = await response.json();
      setEmbeddingData(data);
    } catch (error) {
      console.error('加载Embedding示例失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟的embedding向量可视化 - 优化版
  const getEmbeddingVisualization = () => {
    // 生成更真实的聚类数据
    const generateClusterData = (centerX: number, centerY: number, count: number, spread: number) => {
      return Array.from({ length: count }, () => {
        const angle = Math.random() * 2 * Math.PI;
        const radius = Math.random() * spread;
        return [
          centerX + radius * Math.cos(angle) + (Math.random() - 0.5) * 0.3,
          centerY + radius * Math.sin(angle) + (Math.random() - 0.5) * 0.3
        ];
      });
    };

    // 模拟不同认知状态的embedding聚类
    const clusters = [
      {
        name: '🎯 优秀学生',
        data: generateClusterData(-2, 2, 18, 0.8),
        color: '#52c41a',
        description: '知识掌握全面，学习能力强'
      },
      {
        name: '📈 中等学生',
        data: generateClusterData(0, 0, 25, 1.2),
        color: '#1890ff',
        description: '基础扎实，有提升潜力'
      },
      {
        name: '💪 待提升学生',
        data: generateClusterData(2.5, -1.5, 22, 1.0),
        color: '#faad14',
        description: '需要针对性辅导'
      },
      {
        name: '🔍 特殊情况',
        data: generateClusterData(-1, -2, 8, 0.5),
        color: '#722ed1',
        description: '学习模式独特，需个别关注'
      }
    ];

    const option = {
      backgroundColor: '#ffffff',
      title: {
        text: 'Embedding空间聚类可视化',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#262626'
        },
        subtext: '基于ORCDF模型的学生认知状态分布',
        subtextStyle: {
          color: '#8c8c8c',
          fontSize: 12
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#1890ff',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        },
        formatter: (params: any) => {
          const cluster = clusters.find(c => c.name === params.seriesName);
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.seriesName}</div>
              <div style="color: #bfbfbf; font-size: 12px; margin-bottom: 4px;">${cluster?.description || ''}</div>
              <div style="color: #d9d9d9; font-size: 11px;">
                坐标: (${params.data[0].toFixed(2)}, ${params.data[1].toFixed(2)})
              </div>
            </div>
          `;
        }
      },
      legend: {
        data: clusters.map(c => c.name),
        top: 50,
        textStyle: {
          fontSize: 12,
          color: '#595959'
        },
        itemGap: 20
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '25%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        name: '认知能力维度',
        nameLocation: 'middle',
        nameGap: 30,
        nameTextStyle: {
          fontSize: 12,
          color: '#595959'
        },
        axisLine: {
          lineStyle: {
            color: '#d9d9d9'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#d9d9d9'
          }
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: '学习表现维度',
        nameLocation: 'middle',
        nameGap: 40,
        nameTextStyle: {
          fontSize: 12,
          color: '#595959'
        },
        axisLine: {
          lineStyle: {
            color: '#d9d9d9'
          }
        },
        axisTick: {
          lineStyle: {
            color: '#d9d9d9'
          }
        },
        axisLabel: {
          color: '#8c8c8c',
          fontSize: 11
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      animationDuration: 1000,
      animationEasing: 'cubicOut',
      series: clusters.map((cluster, index) => ({
        name: cluster.name,
        type: 'scatter',
        data: cluster.data,
        symbolSize: (value: number[]) => {
          // 根据距离中心的远近调整点的大小
          const distance = Math.sqrt(value[0] * value[0] + value[1] * value[1]);
          return Math.max(6, 12 - distance * 2);
        },
        itemStyle: {
          color: cluster.color,
          opacity: 0.8,
          borderColor: '#fff',
          borderWidth: 1,
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.1)'
        },
        emphasis: {
          itemStyle: {
            opacity: 1,
            shadowBlur: 8,
            shadowColor: cluster.color,
            borderWidth: 2,
            borderColor: '#fff'
          }
        },
        animationDelay: index * 200
      }))
    };

    return <ReactECharts option={option} style={{ height: '450px', width: '100%' }} />;
  };

  // 语义特征网络图 - 优化版
  const getSemanticNetwork = () => {
    const nodes = [
      // 核心节点
      {
        id: 'center',
        name: '认知诊断',
        x: 0,
        y: 0,
        symbolSize: 60,
        category: 0,
        itemStyle: {
          color: '#1890ff',
          borderColor: '#0050b3',
          borderWidth: 3,
          shadowBlur: 10,
          shadowColor: 'rgba(24, 144, 255, 0.3)'
        }
      },

      // 主要能力维度
      {
        id: 'math',
        name: '数学能力',
        x: 120,
        y: 0,
        symbolSize: 45,
        category: 1,
        itemStyle: {
          color: '#52c41a',
          borderColor: '#389e0d',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(82, 196, 26, 0.3)'
        }
      },
      {
        id: 'logic',
        name: '逻辑思维',
        x: 0,
        y: 120,
        symbolSize: 45,
        category: 1,
        itemStyle: {
          color: '#52c41a',
          borderColor: '#389e0d',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(82, 196, 26, 0.3)'
        }
      },
      {
        id: 'spatial',
        name: '空间想象',
        x: -120,
        y: 0,
        symbolSize: 45,
        category: 1,
        itemStyle: {
          color: '#52c41a',
          borderColor: '#389e0d',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(82, 196, 26, 0.3)'
        }
      },
      {
        id: 'problem',
        name: '问题解决',
        x: 0,
        y: -120,
        symbolSize: 45,
        category: 1,
        itemStyle: {
          color: '#52c41a',
          borderColor: '#389e0d',
          borderWidth: 2,
          shadowBlur: 8,
          shadowColor: 'rgba(82, 196, 26, 0.3)'
        }
      },

      // 具体技能节点
      {
        id: 'algebra',
        name: '代数运算',
        x: 180,
        y: 60,
        symbolSize: 30,
        category: 2,
        itemStyle: {
          color: '#faad14',
          borderColor: '#d48806',
          borderWidth: 2,
          shadowBlur: 6,
          shadowColor: 'rgba(250, 173, 20, 0.3)'
        }
      },
      {
        id: 'geometry',
        name: '几何推理',
        x: 60,
        y: 180,
        symbolSize: 30,
        category: 2,
        itemStyle: {
          color: '#faad14',
          borderColor: '#d48806',
          borderWidth: 2,
          shadowBlur: 6,
          shadowColor: 'rgba(250, 173, 20, 0.3)'
        }
      },
      {
        id: 'function',
        name: '函数理解',
        x: -60,
        y: 180,
        symbolSize: 30,
        category: 2,
        itemStyle: {
          color: '#faad14',
          borderColor: '#d48806',
          borderWidth: 2,
          shadowBlur: 6,
          shadowColor: 'rgba(250, 173, 20, 0.3)'
        }
      },
      {
        id: 'stats',
        name: '概率统计',
        x: -180,
        y: 60,
        symbolSize: 30,
        category: 2,
        itemStyle: {
          color: '#faad14',
          borderColor: '#d48806',
          borderWidth: 2,
          shadowBlur: 6,
          shadowColor: 'rgba(250, 173, 20, 0.3)'
        }
      }
    ];

    const links = [
      // 核心到主要能力的连接
      {
        source: 'center',
        target: 'math',
        value: 0.9,
        lineStyle: {
          color: '#1890ff',
          width: 4,
          opacity: 0.8,
          curveness: 0.1
        }
      },
      {
        source: 'center',
        target: 'logic',
        value: 0.8,
        lineStyle: {
          color: '#1890ff',
          width: 4,
          opacity: 0.8,
          curveness: 0.1
        }
      },
      {
        source: 'center',
        target: 'spatial',
        value: 0.7,
        lineStyle: {
          color: '#1890ff',
          width: 4,
          opacity: 0.8,
          curveness: 0.1
        }
      },
      {
        source: 'center',
        target: 'problem',
        value: 0.9,
        lineStyle: {
          color: '#1890ff',
          width: 4,
          opacity: 0.8,
          curveness: 0.1
        }
      },

      // 主要能力到具体技能的连接
      {
        source: 'math',
        target: 'algebra',
        value: 0.9,
        lineStyle: {
          color: '#52c41a',
          width: 3,
          opacity: 0.7,
          curveness: 0.2
        }
      },
      {
        source: 'logic',
        target: 'geometry',
        value: 0.8,
        lineStyle: {
          color: '#52c41a',
          width: 3,
          opacity: 0.7,
          curveness: 0.2
        }
      },
      {
        source: 'spatial',
        target: 'function',
        value: 0.8,
        lineStyle: {
          color: '#52c41a',
          width: 3,
          opacity: 0.7,
          curveness: 0.2
        }
      },
      {
        source: 'problem',
        target: 'stats',
        value: 0.7,
        lineStyle: {
          color: '#52c41a',
          width: 3,
          opacity: 0.7,
          curveness: 0.2
        }
      },

      // 技能间的关联
      {
        source: 'algebra',
        target: 'function',
        value: 0.6,
        lineStyle: {
          color: '#faad14',
          width: 2,
          opacity: 0.5,
          curveness: 0.3,
          type: 'dashed'
        }
      },
      {
        source: 'geometry',
        target: 'stats',
        value: 0.5,
        lineStyle: {
          color: '#faad14',
          width: 2,
          opacity: 0.5,
          curveness: 0.3,
          type: 'dashed'
        }
      }
    ];

    const option = {
      backgroundColor: '#ffffff',
      title: {
        text: '语义特征关联网络',
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#262626'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#1890ff',
        borderWidth: 1,
        textStyle: {
          color: '#fff'
        },
        formatter: (params: any) => {
          if (params.dataType === 'node') {
            const categoryNames = ['🎯 核心概念', '🧠 能力维度', '⚡ 具体技能'];
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold; margin-bottom: 4px;">${params.data.name}</div>
                <div style="color: #bfbfbf; font-size: 12px;">${categoryNames[params.data.category]}</div>
              </div>
            `;
          } else if (params.dataType === 'edge') {
            return `
              <div style="padding: 8px;">
                <div style="font-weight: bold;">${params.data.source} → ${params.data.target}</div>
                <div style="color: #bfbfbf; font-size: 12px;">关联强度: ${(params.data.value * 100).toFixed(0)}%</div>
              </div>
            `;
          }
          return params.data.name;
        }
      },
      animationDuration: 1500,
      animationEasing: 'cubicOut',
      series: [{
        type: 'graph',
        layout: 'none',
        symbolSize: 50,
        roam: true,
        focusNodeAdjacency: true,
        draggable: true,
        label: {
          show: true,
          position: 'inside',
          fontSize: 11,
          fontWeight: 'bold',
          color: '#fff',
          textBorderColor: 'rgba(0, 0, 0, 0.3)',
          textBorderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 13,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        edgeSymbol: ['none', 'arrow'],
        edgeSymbolSize: [0, 8],
        data: nodes,
        links: links,
        categories: [
          {
            name: '🎯 核心概念',
            itemStyle: {
              color: '#1890ff',
              borderColor: '#0050b3',
              borderWidth: 3
            }
          },
          {
            name: '🧠 能力维度',
            itemStyle: {
              color: '#52c41a',
              borderColor: '#389e0d',
              borderWidth: 2
            }
          },
          {
            name: '⚡ 具体技能',
            itemStyle: {
              color: '#faad14',
              borderColor: '#d48806',
              borderWidth: 2
            }
          }
        ],
        lineStyle: {
          opacity: 0.8,
          width: 3,
          curveness: 0.1
        }
      }]
    };

    return <ReactECharts option={option} style={{ height: '500px', width: '100%' }} />;
  };

  return (
    <div>
      <Card style={{ marginBottom: '16px' }}>
        <Row align="middle" gutter={16}>
          <Col>
            <ThunderboltOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          </Col>
          <Col flex={1}>
            <Title level={4} style={{ margin: 0 }}>Embedding语义增强展示</Title>
            <Text type="secondary">展示大模型Embedding如何丰富认知诊断的语义表示</Text>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<BulbOutlined />}
              onClick={loadEmbeddingExamples}
              loading={loading}
            >
              刷新示例
            </Button>
          </Col>
        </Row>
      </Card>

      <Alert
        message="创新亮点：双驱协同认知诊断"
        description={
          <div>
            <div>我们的系统结合传统认知诊断模型和大模型Embedding技术，实现语义丰富的认知状态表示，提供更准确和可解释的诊断结果。</div>
            {embeddingData && (
              <div style={{ marginTop: '8px' }}>
                <Tag color={embeddingData.data_source === '混合数据' ? 'green' : 'blue'}>
                  数据来源: {embeddingData.data_source || '示例数据'}
                </Tag>
                {embeddingData.experiment_count !== undefined && embeddingData.experiment_count > 0 && (
                  <Tag color="purple">
                    基于 {embeddingData.experiment_count} 个实验结果
                  </Tag>
                )}
              </div>
            )}
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: '16px' }}
      />

      {embeddingData && embeddingData.text_examples && (
        <>
          {/* 文本示例展示 */}
          <Card title="📝 语义丰富性示例" style={{ marginBottom: '16px' }}>
            <Row gutter={[16, 16]}>
              {embeddingData.text_examples.map((example, index) => (
                <Col xs={24} lg={12} key={index}>
                  <Card size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <Text strong>原始诊断结果：</Text>
                        <div style={{ 
                          background: '#f5f5f5', 
                          padding: '8px', 
                          borderRadius: '4px',
                          margin: '8px 0'
                        }}>
                          "{example.original}"
                        </div>
                      </div>
                      
                      <div>
                        <Text strong>Embedding增强：</Text>
                        <Paragraph style={{ margin: '8px 0' }}>
                          {example.embedding_description}
                        </Paragraph>
                      </div>

                      <div>
                        <Text strong>语义特征：</Text>
                        <div style={{ marginTop: '8px' }}>
                          {(example.semantic_features || []).map((feature, idx) => (
                            <Tag key={idx} color="blue" style={{ marginBottom: '4px' }}>
                              {feature}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>

          {/* Embedding可视化 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="🎯 Embedding空间聚类">
                {getEmbeddingVisualization()}
                <Divider />
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong style={{ fontSize: '14px', color: '#262626' }}>📊 技术说明：</Text>
                  <div style={{ background: '#f0f5ff', padding: '12px', borderRadius: '6px', border: '1px solid #adc6ff' }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Text type="secondary">
                        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>•</span> <strong>向量维度</strong>：{embeddingData.embedding_visualization?.dimensions || 768}维高维空间表示
                      </Text>
                      <Text type="secondary">
                        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>•</span> <strong>聚类算法</strong>：基于余弦相似度的K-means聚类
                      </Text>
                      <Text type="secondary">
                        <span style={{ color: '#faad14', fontWeight: 'bold' }}>•</span> <strong>聚类效果</strong>：{embeddingData.embedding_visualization?.clustering_info || '相似认知状态自动聚集'}
                      </Text>
                      <Text type="secondary">
                        <span style={{ color: '#722ed1', fontWeight: 'bold' }}>•</span> <strong>可视化方法</strong>：t-SNE降维至2D空间展示
                      </Text>
                    </Space>
                  </div>

                  <div style={{ marginTop: '12px', padding: '8px', background: '#f0f2f5', borderRadius: '4px' }}>
                    <Text style={{ fontSize: '12px', color: '#8c8c8c' }}>
                      🔬 <em>通过高维embedding向量聚类，系统能够自动发现学生的认知模式，为个性化教学提供数据支撑。</em>
                    </Text>
                  </div>
                </Space>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card title="🕸️ 语义关联网络">
                {getSemanticNetwork()}
                <Divider />
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong style={{ fontSize: '14px', color: '#262626' }}>🎯 应用场景：</Text>
                  <div style={{ background: '#f6ffed', padding: '12px', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Text type="secondary">
                        <span style={{ color: '#52c41a', fontWeight: 'bold' }}>•</span> <strong>个性化学习推荐</strong>：基于语义关联分析学生知识薄弱点
                      </Text>
                      <Text type="secondary">
                        <span style={{ color: '#1890ff', fontWeight: 'bold' }}>•</span> <strong>智能学习路径规划</strong>：构建最优的知识点学习序列
                      </Text>
                      <Text type="secondary">
                        <span style={{ color: '#faad14', fontWeight: 'bold' }}>•</span> <strong>同伴协作匹配</strong>：匹配认知能力互补的学习伙伴
                      </Text>
                      <Text type="secondary">
                        <span style={{ color: '#722ed1', fontWeight: 'bold' }}>•</span> <strong>认知诊断优化</strong>：提升诊断模型的准确性和解释性
                      </Text>
                    </Space>
                  </div>

                  <div style={{ marginTop: '12px', padding: '8px', background: '#f0f2f5', borderRadius: '4px' }}>
                    <Text style={{ fontSize: '12px', color: '#8c8c8c' }}>
                      💡 <em>通过语义网络分析，系统能够深度理解知识点间的内在关联，为每个学生提供精准的个性化学习支持。</em>
                    </Text>
                  </div>
                </Space>
              </Card>
            </Col>
          </Row>


        </>
      )}
    </div>
  );
};

export default EmbeddingShowcase;
