import React, { useState, useEffect } from 'react';
import { <PERSON>, Row, Col, But<PERSON>, Select, message, Spin, Typography, Tag, Divider, Alert, Switch } from 'antd';
import { UserOutlined, ExperimentOutlined, BarChartOutlined, RobotOutlined, BulbOutlined, CalendarOutlined, BookOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';
import { cognitiveService, getApiBaseUrl } from '../../services/api';
import DiagnosisReport from '../DiagnosisReport';

// 添加CSS动画
const blinkAnimation = `
  @keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = blinkAnimation;
  document.head.appendChild(style);
}

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

interface CognitiveDiagnosisProps {
  trainingId: string;
  onDiagnosisComplete?: (result: any) => void;
}

interface DiagnosisResult {
  student_id: string;
  knowledge_diagnosis: Record<string, any>;
  overall_ability: number;
  cognitive_vector?: number[];
  data_quality?: string;
}

const CognitiveDiagnosis: React.FC<CognitiveDiagnosisProps> = ({
  trainingId,
  onDiagnosisComplete
}) => {
  const [selectedStudent, setSelectedStudent] = useState<string>('student_001');
  const [diagnosisResult, setDiagnosisResult] = useState<DiagnosisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [llmReport, setLlmReport] = useState<string>('');
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [useLLM, setUseLLM] = useState<boolean>(true);
  const [enhancedDiagnosisResult, setEnhancedDiagnosisResult] = useState<any>(null);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [structuredReport, setStructuredReport] = useState<any>(null);

  // 添加调试信息
  useEffect(() => {
    console.log('🔍 CognitiveDiagnosis组件加载');
    console.log('📊 trainingId:', trainingId);
    console.log('🧮 experiment_id:', parseInt(trainingId));
  }, [trainingId]);

  const studentOptions = [
    { id: 'student_001', name: '学生001 - 张三' },
    { id: 'student_002', name: '学生002 - 李四' },
    { id: 'student_003', name: '学生003 - 王五' },
    { id: 'student_004', name: '学生004 - 赵六' },
    { id: 'student_005', name: '学生005 - 孙七' },
  ];

  const handleDiagnosis = async () => {
    setLoading(true);
    setDiagnosisResult(null);
    setEnhancedDiagnosisResult(null);
    setLlmReport('');
    setIsStreaming(false);
    setStructuredReport(null);

    try {
      if (useLLM) {
        // 使用AI诊断
        await handleLLMDiagnosis();
      } else {
        // 使用传统诊断
        const response = await cognitiveService.diagnoseStudent({
          student_id: selectedStudent,
          experiment_id: parseInt(trainingId)
        });

        if (response.data.success) {
          const result = response.data.diagnosis;
          setDiagnosisResult(result);
          message.success('认知诊断完成！');
          onDiagnosisComplete?.(result);
        } else {
          message.error('诊断失败');
        }
      }
    } catch (error) {
      message.error('认知诊断失败');
      console.error(error);
    } finally {
      setLoading(false);
      setIsStreaming(false);
    }
  };

  const handleLLMDiagnosis = async () => {
    setIsStreaming(true);
    setStructuredReport(null);

    try {
      console.log('� 开始AI诊断...');

      // 显示详细的加载提示
      message.loading({
        content: 'AI正在分析学习数据，请耐心等待（可能需要1-2分钟）...',
        duration: 0, // 不自动关闭
        key: 'llm-diagnosis'
      });

      // 使用普通的JSON API而不是流式API
      const response = await cognitiveService.diagnoseStudentWithReport({
        student_id: selectedStudent,
        experiment_id: parseInt(trainingId)
      });

      // 关闭加载提示
      message.destroy('llm-diagnosis');

      console.log('📤 收到AI诊断响应:', response.data);

      if (response.data.success) {
        const diagnosis = response.data.diagnosis;
        setDiagnosisResult(diagnosis);

        // 检查是否有LLM报告 - 更宽松的条件判断
        console.log('🔍 检查LLM报告数据:', {
          hasLlmReport: !!diagnosis.llm_report,
          reportGenerated: diagnosis.report_generated,
          llmReportKeys: diagnosis.llm_report ? Object.keys(diagnosis.llm_report) : [],
          diagnosisKeys: Object.keys(diagnosis)
        });

        // 🔧 修复：更宽松的条件判断，只要有llm_report就显示
        if (diagnosis.llm_report) {
          setStructuredReport(diagnosis.llm_report);
          setEnhancedDiagnosisResult(diagnosis);
          message.success('AI诊断报告生成完成！');
          console.log('✅ AI报告生成成功');
          console.log('📊 增强诊断数据:', diagnosis);
          console.log('📋 LLM报告内容:', diagnosis.llm_report);
        } else if (diagnosis.report_generated === false) {
          message.warning('AI报告生成失败，显示基础诊断结果');
          if (diagnosis.report_error) {
            console.warn('报告生成错误:', diagnosis.report_error);
          }
        } else {
          // 即使没有LLM报告，也设置基础诊断结果
          setEnhancedDiagnosisResult(diagnosis);
          message.info('显示基础诊断结果');
        }

        // 通知父组件诊断完成
        onDiagnosisComplete?.(diagnosis);
      } else {
        message.error('AI诊断失败');
      }
    } catch (error) {
      // 关闭加载提示
      message.destroy('llm-diagnosis');

      console.error('❌ AI诊断失败:', error);

      // 更详细的错误处理
      const errorMessage = error as any;
      if (errorMessage?.code === 'ECONNABORTED' || errorMessage?.message?.includes('timeout')) {
        message.error('AI诊断超时，请稍后重试');
      } else if (errorMessage?.message?.includes('Server disconnected')) {
        message.error('服务器连接中断，请检查网络连接后重试');
      } else if (errorMessage?.response?.status === 502) {
        message.error('服务器暂时不可用，请稍后重试');
      } else {
        message.error('AI诊断服务暂时不可用，请稍后重试');
      }
    } finally {
      setIsStreaming(false);
    }
  };

  // 雷达图配置
  const getRadarChart = () => {
    if (!diagnosisResult || !diagnosisResult.knowledge_diagnosis) return null;

    const knowledgeComponents = Object.keys(diagnosisResult.knowledge_diagnosis);
    const values = Object.values(diagnosisResult.knowledge_diagnosis).map((item: any) => item?.mastery_probability || 0);

    const option = {
      title: {
        text: `${selectedStudent} 认知诊断雷达图`,
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      radar: {
        indicator: knowledgeComponents.map(kc => ({
          name: kc,
          max: 1
        })),
        radius: '70%'
      },
      series: [{
        name: '掌握程度',
        type: 'radar',
        data: [{
          value: values,
          name: '认知能力',
          areaStyle: {
            color: 'rgba(24, 144, 255, 0.3)'
          },
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          }
        }]
      }]
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  // 柱状图配置
  const getBarChart = () => {
    if (!diagnosisResult || !diagnosisResult.knowledge_diagnosis) return null;

    const knowledgeComponents = Object.keys(diagnosisResult.knowledge_diagnosis);
    const values = Object.values(diagnosisResult.knowledge_diagnosis).map((item: any) => item?.mastery_probability || 0);

    const option = {
      title: {
        text: '知识点掌握情况',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${(data.value * 100).toFixed(1)}%`;
        }
      },
      xAxis: {
        type: 'category',
        data: knowledgeComponents,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        max: 1,
        axisLabel: {
          formatter: (value: number) => `${(value * 100).toFixed(0)}%`
        }
      },
      series: [{
        data: values.map((value, index) => ({
          value,
          itemStyle: {
            color: value > 0.7 ? '#52c41a' : value > 0.5 ? '#faad14' : '#ff4d4f'
          }
        })),
        type: 'bar',
        barWidth: '60%'
      }]
    };

    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  // 认知向量可视化（使用散点图模拟3D效果）
  const get3DChart = () => {
    if (!diagnosisResult || !diagnosisResult.knowledge_diagnosis) {
      return (
        <div style={{
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999'
        }}>
          暂无认知向量数据
        </div>
      );
    }

    const knowledgeComponents = Object.keys(diagnosisResult.knowledge_diagnosis);
    const masteryValues = Object.values(diagnosisResult.knowledge_diagnosis).map((item: any) => item?.mastery_probability || 0);

    // 使用认知向量或掌握程度值
    const vectorValues = diagnosisResult.cognitive_vector || masteryValues;

    // 创建多个散点图系列来模拟3D效果
    const series: any[] = [];

    // 按掌握程度分组
    const groups = [
      { name: '待提升 (<50%)', data: [] as any[], color: '#ff4d4f' },
      { name: '部分掌握 (50%-70%)', data: [] as any[], color: '#faad14' },
      { name: '已掌握 (>70%)', data: [] as any[], color: '#52c41a' }
    ];

    knowledgeComponents.forEach((kc, index) => {
      const value = vectorValues[index] || 0;
      const x = Math.cos(index * 2 * Math.PI / knowledgeComponents.length) * (value * 100 + 20);
      const y = Math.sin(index * 2 * Math.PI / knowledgeComponents.length) * (value * 100 + 20);

      const point = {
        name: kc,
        value: [x, y, (value * 100).toFixed(1)],
        symbolSize: Math.max(10, value * 30),
        label: {
          show: true,
          position: 'top',
          formatter: '{b}',
          fontSize: 10
        }
      };

      if (value < 0.5) {
        groups[0].data.push(point);
      } else if (value < 0.7) {
        groups[1].data.push(point);
      } else {
        groups[2].data.push(point);
      }
    });

    groups.forEach(group => {
      if (group.data.length > 0) {
        series.push({
          name: group.name,
          type: 'scatter',
          data: group.data,
          itemStyle: {
            color: group.color,
            opacity: 0.8
          }
        });
      }
    });

    const option = {
      title: {
        text: '认知向量空间分布',
        left: 'center',
        textStyle: {
          fontSize: 16
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `${params.data.name}<br/>掌握程度: ${params.data.value[2]}%`;
        }
      },
      legend: {
        data: groups.filter(g => g.data.length > 0).map(g => g.name),
        top: 30
      },
      xAxis: {
        type: 'value',
        name: '认知维度X',
        nameLocation: 'middle',
        nameGap: 30,
        axisLine: { show: true },
        axisTick: { show: true }
      },
      yAxis: {
        type: 'value',
        name: '认知维度Y',
        nameLocation: 'middle',
        nameGap: 40,
        axisLine: { show: true },
        axisTick: { show: true }
      },
      series: series
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  return (
    <div>
      <Card style={{ marginBottom: '16px' }}>
        <Row align="middle" gutter={16}>
          <Col>
            <UserOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          </Col>
          <Col flex={1}>
            <Title level={4} style={{ margin: 0 }}>智能诊断分析</Title>
            <Text type="secondary">选择学生进行个性化认知诊断与学习规划</Text>
          </Col>
          <Col>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <RobotOutlined style={{ color: useLLM ? '#1890ff' : '#d9d9d9' }} />
                <Switch
                  checked={useLLM}
                  onChange={setUseLLM}
                  size="small"
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>AI分析</Text>
              </div>
              <Select
                value={selectedStudent}
                onChange={setSelectedStudent}
                style={{ width: 180 }}
              >
                {studentOptions.map(student => (
                  <Option key={student.id} value={student.id}>
                    {student.name}
                  </Option>
                ))}
              </Select>
              <Button
                type="primary"
                icon={useLLM ? <BulbOutlined /> : <ExperimentOutlined />}
                onClick={handleDiagnosis}
                loading={loading}
              >
                {useLLM ? 'AI智能诊断' : '基础诊断'}
              </Button>
            </div>
          </Col>
        </Row>
      </Card>

      {loading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text>正在进行认知诊断分析...</Text>
              {useLLM && (
                <div style={{ marginTop: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <RobotOutlined style={{ marginRight: '4px' }} />
                    AI正在生成个性化分析报告
                  </Text>
                </div>
              )}
            </div>




          </div>
        </Card>
      )}

      {/* AI结构化诊断报告 */}
      {structuredReport && !loading && (
        <div style={{ marginBottom: '16px' }}>
          {/* 报告标题 */}
          <Card
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              marginBottom: '16px',
              border: 'none'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <RobotOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
              <Title level={3} style={{ color: 'white', margin: 0 }}>
                AI智能诊断与规划报告
              </Title>
              <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                基于认知诊断模型的个性化学习分析与路径规划
              </Text>
            </div>
          </Card>

          {/* 学习现状总结 */}
          <Card
            style={{
              marginBottom: '16px',
              borderLeft: '4px solid #1890ff',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                background: '#1890ff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: '12px'
              }}>
                <BarChartOutlined style={{ color: 'white', fontSize: '16px' }} />
              </div>
              <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                📊 学习现状总结
              </Title>
            </div>
            <Paragraph style={{
              fontSize: '16px',
              lineHeight: '1.6',
              background: '#f8f9fa',
              padding: '16px',
              borderRadius: '8px',
              margin: 0
            }}>
              {structuredReport.summary}
            </Paragraph>
          </Card>

          <Row gutter={[16, 16]}>
            {/* 能力分析 */}
            <Col xs={24} lg={12}>
              <Card
                style={{
                  height: '100%',
                  borderLeft: '4px solid #52c41a',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: '#52c41a',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}>
                    <UserOutlined style={{ color: 'white', fontSize: '16px' }} />
                  </div>
                  <Title level={4} style={{ margin: 0, color: '#52c41a' }}>
                    🎯 能力分析
                  </Title>
                </div>

                <div style={{ marginBottom: '16px' }}>
                  <div style={{
                    background: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px'
                  }}>
                    <Text strong style={{ color: '#52c41a', fontSize: '16px' }}>
                      ✅ 优势领域
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.ability_analysis?.strengths?.map((strength: string, index: number) => (
                        <Tag key={index} color="green" style={{ margin: '4px 4px 4px 0' }}>
                          {strength}
                        </Tag>
                      ))}
                    </div>
                  </div>

                  <div style={{
                    background: '#fff2f0',
                    border: '1px solid #ffccc7',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px'
                  }}>
                    <Text strong style={{ color: '#ff4d4f', fontSize: '16px' }}>
                      ⚠️ 需要改进
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.ability_analysis?.weaknesses?.map((weakness: string, index: number) => (
                        <Tag key={index} color="red" style={{ margin: '4px 4px 4px 0' }}>
                          {weakness}
                        </Tag>
                      ))}
                    </div>
                  </div>
                </div>

                <div style={{
                  background: '#f0f5ff',
                  border: '1px solid #adc6ff',
                  borderRadius: '8px',
                  padding: '12px'
                }}>
                  <Text strong style={{ color: '#1890ff', fontSize: '16px' }}>
                    📝 整体评价
                  </Text>
                  <Paragraph style={{ marginTop: '8px', marginBottom: 0 }}>
                    {structuredReport.ability_analysis?.overall_assessment}
                  </Paragraph>
                </div>
              </Card>
            </Col>

            {/* 学习建议 */}
            <Col xs={24} lg={12}>
              <Card
                style={{
                  height: '100%',
                  borderLeft: '4px solid #722ed1',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: '#722ed1',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}>
                    <BulbOutlined style={{ color: 'white', fontSize: '16px' }} />
                  </div>
                  <Title level={4} style={{ margin: 0, color: '#722ed1' }}>
                    💡 学习建议
                  </Title>
                </div>

                <div style={{ marginBottom: '12px' }}>
                  <div style={{
                    background: '#f9f0ff',
                    border: '1px solid #d3adf7',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px'
                  }}>
                    <Text strong style={{ color: '#1890ff', fontSize: '14px' }}>
                      🎯 立即行动
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.recommendations?.immediate_actions?.map((action: string, index: number) => (
                        <div key={index} style={{
                          background: 'white',
                          padding: '8px',
                          borderRadius: '4px',
                          margin: '4px 0',
                          border: '1px solid #e6f7ff'
                        }}>
                          <Text>• {action}</Text>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div style={{
                    background: '#f6ffed',
                    border: '1px solid #b7eb8f',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px'
                  }}>
                    <Text strong style={{ color: '#722ed1', fontSize: '14px' }}>
                      📚 学习方法
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.recommendations?.study_methods?.map((method: string, index: number) => (
                        <div key={index} style={{
                          background: 'white',
                          padding: '8px',
                          borderRadius: '4px',
                          margin: '4px 0',
                          border: '1px solid #f6ffed'
                        }}>
                          <Text>• {method}</Text>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div style={{
                    background: '#fff7e6',
                    border: '1px solid #ffd591',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <Text strong style={{ color: '#fa8c16', fontSize: '14px' }}>
                      🔍 重点关注
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.recommendations?.focus_areas?.map((area: string, index: number) => (
                        <div key={index} style={{
                          background: 'white',
                          padding: '8px',
                          borderRadius: '4px',
                          margin: '4px 0',
                          border: '1px solid #fff7e6'
                        }}>
                          <Text>• {area}</Text>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            {/* 学习计划 */}
            <Col xs={24} lg={12}>
              <Card
                style={{
                  borderLeft: '4px solid #13c2c2',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: '#13c2c2',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}>
                    <CalendarOutlined style={{ color: 'white', fontSize: '16px' }} />
                  </div>
                  <Title level={4} style={{ margin: 0, color: '#13c2c2' }}>
                    📅 学习计划
                  </Title>
                </div>

                <div style={{ marginBottom: '16px' }}>
                  <div style={{
                    background: '#e6fffb',
                    border: '1px solid #87e8de',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px'
                  }}>
                    <Text strong style={{ color: '#13c2c2', fontSize: '14px' }}>
                      📅 短期目标 (1-3个月)
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.learning_plan?.short_term?.map((goal: string, index: number) => (
                        <div key={index} style={{
                          background: 'white',
                          padding: '8px',
                          borderRadius: '4px',
                          margin: '4px 0',
                          border: '1px solid #e6fffb',
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          <div style={{
                            width: '6px',
                            height: '6px',
                            borderRadius: '50%',
                            background: '#13c2c2',
                            marginRight: '8px'
                          }} />
                          <Text>{goal}</Text>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div style={{
                    background: '#fff0f6',
                    border: '1px solid #ffadd2',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <Text strong style={{ color: '#eb2f96', fontSize: '14px' }}>
                      🎯 长期目标 (3-12个月)
                    </Text>
                    <div style={{ marginTop: '8px' }}>
                      {structuredReport.learning_plan?.long_term?.map((goal: string, index: number) => (
                        <div key={index} style={{
                          background: 'white',
                          padding: '8px',
                          borderRadius: '4px',
                          margin: '4px 0',
                          border: '1px solid #fff0f6',
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          <div style={{
                            width: '6px',
                            height: '6px',
                            borderRadius: '50%',
                            background: '#eb2f96',
                            marginRight: '8px'
                          }} />
                          <Text>{goal}</Text>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            </Col>

            {/* 推荐资源 */}
            <Col xs={24} lg={12}>
              <Card
                style={{
                  borderLeft: '4px solid #fa8c16',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: '#fa8c16',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}>
                    <BookOutlined style={{ color: 'white', fontSize: '16px' }} />
                  </div>
                  <Title level={4} style={{ margin: 0, color: '#fa8c16' }}>
                    📚 推荐资源
                  </Title>
                </div>

                <div style={{
                  background: '#fff7e6',
                  border: '1px solid #ffd591',
                  borderRadius: '8px',
                  padding: '12px'
                }}>
                  {structuredReport.learning_plan?.resources?.map((resource: string, index: number) => (
                    <div key={index} style={{
                      background: 'white',
                      padding: '12px',
                      borderRadius: '6px',
                      margin: '8px 0',
                      border: '1px solid #fff7e6',
                      display: 'flex',
                      alignItems: 'center',
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                    }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        background: '#fa8c16',
                        marginRight: '12px'
                      }} />
                      <Text style={{ fontSize: '14px' }}>{resource}</Text>
                    </div>
                  ))}
                </div>
              </Card>
            </Col>
          </Row>

          {/* 鼓励话语 */}
          <Card
            style={{
              marginTop: '16px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              color: 'white'
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', marginBottom: '12px' }}>💪</div>
              <Title level={3} style={{ color: 'white', marginBottom: '8px' }}>
                加油鼓励
              </Title>
              <Paragraph style={{
                color: 'rgba(255,255,255,0.9)',
                fontSize: '16px',
                lineHeight: '1.6',
                margin: 0
              }}>
                {structuredReport.encouragement}
              </Paragraph>
            </div>
          </Card>
        </div>
      )}

      {diagnosisResult && !loading && (
        <>
          {/* 诊断概览 */}
          <Card
            title="诊断概览"
            style={{ marginBottom: '16px' }}
            extra={
              <Tag color={diagnosisResult.data_quality === '混合数据' ? 'green' : 'blue'}>
                {diagnosisResult.data_quality || '模拟数据'}
              </Tag>
            }
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#1890ff' }}>
                    {((diagnosisResult.overall_ability || 0) * 100).toFixed(1)}%
                  </div>
                  <div>整体能力水平</div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {Object.values(diagnosisResult.knowledge_diagnosis || {}).filter((item: any) => item?.mastery_probability > 0.7).length}
                  </div>
                  <div>掌握知识点</div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>
                    {Object.values(diagnosisResult.knowledge_diagnosis || {}).filter((item: any) => item?.mastery_probability < 0.5).length}
                  </div>
                  <div>薄弱知识点</div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 可视化图表 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="雷达图分析">
                {getRadarChart()}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="柱状图分析">
                {getBarChart()}
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col xs={24}>
              <Card title="认知向量空间分布">
                {get3DChart()}
              </Card>
            </Col>
          </Row>

          {/* AI智能诊断报告 */}
          {(() => {
            console.log('🎯 渲染条件检查:', {
              hasEnhancedResult: !!enhancedDiagnosisResult,
              hasLlmReport: !!(enhancedDiagnosisResult?.llm_report),
              enhancedResultKeys: enhancedDiagnosisResult ? Object.keys(enhancedDiagnosisResult) : [],
              llmReportKeys: enhancedDiagnosisResult?.llm_report ? Object.keys(enhancedDiagnosisResult.llm_report) : []
            });
            return null;
          })()}
          {enhancedDiagnosisResult && (
            <div style={{ marginTop: '16px' }}>
              <DiagnosisReport
                diagnosisData={enhancedDiagnosisResult}
                onRefresh={handleDiagnosis}
              />
            </div>
          )}

          {/* 简单的LLM报告显示（向后兼容） */}
          {llmReport && !enhancedDiagnosisResult && (
            <Card title="🤖 AI诊断报告" style={{ marginTop: '16px' }}>
              <div style={{ whiteSpace: 'pre-wrap', lineHeight: '1.6' }}>
                {llmReport}
              </div>
            </Card>
          )}

          {/* 学习推荐 */}
          {recommendations.length > 0 && (
            <Card title="📚 个性化学习推荐" style={{ marginTop: '16px' }}>
              <Row gutter={[16, 16]}>
                {recommendations.map((question, index) => (
                  <Col xs={24} md={12} lg={8} key={question.id}>
                    <Card size="small" hoverable>
                      <div style={{ marginBottom: '8px' }}>
                        <Tag color="blue">{question.knowledge_component}</Tag>
                        <Tag color={
                          question.difficulty === '基础' ? 'green' : 
                          question.difficulty === '中等' ? 'orange' : 'red'
                        }>
                          {question.difficulty}
                        </Tag>
                      </div>
                      <Paragraph ellipsis={{ rows: 2 }}>
                        {question.question}
                      </Paragraph>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {question.explanation}
                      </Text>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          )}
        </>
      )}
    </div>
  );
};

export default CognitiveDiagnosis;
