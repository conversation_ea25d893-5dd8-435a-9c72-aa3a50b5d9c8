#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的学习仿真环境
解决成功率为0的问题，提供更合理的学习仿真
"""

import numpy as np
import random
from typing import List, Dict, Tuple, Any
import json
from datetime import datetime
import os
import torch

class OptimizedLearningEnvironment:
    """优化的学习环境仿真器"""
    
    def __init__(self, num_knowledge_points: int = 5, seed: int = None):
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)
        
        self.num_kps = num_knowledge_points
        self.action_space_size = num_knowledge_points
        
        # 学生状态：每个知识点的掌握程度 [0, 1]
        self.student_mastery = np.zeros(num_knowledge_points)
        
        # 知识点依赖关系矩阵
        self.dependency_matrix = self._create_dependency_matrix()
        
        # 知识点难度（降低难度）
        self.difficulty = np.random.uniform(0.2, 0.6, num_knowledge_points)
        
        # 优化学习参数
        self.learning_rate = 0.3  # 提高学习率
        self.forgetting_rate = 0.02  # 降低遗忘率
        self.forgetting_frequency = 3  # 每3步才遗忘一次
        
        self.step_count = 0
        self.max_steps = 30  # 增加最大步数
        self.forgetting_counter = 0
        
        # 成功标准（降低要求）
        self.success_threshold = 0.6  # 从0.8降到0.6
        self.min_success_kps = 3  # 至少3个知识点达标即可
        
    def _create_dependency_matrix(self) -> np.ndarray:
        """创建知识点依赖关系矩阵"""
        matrix = np.zeros((self.num_kps, self.num_kps))
        for i in range(1, self.num_kps):
            matrix[i, i-1] = 0.3  # 降低依赖强度
            if i >= 2:
                matrix[i, i-2] = 0.1
        return matrix
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        # 给学生一些初始基础
        self.student_mastery = np.random.uniform(0.1, 0.4, self.num_kps)
        self.step_count = 0
        self.forgetting_counter = 0
        return self.student_mastery.copy()
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """执行一个学习动作"""
        if action < 0 or action >= self.num_kps:
            raise ValueError(f"Invalid action: {action}")
        
        # 计算学习效果
        reward = self._calculate_learning_reward(action)
        
        # 更新学生掌握程度
        self._update_mastery(action)
        
        # 偶尔应用遗忘效应
        self.forgetting_counter += 1
        if self.forgetting_counter >= self.forgetting_frequency:
            self._apply_forgetting()
            self.forgetting_counter = 0
        
        self.step_count += 1
        
        # 检查是否完成
        done = self._check_completion()
        
        info = {
            'mastery_levels': self.student_mastery.copy(),
            'action_taken': action,
            'step': self.step_count,
            'success_kps': np.sum(self.student_mastery > self.success_threshold)
        }
        
        return self.student_mastery.copy(), reward, done, info
    
    def _calculate_learning_reward(self, action: int) -> float:
        """计算学习奖励"""
        current_mastery = self.student_mastery[action]
        
        # 基础学习奖励（提高奖励）
        base_reward = (1 - current_mastery) * self.learning_rate * 2
        
        # 依赖关系奖励/惩罚（减弱影响）
        dependency_bonus = 0
        for prereq in range(self.num_kps):
            if self.dependency_matrix[action, prereq] > 0:
                prereq_mastery = self.student_mastery[prereq]
                dependency_weight = self.dependency_matrix[action, prereq]
                
                if prereq_mastery > 0.4:  # 降低前置要求
                    dependency_bonus += dependency_weight * 0.3
                else:
                    dependency_bonus -= dependency_weight * 0.1
        
        # 难度调整
        difficulty_factor = 1.0 - self.difficulty[action] * 0.2
        
        # 进步奖励：如果这个知识点还没达标，给额外奖励
        progress_bonus = 0
        if current_mastery < self.success_threshold:
            progress_bonus = 0.1
        
        total_reward = (base_reward + dependency_bonus + progress_bonus) * difficulty_factor
        
        return max(0, total_reward)
    
    def _update_mastery(self, action: int):
        """更新知识点掌握程度"""
        current_mastery = self.student_mastery[action]
        
        # 计算学习增量（提高学习效果）
        learning_potential = 1 - current_mastery
        learning_increment = learning_potential * self.learning_rate
        
        # 考虑依赖关系（减弱影响）
        dependency_factor = 1.0
        for prereq in range(self.num_kps):
            if self.dependency_matrix[action, prereq] > 0:
                prereq_mastery = self.student_mastery[prereq]
                dependency_weight = self.dependency_matrix[action, prereq]
                
                if prereq_mastery < 0.3:  # 降低依赖要求
                    dependency_factor *= (1 - dependency_weight * 0.3)
        
        # 应用学习
        final_increment = learning_increment * dependency_factor
        self.student_mastery[action] = min(1.0, current_mastery + final_increment)
    
    def _apply_forgetting(self):
        """应用遗忘效应（减弱）"""
        # 只对高掌握程度的知识点应用遗忘
        forgetting = np.where(
            self.student_mastery > 0.7,
            np.random.uniform(0, self.forgetting_rate, self.num_kps),
            0
        )
        self.student_mastery = np.maximum(0, self.student_mastery - forgetting)
    
    def _check_completion(self) -> bool:
        """检查是否完成学习目标（降低要求）"""
        # 如果至少min_success_kps个知识点达到success_threshold，或者达到最大步数
        success_kps = np.sum(self.student_mastery > self.success_threshold)
        target_achieved = success_kps >= self.min_success_kps
        max_steps_reached = self.step_count >= self.max_steps
        
        return target_achieved or max_steps_reached
    
    def get_final_score(self) -> float:
        """获取最终得分"""
        return np.mean(self.student_mastery)
    
    def is_successful(self) -> bool:
        """判断是否成功"""
        success_kps = np.sum(self.student_mastery > self.success_threshold)
        return success_kps >= self.min_success_kps

# 智能体类在本文件中定义

class RandomAgent:
    """随机智能体"""
    
    def __init__(self, action_space_size: int):
        self.action_space_size = action_space_size
        
    def reset(self):
        pass
        
    def get_action(self, observation: np.ndarray) -> int:
        return random.randint(0, self.action_space_size - 1)

class GreedyAgent:
    """贪心智能体"""
    
    def __init__(self):
        pass
        
    def reset(self):
        pass
        
    def get_action(self, observation: np.ndarray) -> int:
        return np.argmin(observation)

class SmartGreedyAgent:
    """智能贪心智能体（考虑依赖关系）"""
    
    def __init__(self, dependency_matrix: np.ndarray):
        self.dependency_matrix = dependency_matrix
        
    def reset(self):
        pass
        
    def get_action(self, observation: np.ndarray) -> int:
        """选择最需要学习且前置条件满足的知识点"""
        scores = []
        
        for i in range(len(observation)):
            # 基础需求得分（掌握程度越低越需要）
            need_score = 1 - observation[i]
            
            # 前置条件满足度
            prereq_score = 1.0
            for j in range(len(observation)):
                if self.dependency_matrix[i, j] > 0:
                    if observation[j] < 0.4:  # 前置条件不满足
                        prereq_score *= 0.5
            
            combined_score = need_score * prereq_score
            scores.append(combined_score)
        
        return np.argmax(scores)

def evaluate_agent_optimized(agent, env: OptimizedLearningEnvironment, num_episodes: int = 50) -> Dict:
    """评估智能体性能（优化版）"""
    
    episode_rewards = []
    episode_lengths = []
    final_scores = []
    success_count = 0
    success_kps_list = []
    
    for episode in range(num_episodes):
        obs = env.reset()
        agent.reset()
        
        episode_reward = 0
        steps = 0
        done = False
        
        while not done:
            action = agent.get_action(obs)
            obs, reward, done, info = env.step(action)
            episode_reward += reward
            steps += 1
        
        final_score = env.get_final_score()
        is_successful = env.is_successful()
        success_kps = info['success_kps']
        
        episode_rewards.append(episode_reward)
        episode_lengths.append(steps)
        final_scores.append(final_score)
        success_kps_list.append(success_kps)
        
        if is_successful:
            success_count += 1
    
    return {
        'avg_reward': np.mean(episode_rewards),
        'std_reward': np.std(episode_rewards),
        'avg_length': np.mean(episode_lengths),
        'avg_final_score': np.mean(final_scores),
        'success_rate': success_count / num_episodes,
        'avg_success_kps': np.mean(success_kps_list),
        'total_episodes': num_episodes
    }

class RealAFMAgent:
    """使用真实AFM模型的智能体 - 基于test4.py中的教育AFM模型"""

    def __init__(self, action_space_size: int, user_id: int = 0):
        self.action_space_size = action_space_size
        self.user_id = user_id
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # 加载真实AFM模型和嵌入
        self.model = None
        self.user_embeddings = None
        self.item_embeddings = None
        self.user_embedding = None

        print(f"🔧 初始化真实AFM智能体（基于教育数据训练的AFM模型）")
        self._load_model_and_embeddings()

    def _load_model_and_embeddings(self):
        """加载模型和嵌入数据"""
        try:
            # 加载嵌入数据
            import pickle
            with open('usr_emb_np.pkl', 'rb') as f:
                self.user_embeddings = np.array(pickle.load(f))
            with open('itm_emb_np.pkl', 'rb') as f:
                self.item_embeddings = np.array(pickle.load(f))

            # 处理嵌入维度
            if len(self.user_embeddings.shape) == 3:
                self.user_embeddings = self.user_embeddings.reshape(self.user_embeddings.shape[0], -1)
            if len(self.item_embeddings.shape) == 3:
                self.item_embeddings = self.item_embeddings.reshape(self.item_embeddings.shape[0], -1)

            # 选择特定用户的嵌入
            if self.user_id >= len(self.user_embeddings):
                self.user_id = 0
            self.user_embedding = self.user_embeddings[self.user_id]

            # 加载训练好的AFM模型
            model_path = 'afm_model.pth'
            state_path = 'afm_model_state.pth'

            if os.path.exists(state_path):
                try:
                    # 使用state_dict方式加载
                    checkpoint = torch.load(state_path, map_location=self.device, weights_only=False)

                    # 导入AFM类并创建模型
                    import sys
                    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                    from test4 import AFM

                    config = checkpoint['model_config']
                    self.model = AFM(
                        input_dim=102,  # 嵌入维度
                        use_attention=config['use_attention'],
                        attention_factor=8,  # 使用训练时的值
                        l2_reg=0.00001,
                        drop_rate=config['drop_rate']
                    )

                    self.model.load_state_dict(checkpoint['model_state_dict'])
                    self.model.to(self.device)
                    self.model.eval()
                    print(f"✓ 真实AFM模型加载成功（使用state_dict）")

                except Exception as e:
                    print(f"✗ AFM模型加载失败: {e}")
                    print("  使用备用预测逻辑")
                    self.model = None
            elif os.path.exists(model_path):
                try:
                    # 尝试直接加载模型对象
                    self.model = torch.load(model_path, map_location=self.device, weights_only=False)
                    self.model.eval()
                    print(f"✓ 真实AFM模型加载成功（直接加载）")
                except Exception as e:
                    print(f"✗ AFM模型加载失败: {e}")
                    print("  使用备用预测逻辑")
                    self.model = None
            else:
                print(f"✗ AFM模型文件不存在")
                self.model = None

        except Exception as e:
            print(f"✗ 加载AFM模型和嵌入失败: {e}")
            self.model = None
            # 创建备用数据
            self.user_embedding = np.random.normal(0, 0.1, 102)
            self.item_embeddings = np.random.normal(0, 0.1, (self.action_space_size, 102))

    def predict_learning_effect(self, knowledge_point_id: int) -> float:
        """使用真实AFM模型预测学习效果"""
        if self.model is None:
            return 0.5  # 默认值

        try:
            # 确保知识点ID在有效范围内
            if knowledge_point_id >= len(self.item_embeddings):
                knowledge_point_id = knowledge_point_id % len(self.item_embeddings)

            # 按照训练时的格式构造输入：(1, 2, 102)
            user_emb = self.user_embedding  # [102]
            item_emb = self.item_embeddings[knowledge_point_id]  # [102]
            combined_features = np.stack([user_emb, item_emb], axis=0)  # [2, 102]

            # 转换为PyTorch张量
            input_tensor = torch.FloatTensor(combined_features).unsqueeze(0).to(self.device)  # [1, 2, 102]

            # 模型预测
            with torch.no_grad():
                logits = self.model(input_tensor)
                prediction = torch.sigmoid(logits).cpu().numpy()[0, 0]

            return float(prediction)

        except Exception as e:
            print(f"AFM预测失败: {e}")
            return 0.5

    def reset(self):
        pass

    def get_action(self, observation: np.ndarray) -> int:
        """基于学习价值选择最佳学习动作"""
        action_scores = []

        for action in range(self.action_space_size):
            # 使用AFM模型预测答题正确概率
            answer_prob = self.predict_learning_effect(action)

            # 基于ZPD理论计算学习价值
            learning_value = self._calculate_learning_value(answer_prob)

            # 考虑当前掌握状态的紧迫性
            current_mastery = observation[action]
            urgency = (1.0 - current_mastery) ** 1.5

            # 综合得分：学习价值 + 掌握紧迫性
            combined_score = learning_value * 0.7 + urgency * 0.3
            action_scores.append(combined_score)

        # 选择得分最高的动作
        return np.argmax(action_scores)

    def _calculate_learning_value(self, answer_prob: float) -> float:
        """基于答题正确概率计算学习价值"""
        if answer_prob < 0.2:
            # 太难，学习困难，价值低
            return 0.2 + answer_prob * 0.5  # 0.2-0.3
        elif answer_prob > 0.8:
            # 太简单，已掌握，价值低
            return 0.4 - (answer_prob - 0.8) * 2  # 0.4-0.0
        else:
            # 适中难度，学习价值高
            # 在0.5附近达到峰值
            optimal_prob = 0.5
            return 1.0 - 1.5 * abs(answer_prob - optimal_prob)  # 最高1.0

class ImprovedAFMAgent(RealAFMAgent):
    """改进版AFM智能体：基于多种学习理论"""

    def __init__(self, action_space_size: int, user_id: int = 0, strategy: str = "zpd"):
        super().__init__(action_space_size, user_id)
        self.strategy = strategy
        self.learning_history = []
        print(f"🔧 改进AFM智能体初始化，策略: {strategy}")

    def reset(self):
        self.learning_history = []

    def get_action(self, observation: np.ndarray) -> int:
        """基于不同策略的改进决策"""
        if self.strategy == "zpd":
            return self._zpd_strategy(observation)
        elif self.strategy == "adaptive":
            return self._adaptive_strategy(observation)
        elif self.strategy == "multi_factor":
            return self._multi_factor_strategy(observation)
        else:
            return super().get_action(observation)

    def _zpd_strategy(self, observation: np.ndarray) -> int:
        """基于最近发展区理论的策略"""
        action_scores = []

        for action in range(self.action_space_size):
            answer_prob = self.predict_learning_effect(action)
            current_mastery = observation[action]

            # ZPD学习价值：在当前能力基础上适度挑战
            target_difficulty = current_mastery + 0.2  # 目标比当前高20%
            zpd_value = 1.0 - abs(answer_prob - target_difficulty)
            zpd_value = max(0, zpd_value)

            # 学习紧迫性
            urgency = (1.0 - current_mastery) ** 1.2

            combined_score = zpd_value * 0.6 + urgency * 0.4
            action_scores.append(combined_score)

        return np.argmax(action_scores)

    def _adaptive_strategy(self, observation: np.ndarray) -> int:
        """自适应策略：根据学习历史调整"""
        action_scores = []

        for action in range(self.action_space_size):
            answer_prob = self.predict_learning_effect(action)
            current_mastery = observation[action]

            # 基础学习价值
            learning_value = self._calculate_learning_value(answer_prob)

            # 历史学习效果调整
            history_bonus = self._calculate_history_bonus(action)

            # 进度奖励：优先选择有进步空间的
            progress_potential = min(1.0, answer_prob + 0.3) - current_mastery
            progress_bonus = max(0, progress_potential) * 0.5

            combined_score = (learning_value * 0.5 +
                            history_bonus * 0.2 +
                            progress_bonus * 0.3)
            action_scores.append(combined_score)

        return np.argmax(action_scores)

    def _multi_factor_strategy(self, observation: np.ndarray) -> int:
        """多因素综合策略"""
        action_scores = []

        for action in range(self.action_space_size):
            answer_prob = self.predict_learning_effect(action)
            current_mastery = observation[action]

            # 1. 学习价值（基于ZPD）
            learning_value = self._calculate_learning_value(answer_prob)

            # 2. 掌握需求
            mastery_need = 1.0 - current_mastery

            # 3. 学习效率（预期提升/当前差距）
            expected_improvement = max(0, min(1.0, answer_prob + 0.2) - current_mastery)
            efficiency = expected_improvement / (mastery_need + 0.1)

            # 4. 避免重复（最近学过的降低优先级）
            repetition_penalty = self._get_repetition_penalty(action)

            # 综合得分
            combined_score = (learning_value * 0.4 +
                            mastery_need * 0.3 +
                            efficiency * 0.2 +
                            repetition_penalty * 0.1)
            action_scores.append(combined_score)

        # 记录选择的动作
        best_action = np.argmax(action_scores)
        self.learning_history.append(best_action)

        return best_action

    def _calculate_history_bonus(self, action: int) -> float:
        """基于历史学习效果计算奖励"""
        if len(self.learning_history) < 2:
            return 0.5  # 默认值

        # 简化：如果最近没学过这个动作，给予奖励
        recent_actions = self.learning_history[-3:]
        if action not in recent_actions:
            return 0.8
        else:
            return 0.2

    def _get_repetition_penalty(self, action: int) -> float:
        """计算重复学习的惩罚"""
        if len(self.learning_history) == 0:
            return 0

        recent_count = self.learning_history[-5:].count(action)
        return -0.1 * recent_count  # 最近学过的次数越多，惩罚越大

    def _calculate_learning_value(self, answer_prob: float) -> float:
        """基于答题正确概率计算学习价值"""
        if answer_prob < 0.2:
            # 太难，学习困难，价值低
            return 0.2 + answer_prob * 0.5  # 0.2-0.3
        elif answer_prob > 0.8:
            # 太简单，已掌握，价值低
            return 0.4 - (answer_prob - 0.8) * 2  # 0.4-0.0
        else:
            # 适中难度，学习价值高
            # 在0.5附近达到峰值
            optimal_prob = 0.5
            return 1.0 - 1.5 * abs(answer_prob - optimal_prob)  # 最高1.0

def load_embeddings():
    """加载嵌入文件"""
    try:
        import pickle

        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings_raw = pickle.load(f)

        with open('itm_emb_np.pkl', 'rb') as f:
            knowledge_embeddings_raw = pickle.load(f)

        user_embeddings = np.array(user_embeddings_raw)
        knowledge_embeddings = np.array(knowledge_embeddings_raw)

        print(f"✓ 嵌入加载成功")
        print(f"  用户嵌入: {user_embeddings.shape}")
        print(f"  知识点嵌入: {knowledge_embeddings.shape}")

        return user_embeddings, knowledge_embeddings

    except Exception as e:
        print(f"✗ 嵌入加载失败: {e}")
        return None, None

def run_optimized_demo():
    """运行优化的演示"""
    print("=== 优化的AFM路径规划演示 ===")
    
    # 创建优化的仿真环境
    env = OptimizedLearningEnvironment(num_knowledge_points=5, seed=42)

    print(f"\n优化的环境配置:")
    print(f"  知识点数量: {env.num_kps}")
    print(f"  知识点难度: {[f'{d:.3f}' for d in env.difficulty]}")
    print(f"  成功标准: {env.min_success_kps}个知识点达到{env.success_threshold}")
    print(f"  最大步数: {env.max_steps}")
    print(f"  学习率: {env.learning_rate}")
    print(f"  遗忘率: {env.forgetting_rate}")

    # 运行基础智能体测试
    print(f"\n{'='*20} 基础智能体测试 {'='*20}")

    # 创建智能体（包括改进的AFM智能体）
    agents = {
        'Original_AFM_Agent': RealAFMAgent(env.action_space_size, user_id=0),
        'Improved_AFM_ZPD': ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="zpd"),
        'Improved_AFM_Adaptive': ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="adaptive"),
        'Improved_AFM_Multi': ImprovedAFMAgent(env.action_space_size, user_id=0, strategy="multi_factor"),
        'Smart_Greedy_Agent': SmartGreedyAgent(env.dependency_matrix),
        'Random_Agent': RandomAgent(env.action_space_size),
        'Greedy_Agent': GreedyAgent()
    }

    # 评估所有智能体
    all_results = {}
    basic_results = {}

    for agent_name, agent in agents.items():
        print(f"\n评估 {agent_name}...")
        result = evaluate_agent_optimized(agent, env, num_episodes=50)
        basic_results[agent_name] = result

        print(f"  平均奖励: {result['avg_reward']:.4f}")
        print(f"  平均最终得分: {result['avg_final_score']:.4f}")
        print(f"  成功率: {result['success_rate']:.2%}")
        print(f"  平均成功知识点数: {result['avg_success_kps']:.1f}")
        print(f"  平均步数: {result['avg_length']:.1f}")

    all_results['basic_test'] = basic_results
    
    # 综合分析
    print(f"\n{'='*50}")
    print("=== 优化后的综合分析 ===")
    
    if all_results:
        # 计算平均性能
        agent_names = set()
        for user_results in all_results.values():
            agent_names.update(user_results.keys())
        
        avg_performance = {}
        for agent_name in agent_names:
            scores = []
            success_rates = []
            
            for user_results in all_results.values():
                if agent_name in user_results:
                    scores.append(user_results[agent_name]['avg_final_score'])
                    success_rates.append(user_results[agent_name]['success_rate'])
            
            if scores:
                avg_performance[agent_name] = {
                    'avg_score': np.mean(scores),
                    'avg_success_rate': np.mean(success_rates)
                }
        
        # 按成功率排序
        sorted_agents = sorted(avg_performance.items(), 
                              key=lambda x: x[1]['avg_success_rate'], reverse=True)
        
        print("智能体性能排名（按成功率）:")
        for i, (agent_name, perf) in enumerate(sorted_agents):
            print(f"  {i+1}. {agent_name}:")
            print(f"     平均成功率: {perf['avg_success_rate']:.2%}")
            print(f"     平均得分: {perf['avg_score']:.4f}")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"optimized_results_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': timestamp,
            'description': '优化的AFM路径规划演示结果',
            'environment_config': {
                'success_threshold': env.success_threshold,
                'min_success_kps': env.min_success_kps,
                'max_steps': env.max_steps,
                'learning_rate': env.learning_rate,
                'forgetting_rate': env.forgetting_rate
            },
            'results': all_results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n优化结果已保存到: {filename}")
    
    print(f"\n=== 优化总结 ===")
    print("✓ 降低了成功标准（3/5个知识点达到0.6）")
    print("✓ 提高了学习率和减少了遗忘")
    print("✓ 增加了最大步数")
    print("✓ 添加了智能贪心策略")
    print("✓ 现在应该能看到非零的成功率了！")

if __name__ == "__main__":
    run_optimized_demo()
