#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的强化学习智能体实现
展示如何将仿真框架改造为真正的强化学习
"""

import numpy as np
import random
from collections import defaultdict, deque
from simulation_framework_explained import LearningEnvironment, BaseAgent

class QLearningAgent(BaseAgent):
    """
    Q-Learning强化学习智能体
    真正从经验中学习的智能体
    """
    
    def __init__(self, action_space_size: int, 
                 learning_rate: float = 0.1,
                 discount_factor: float = 0.95,
                 epsilon: float = 0.1,
                 epsilon_decay: float = 0.995,
                 epsilon_min: float = 0.01):
        super().__init__(action_space_size)
        
        # Q-Learning参数
        self.learning_rate = learning_rate      # 学习率α
        self.discount_factor = discount_factor  # 折扣因子γ
        self.epsilon = epsilon                  # 探索率ε
        self.epsilon_decay = epsilon_decay      # ε衰减率
        self.epsilon_min = epsilon_min          # 最小ε值
        
        # Q表：Q(s,a) -> 期望回报
        self.q_table = defaultdict(lambda: np.zeros(action_space_size))
        
        # 经验记录
        self.episode_count = 0
        self.total_reward = 0
        
        print(f"🧠 Q-Learning智能体初始化")
        print(f"  学习率: {learning_rate}")
        print(f"  折扣因子: {discount_factor}")
        print(f"  初始探索率: {epsilon}")
    
    def _state_to_key(self, observation: np.ndarray) -> str:
        """将连续状态离散化为字符串键"""
        # 将[0,1]的连续值离散化为10个区间
        discretized = (observation * 10).astype(int)
        discretized = np.clip(discretized, 0, 9)
        return str(discretized.tolist())
    
    def get_action(self, observation: np.ndarray) -> int:
        """ε-贪心策略选择动作"""
        state_key = self._state_to_key(observation)
        
        # ε-贪心策略
        if random.random() < self.epsilon:
            # 探索：随机选择动作
            action = random.randint(0, self.action_space_size - 1)
            # print(f"🔍 探索动作: {action}")
        else:
            # 利用：选择Q值最大的动作
            q_values = self.q_table[state_key]
            action = np.argmax(q_values)
            # print(f"🎯 利用动作: {action} (Q值: {q_values[action]:.3f})")
        
        return action
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """Q-Learning更新规则"""
        state_key = self._state_to_key(observation)
        next_state_key = self._state_to_key(next_observation)
        
        # 当前Q值
        current_q = self.q_table[state_key][action]
        
        # 下一状态的最大Q值
        if done:
            next_max_q = 0  # 终止状态
        else:
            next_max_q = np.max(self.q_table[next_state_key])
        
        # Q-Learning更新公式
        # Q(s,a) ← Q(s,a) + α[r + γ·max Q(s',a') - Q(s,a)]
        target = reward + self.discount_factor * next_max_q
        self.q_table[state_key][action] += self.learning_rate * (target - current_q)
        
        # 记录学习过程
        self.total_reward += reward
        
        # 调试信息（可选）
        # print(f"📚 Q更新: Q({state_key},{action}) = {current_q:.3f} → {self.q_table[state_key][action]:.3f}")
    
    def reset(self):
        """回合结束时的处理"""
        self.episode_count += 1
        
        # ε衰减：随着学习进行，减少探索
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        # 每100回合打印学习进度
        if self.episode_count % 100 == 0:
            avg_reward = self.total_reward / 100
            print(f"📈 回合 {self.episode_count}: 平均奖励={avg_reward:.3f}, ε={self.epsilon:.3f}")
            self.total_reward = 0
    
    def get_q_table_stats(self):
        """获取Q表统计信息"""
        if not self.q_table:
            return {"states": 0, "avg_q": 0, "max_q": 0}
        
        all_q_values = []
        for state_q in self.q_table.values():
            all_q_values.extend(state_q)
        
        return {
            "states": len(self.q_table),
            "avg_q": np.mean(all_q_values),
            "max_q": np.max(all_q_values),
            "min_q": np.min(all_q_values)
        }

class DQNAgent(BaseAgent):
    """
    深度Q网络(DQN)智能体
    使用神经网络近似Q函数
    """
    
    def __init__(self, action_space_size: int, state_size: int = 5):
        super().__init__(action_space_size)
        self.state_size = state_size
        
        # 经验回放缓冲区
        self.memory = deque(maxlen=2000)
        self.epsilon = 1.0
        self.epsilon_decay = 0.995
        self.epsilon_min = 0.01
        self.learning_rate = 0.001
        
        # 简化的神经网络（线性近似）
        self.weights = np.random.normal(0, 0.1, (state_size, action_space_size))
        self.bias = np.zeros(action_space_size)
        
        print(f"🧠 DQN智能体初始化（简化版）")
    
    def get_action(self, observation: np.ndarray) -> int:
        """ε-贪心策略"""
        if random.random() < self.epsilon:
            return random.randint(0, self.action_space_size - 1)
        
        # 前向传播
        q_values = np.dot(observation, self.weights) + self.bias
        return np.argmax(q_values)
    
    def update(self, observation: np.ndarray, action: int,
               reward: float, next_observation: np.ndarray, done: bool):
        """经验回放学习"""
        # 存储经验
        self.memory.append((observation, action, reward, next_observation, done))
        
        # 经验回放
        if len(self.memory) > 32:
            self._replay(32)
    
    def _replay(self, batch_size: int):
        """经验回放训练"""
        batch = random.sample(self.memory, batch_size)
        
        for observation, action, reward, next_observation, done in batch:
            target = reward
            if not done:
                next_q_values = np.dot(next_observation, self.weights) + self.bias
                target += 0.95 * np.max(next_q_values)
            
            # 当前Q值
            current_q_values = np.dot(observation, self.weights) + self.bias
            target_q_values = current_q_values.copy()
            target_q_values[action] = target
            
            # 梯度下降更新（简化）
            error = target_q_values - current_q_values
            self.weights += self.learning_rate * np.outer(observation, error)
            self.bias += self.learning_rate * error
    
    def reset(self):
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

def compare_learning_vs_rule_based():
    """比较学习型智能体与基于规则的智能体"""
    print("🔬 强化学习 vs 基于规则的智能体对比")
    print("=" * 80)
    
    env = LearningEnvironment(max_steps=20)
    
    # 创建不同类型的智能体
    agents = {
        'Q-Learning': QLearningAgent(env.action_space_size),
        'DQN': DQNAgent(env.action_space_size),
        'Rule-Based': SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
    }
    
    # 训练阶段（只对学习型智能体）
    print("🏋️ 训练阶段...")
    training_episodes = 500
    
    for episode in range(training_episodes):
        for name, agent in agents.items():
            if name in ['Q-Learning', 'DQN']:  # 只训练学习型智能体
                observation = env.reset()
                agent.reset()
                
                while True:
                    action = agent.get_action(observation)
                    next_observation, reward, done, info = env.step(action)
                    agent.update(observation, action, reward, next_observation, done)
                    observation = next_observation
                    
                    if done:
                        break
    
    print("✅ 训练完成！")
    
    # 测试阶段
    print(f"\n🧪 测试阶段...")
    test_episodes = 50
    results = {}
    
    for name, agent in agents.items():
        episode_rewards = []
        episode_scores = []
        success_count = 0
        
        for episode in range(test_episodes):
            observation = env.reset()
            agent.reset()
            
            total_reward = 0
            
            while True:
                action = agent.get_action(observation)
                observation, reward, done, info = env.step(action)
                total_reward += reward
                
                if done:
                    break
            
            episode_rewards.append(total_reward)
            episode_scores.append(np.mean(observation))
            
            if env._check_completion():
                success_count += 1
        
        results[name] = {
            'avg_reward': np.mean(episode_rewards),
            'avg_score': np.mean(episode_scores),
            'success_rate': success_count / test_episodes,
            'std_reward': np.std(episode_rewards)
        }
    
    # 显示结果
    print(f"\n📊 测试结果对比:")
    print(f"{'智能体':<15} {'平均奖励':<12} {'平均得分':<12} {'成功率':<10} {'奖励标准差':<12}")
    print("-" * 70)
    
    for name, result in results.items():
        print(f"{name:<15} {result['avg_reward']:<12.3f} {result['avg_score']:<12.3f} "
              f"{result['success_rate']:<10.2%} {result['std_reward']:<12.3f}")
    
    # Q-Learning智能体的学习统计
    if 'Q-Learning' in agents:
        q_stats = agents['Q-Learning'].get_q_table_stats()
        print(f"\n🧠 Q-Learning学习统计:")
        print(f"  学习到的状态数: {q_stats['states']}")
        print(f"  平均Q值: {q_stats['avg_q']:.3f}")
        print(f"  最大Q值: {q_stats['max_q']:.3f}")
        print(f"  最小Q值: {q_stats['min_q']:.3f}")
    
    return results

def demonstrate_learning_process():
    """演示学习过程"""
    print(f"\n🎓 强化学习过程演示")
    print("=" * 80)
    
    env = LearningEnvironment(max_steps=15)
    agent = QLearningAgent(env.action_space_size, epsilon=0.3)
    
    print(f"📚 观察Q-Learning智能体的学习过程...")
    
    # 显示前几个回合的详细学习过程
    for episode in range(5):
        print(f"\n--- 回合 {episode + 1} ---")
        observation = env.reset()
        agent.reset()
        
        step = 0
        total_reward = 0
        
        while True:
            step += 1
            state_key = agent._state_to_key(observation)
            q_values = agent.q_table[state_key]
            
            print(f"步骤{step}: 状态={[f'{x:.2f}' for x in observation]}")
            print(f"       Q值={[f'{q:.2f}' for q in q_values]}")
            
            action = agent.get_action(observation)
            next_observation, reward, done, info = env.step(action)
            
            print(f"       动作={action}, 奖励={reward:.3f}")
            
            agent.update(observation, action, reward, next_observation, done)
            total_reward += reward
            observation = next_observation
            
            if done or step >= 10:  # 限制显示步数
                break
        
        print(f"回合奖励: {total_reward:.3f}")
        print(f"当前ε: {agent.epsilon:.3f}")

if __name__ == "__main__":
    # 导入基于规则的智能体
    from simulation_framework_explained import SmartGreedyAgent
    
    print("🤖 真正的强化学习 vs 基于规则的智能体")
    print("=" * 100)
    
    # 演示学习过程
    demonstrate_learning_process()
    
    # 对比不同类型的智能体
    results = compare_learning_vs_rule_based()
    
    print(f"\n🎯 总结:")
    print(f"1. Q-Learning和DQN是真正的强化学习智能体，会从经验中学习")
    print(f"2. 基于规则的智能体使用固定策略，不会学习")
    print(f"3. 强化学习智能体需要训练时间，但可能学到更好的策略")
    print(f"4. 基于规则的智能体立即可用，但策略固定")
