.data-preprocessing-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.preprocessing-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 32px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.preprocessing-header h2 {
  color: #1890ff;
  margin-bottom: 16px;
}

.preprocessing-flow-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.preprocessing-flow-card .ant-steps-item-title {
  font-weight: 600;
}

.preprocessing-flow-card .ant-steps-item-description {
  color: #666;
}

.preprocessing-actions {
  margin-top: 32px;
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.start-button,
.proceed-button {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 24px;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  border: none;
  transition: all 0.3s;
}

.start-button:hover,
.proceed-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
}

.processing-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.data-stats-card,
.processing-details-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.data-stats-card .ant-statistic-title {
  font-size: 12px;
  color: #666;
}

.data-stats-card .ant-statistic-content {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.processing-details-card .ant-table-tbody > tr > td {
  padding: 8px 12px;
}

.preprocessing-info-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.info-item {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  height: 100%;
}

.info-item h5 {
  color: #1890ff;
  margin-bottom: 12px !important;
}

.info-item .ant-typography {
  margin-bottom: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

/* 步骤样式优化 */
.ant-steps-vertical .ant-steps-item-content {
  min-height: 80px;
}

.ant-steps-item-process .ant-steps-item-icon {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-steps-item-finish .ant-steps-item-icon {
  background: #52c41a;
  border-color: #52c41a;
}

.ant-steps-item-process .ant-steps-item-icon .ant-steps-icon {
  color: white;
}

.ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-preprocessing-container {
    padding: 16px;
  }
  
  .preprocessing-header {
    padding: 24px 16px;
  }
  
  .ant-steps-vertical {
    margin: 0 -8px;
  }
  
  .processing-actions {
    margin-top: 24px;
  }
  
  .start-button,
  .proceed-button {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .info-item {
    margin-bottom: 16px;
  }
}
