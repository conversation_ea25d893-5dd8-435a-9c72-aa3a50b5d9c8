# 快速内网穿透脚本 - 一键启动并配置
# 使用方法: .\quick-expose.ps1

Write-Host "🚀 ORCDF项目内网穿透快速启动" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

# 检查必要工具
function Test-ToolInstalled {
    param($ToolName)
    try {
        & $ToolName --version 2>$null | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# 检查服务状态
function Test-ServiceRunning {
    param($Port, $ServiceName)
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 3 -ErrorAction Stop
        Write-Host "✅ $ServiceName 运行正常 (端口$Port)" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $ServiceName 未运行 (端口$Port)" -ForegroundColor Red
        return $false
    }
}

# 步骤1: 检查工具安装
Write-Host "📋 步骤1: 检查工具安装" -ForegroundColor Blue
$ngrokInstalled = Test-ToolInstalled "ngrok"
$cpolarInstalled = Test-ToolInstalled "cpolar"

if (-not $ngrokInstalled -and -not $cpolarInstalled) {
    Write-Host "❌ 未检测到内网穿透工具" -ForegroundColor Red
    Write-Host "请安装以下工具之一:" -ForegroundColor Yellow
    Write-Host "- ngrok: https://ngrok.com/" -ForegroundColor White
    Write-Host "- cpolar: https://www.cpolar.com/" -ForegroundColor White
    exit 1
}

$selectedTool = if ($ngrokInstalled) { "ngrok" } else { "cpolar" }
Write-Host "✅ 检测到工具: $selectedTool" -ForegroundColor Green

# 步骤2: 检查服务状态
Write-Host "`n📋 步骤2: 检查服务状态" -ForegroundColor Blue
$frontendRunning = Test-ServiceRunning -Port 3000 -ServiceName "前端服务"
$backendRunning = Test-ServiceRunning -Port 8000 -ServiceName "后端服务"

if (-not $frontendRunning -or -not $backendRunning) {
    Write-Host "`n⚠️  请先启动所有服务:" -ForegroundColor Yellow
    if (-not $backendRunning) {
        Write-Host "启动后端: cd design_platform/backend && uvicorn app.main:app --reload --host 0.0.0.0" -ForegroundColor White
    }
    if (-not $frontendRunning) {
        Write-Host "启动前端: cd design_platform/frontend && npm start" -ForegroundColor White
    }
    
    $continue = Read-Host "`n是否继续配置内网穿透? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 0
    }
}

# 步骤3: 启动内网穿透
Write-Host "`n📋 步骤3: 启动内网穿透" -ForegroundColor Blue
Write-Host "正在启动 $selectedTool..." -ForegroundColor Yellow

# 启动后端穿透
Write-Host "启动后端穿透 (端口8000)..." -ForegroundColor Cyan
if ($selectedTool -eq "ngrok") {
    Start-Process -FilePath "ngrok" -ArgumentList "http", "8000" -WindowStyle Normal
} else {
    Start-Process -FilePath "cpolar" -ArgumentList "http", "8000" -WindowStyle Normal
}

Start-Sleep -Seconds 3

# 启动前端穿透
Write-Host "启动前端穿透 (端口3000)..." -ForegroundColor Cyan
if ($selectedTool -eq "ngrok") {
    Start-Process -FilePath "ngrok" -ArgumentList "http", "3000" -WindowStyle Normal
} else {
    Start-Process -FilePath "cpolar" -ArgumentList "http", "3000" -WindowStyle Normal
}

# 步骤4: 等待用户输入地址
Write-Host "`n📋 步骤4: 配置API地址" -ForegroundColor Blue
Write-Host "请查看 $selectedTool 窗口，获取后端服务的公网地址" -ForegroundColor Yellow

if ($selectedTool -eq "ngrok") {
    Write-Host "地址格式类似: https://abc123.ngrok.io" -ForegroundColor Gray
} else {
    Write-Host "地址格式类似: https://abc123.cpolar.top" -ForegroundColor Gray
    Write-Host "也可访问 https://dashboard.cpolar.com/ 查看" -ForegroundColor Gray
}

$backendUrl = Read-Host "`n请输入后端的完整公网地址"

if (-not $backendUrl) {
    Write-Host "❌ 未输入地址，退出配置" -ForegroundColor Red
    exit 1
}

# 验证URL格式
if (-not ($backendUrl -match "^https?://")) {
    Write-Host "❌ 无效的URL格式" -ForegroundColor Red
    exit 1
}

# 步骤5: 更新配置
Write-Host "`n📋 步骤5: 更新前端配置" -ForegroundColor Blue
Write-Host "正在更新API配置..." -ForegroundColor Yellow

# 调用配置更新脚本
& ".\update-api-config.ps1" -BackendUrl $backendUrl

# 步骤6: 完成提示
Write-Host "`n🎉 配置完成!" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

Write-Host "`n📋 下一步操作:" -ForegroundColor Magenta
Write-Host "1. 重启前端服务 (Ctrl+C 停止，然后 npm start)" -ForegroundColor White
Write-Host "2. 获取前端的公网地址" -ForegroundColor White
Write-Host "3. 将前端地址分享给组员" -ForegroundColor White

Write-Host "`n🔗 服务地址:" -ForegroundColor Yellow
Write-Host "后端API: $backendUrl" -ForegroundColor White
Write-Host "前端地址: 请查看 $selectedTool 窗口" -ForegroundColor White

Write-Host "`n⚠️  注意事项:" -ForegroundColor Red
Write-Host "- 保持内网穿透工具运行" -ForegroundColor White
Write-Host "- 如果地址变化，需要重新配置" -ForegroundColor White
Write-Host "- 使用完毕后运行 .\restore-config.ps1 恢复配置" -ForegroundColor White

Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
