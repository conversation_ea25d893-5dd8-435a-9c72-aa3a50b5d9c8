#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新严格审视AFM分数的含义
"""

import numpy as np
import pickle
from scipy.sparse import vstack

def examine_training_process():
    """重新审视训练过程"""
    print("=== 重新审视AFM训练过程 ===")
    
    print("🔍 AFM模型训练的实际过程：")
    print("1. 输入：(用户嵌入, 物品嵌入)")
    print("2. 输出：一个概率值")
    print("3. 标签：0或1（二分类）")
    print("4. 损失函数：二分类交叉熵")
    
    print("\n❓ 关键问题：")
    print("- 用户嵌入代表什么？")
    print("- 物品嵌入代表什么？")
    print("- 标签1代表什么事件发生了？")
    print("- 标签0代表什么事件没发生？")

def analyze_embedding_source():
    """分析嵌入的来源"""
    print("\n=== 分析嵌入来源 ===")
    
    try:
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings = np.array(pickle.load(f))
        with open('itm_emb_np.pkl', 'rb') as f:
            item_embeddings = np.array(pickle.load(f))
        
        # 处理维度
        if len(user_embeddings.shape) == 3:
            user_embeddings = user_embeddings.reshape(user_embeddings.shape[0], -1)
        if len(item_embeddings.shape) == 3:
            item_embeddings = item_embeddings.reshape(item_embeddings.shape[0], -1)
        
        print(f"🔍 嵌入特征分析：")
        print(f"用户嵌入：")
        print(f"  数量: {len(user_embeddings)}")
        print(f"  维度: {user_embeddings.shape[1]}")
        print(f"  数值范围: [{user_embeddings.min():.6f}, {user_embeddings.max():.6f}]")
        print(f"  均值: {user_embeddings.mean():.6f}")
        print(f"  标准差: {user_embeddings.std():.6f}")
        
        print(f"\n物品嵌入：")
        print(f"  数量: {len(item_embeddings)}")
        print(f"  维度: {item_embeddings.shape[1]}")
        print(f"  数值范围: [{item_embeddings.min():.6f}, {item_embeddings.max():.6f}]")
        print(f"  均值: {item_embeddings.mean():.6f}")
        print(f"  标准差: {item_embeddings.std():.6f}")
        
        # 关键观察
        print(f"\n🤔 关键观察：")
        print(f"1. 用户和物品嵌入的统计特征几乎相同")
        print(f"2. 都是102维的密集向量")
        print(f"3. 数值分布都接近标准正态分布")
        print(f"4. 这些嵌入很可能来自同一个预训练模型")
        
        # 检查是否有相同的嵌入
        print(f"\n🔍 检查嵌入的唯一性：")
        user_unique = len(np.unique(user_embeddings.view(np.void), axis=0))
        item_unique = len(np.unique(item_embeddings.view(np.void), axis=0))
        
        print(f"唯一用户嵌入: {user_unique} / {len(user_embeddings)}")
        print(f"唯一物品嵌入: {item_unique} / {len(item_embeddings)}")
        
        if user_unique == len(user_embeddings):
            print("✅ 每个用户都有唯一的嵌入")
        else:
            print("❌ 存在重复的用户嵌入")
            
        if item_unique == len(item_embeddings):
            print("✅ 每个物品都有唯一的嵌入")
        else:
            print("❌ 存在重复的物品嵌入")
            
        return user_embeddings, item_embeddings
        
    except Exception as e:
        print(f"❌ 嵌入分析失败: {e}")
        return None, None

def analyze_interaction_data():
    """分析交互数据的真实含义"""
    print("\n=== 分析交互数据 ===")
    
    try:
        # 加载交互数据
        with open('trn_mat.pkl', 'rb') as f:
            trn_mat = pickle.load(f)
        
        print(f"训练数据形状: {trn_mat.shape}")
        print(f"数据类型: {type(trn_mat)}")
        print(f"非零元素: {trn_mat.nnz}")
        
        # 分析数据的结构
        print(f"\n🔍 数据结构分析：")
        
        # 检查前几行的数据
        print(f"前5行数据的非零元素位置：")
        for i in range(min(5, trn_mat.shape[0])):
            row = trn_mat.getrow(i)
            nonzero_cols = row.nonzero()[1]
            nonzero_values = row.data
            print(f"  行{i}: 列{nonzero_cols[:10]} = {nonzero_values[:10]}")
        
        # 分析最后一列（标签列）
        last_col = trn_mat.getcol(-1)
        print(f"\n标签列分析：")
        print(f"  非零元素数: {last_col.nnz}")
        print(f"  唯一值: {np.unique(last_col.data)}")
        
        # 这里是关键：理解数据的真实含义
        print(f"\n❓ 关键问题：")
        print(f"1. 每一行代表什么？")
        print(f"   - 一次具体的学习交互？")
        print(f"   - 一个用户-物品对？")
        print(f"   - 一次答题记录？")
        
        print(f"\n2. 标签=1代表什么？")
        print(f"   - 答题正确？")
        print(f"   - 学习成功？")
        print(f"   - 任务完成？")
        
        print(f"\n3. 为什么正样本比例这么低？")
        print(f"   - 题目太难？")
        print(f"   - 评估标准严格？")
        print(f"   - 数据收集方式特殊？")
        
        return trn_mat
        
    except Exception as e:
        print(f"❌ 交互数据分析失败: {e}")
        return None

def critical_thinking_about_afm_score():
    """批判性思考AFM分数的含义"""
    print("\n=== 批判性思考AFM分数含义 ===")
    
    print("🤔 让我们重新思考：")
    
    print("\n假设1: AFM分数 = 当前掌握程度")
    print("  支持证据:")
    print("  - 分数在0-1之间，像概率")
    print("  - 不同用户对同一知识点分数不同")
    print("  - 可以用来评估能力")
    print("  反对证据:")
    print("  - 训练时没有'掌握程度'标签")
    print("  - 训练标签是二分类的成功/失败")
    print("  - 掌握程度应该是连续值，不是0/1")
    
    print("\n假设2: AFM分数 = 答题正确概率")
    print("  支持证据:")
    print("  - 训练数据可能是答题记录")
    print("  - 标签1=答对，0=答错")
    print("  - 符合二分类训练过程")
    print("  反对证据:")
    print("  - 正样本比例太低(0.01%)")
    print("  - 正常答题正确率不会这么低")
    
    print("\n假设3: AFM分数 = 学习成功概率")
    print("  支持证据:")
    print("  - 低正样本比例符合学习的挑战性")
    print("  - 标签1=学习成功，0=学习失败")
    print("  - 可以用于学习路径规划")
    print("  反对证据:")
    print("  - '学习成功'定义模糊")
    print("  - 难以客观量化")
    
    print("\n假设4: AFM分数 = 特定事件发生概率")
    print("  支持证据:")
    print("  - 训练过程就是预测事件发生概率")
    print("  - 标签1=事件发生，0=事件未发生")
    print("  - 但我们不知道这个'事件'具体是什么")
    print("  问题:")
    print("  - 关键在于理解训练数据中的'事件'是什么")

def reexamine_agent_usage():
    """重新审视Agent中的使用方式"""
    print("\n=== 重新审视Agent使用方式 ===")
    
    print("🔍 当前Agent的使用逻辑：")
    print("1. AFM.predict(用户, 知识点) → 分数")
    print("2. 将分数解释为'学习效果预测'")
    print("3. 结合掌握需求进行决策")
    
    print("\n❓ 关键问题：")
    print("1. 我们有权利将AFM分数解释为'学习效果'吗？")
    print("2. 如果AFM训练时的标签不是'学习效果'，")
    print("   那么用它预测'学习效果'是否合理？")
    print("3. 这种使用方式是否存在概念偷换？")
    
    print("\n🎯 可能的情况：")
    print("情况A: AFM分数确实反映学习相关的能力")
    print("  - 即使训练标签不是'掌握程度'")
    print("  - 但学到的模式可以泛化到学习预测")
    print("  - 这是合理的迁移学习")
    
    print("\n情况B: AFM分数与学习能力无关")
    print("  - 训练数据是其他类型的交互")
    print("  - 用于学习预测是错误的")
    print("  - 需要重新训练专门的学习预测模型")
    
    print("\n情况C: AFM分数部分相关")
    print("  - 有一定的相关性但不完全对应")
    print("  - 可以作为特征但需要额外处理")
    print("  - 需要校准或转换")

def main():
    """主分析函数"""
    print("🔍 重新严格审视AFM分数的含义")
    print("=" * 60)
    
    # 重新审视训练过程
    examine_training_process()
    
    # 分析嵌入来源
    user_emb, item_emb = analyze_embedding_source()
    
    # 分析交互数据
    trn_mat = analyze_interaction_data()
    
    # 批判性思考
    critical_thinking_about_afm_score()
    
    # 重新审视使用方式
    reexamine_agent_usage()
    
    print("\n" + "=" * 60)
    print("🎯 诚实的结论：")
    print("我必须承认，仅从现有信息无法100%确定AFM分数的确切含义。")
    print("")
    print("我们知道的是：")
    print("1. AFM模型基于(用户嵌入, 物品嵌入)预测二分类标签")
    print("2. 训练数据有100万+交互记录，正样本比例极低")
    print("3. 模型输出0-1之间的概率值")
    print("")
    print("我们不确定的是：")
    print("1. 训练标签的确切含义（答题？学习？其他？）")
    print("2. 用户和物品嵌入的具体来源和含义")
    print("3. AFM分数是否真的代表'掌握程度'")
    print("")
    print("建议：")
    print("1. 查看原始数据的文档或论文")
    print("2. 分析数据收集的具体过程")
    print("3. 验证AFM分数与真实学习效果的相关性")

if __name__ == "__main__":
    main()
