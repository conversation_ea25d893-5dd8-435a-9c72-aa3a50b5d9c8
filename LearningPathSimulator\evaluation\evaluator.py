"""
评估器实现

提供完整的智能体性能评估功能。
"""

import numpy as np
import time
import json
from typing import Dict, List, Any, Optional, Callable
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

from ..core.base import BaseAgent, BaseEnvironment, BaseEvaluator
from .metrics import MetricCalculator
from .statistics import StatisticalAnalyzer


class Evaluator(BaseEvaluator):
    """标准评估器"""
    
    def __init__(self, 
                 metric_calculator: Optional[MetricCalculator] = None,
                 statistical_analyzer: Optional[StatisticalAnalyzer] = None,
                 save_detailed_logs: bool = False,
                 random_seed: Optional[int] = None):
        """
        初始化评估器
        
        Args:
            metric_calculator: 指标计算器
            statistical_analyzer: 统计分析器
            save_detailed_logs: 是否保存详细日志
            random_seed: 随机种子
        """
        config = {
            'save_detailed_logs': save_detailed_logs,
            'random_seed': random_seed
        }
        super().__init__(config)
        
        self.metric_calculator = metric_calculator or MetricCalculator()
        self.statistical_analyzer = statistical_analyzer or StatisticalAnalyzer()
        self.save_detailed_logs = save_detailed_logs
        
        if random_seed is not None:
            np.random.seed(random_seed)
    
    def evaluate(self, agent: BaseAgent, environment: BaseEnvironment, 
                num_episodes: int, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        评估智能体性能
        
        Args:
            agent: 待评估的智能体
            environment: 评估环境
            num_episodes: 评估回合数
            progress_callback: 进度回调函数
            
        Returns:
            评估结果
        """
        print(f"开始评估智能体 {agent.name}，共 {num_episodes} 回合...")
        
        episode_data = []
        start_time = time.time()
        
        for episode in range(num_episodes):
            episode_result = self._run_single_episode(agent, environment, episode)
            episode_data.append(episode_result)
            
            # 进度回调
            if progress_callback:
                progress_callback(episode + 1, num_episodes, episode_result)
            
            # 进度显示
            if (episode + 1) % max(1, num_episodes // 10) == 0:
                progress = (episode + 1) / num_episodes * 100
                print(f"进度: {progress:.1f}% ({episode + 1}/{num_episodes})")
        
        evaluation_time = time.time() - start_time
        
        # 计算指标
        metrics = self.metric_calculator.calculate_all_metrics(episode_data)
        
        # 计算置信区间
        final_scores = [ep['final_score'] for ep in episode_data]
        rewards = [ep['total_reward'] for ep in episode_data]
        
        score_ci = self.statistical_analyzer.ci_calculator.calculate_mean_ci(final_scores)
        reward_ci = self.statistical_analyzer.ci_calculator.calculate_mean_ci(rewards)
        
        # 成功率置信区间
        success_count = sum(1 for ep in episode_data if ep.get('success', False))
        success_ci = self.statistical_analyzer.ci_calculator.calculate_proportion_ci(
            success_count, num_episodes
        )
        
        # 汇总结果
        result = {
            'agent_name': agent.name,
            'num_episodes': num_episodes,
            'evaluation_time': evaluation_time,
            'metrics': metrics,
            'confidence_intervals': {
                'final_score': score_ci,
                'total_reward': reward_ci,
                'success_rate': success_ci
            },
            'raw_data': episode_data if self.save_detailed_logs else None,
            'summary_statistics': self._calculate_summary_statistics(episode_data),
            'agent_info': agent.get_info()
        }
        
        print(f"评估完成！用时 {evaluation_time:.2f} 秒")
        return result
    
    def _run_single_episode(self, agent: BaseAgent, environment: BaseEnvironment, 
                           episode_num: int) -> Dict[str, Any]:
        """运行单个评估回合"""
        observation = environment.reset()
        agent.reset()
        
        total_reward = 0.0
        step_count = 0
        learning_curve = []
        action_sequence = []
        reward_sequence = []
        
        episode_start_time = time.time()
        
        while True:
            # 智能体决策
            action, decision_time = agent.get_action_with_timing(observation)
            action_sequence.append(action)
            
            # 环境响应
            next_observation, reward, done, info = environment.step(action)
            reward_sequence.append(reward)
            
            # 智能体更新
            agent.update(observation, action, reward, next_observation, done)
            
            # 记录数据
            total_reward += reward
            step_count += 1
            learning_curve.append(np.mean(next_observation))
            
            observation = next_observation
            
            if done:
                break
        
        episode_time = time.time() - episode_start_time
        final_score = np.mean(observation)
        success = environment._check_completion() if hasattr(environment, '_check_completion') else False
        
        return {
            'episode': episode_num,
            'total_reward': total_reward,
            'final_score': final_score,
            'steps': step_count,
            'success': success,
            'episode_time': episode_time,
            'learning_curve': learning_curve,
            'action_sequence': action_sequence if self.save_detailed_logs else None,
            'reward_sequence': reward_sequence if self.save_detailed_logs else None,
            'final_state': observation.tolist(),
            'environment_info': environment.get_state_info()
        }
    
    def _calculate_summary_statistics(self, episode_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算汇总统计信息"""
        if not episode_data:
            return {}
        
        final_scores = [ep['final_score'] for ep in episode_data]
        rewards = [ep['total_reward'] for ep in episode_data]
        steps = [ep['steps'] for ep in episode_data]
        times = [ep['episode_time'] for ep in episode_data]
        
        return {
            'final_score': {
                'mean': np.mean(final_scores),
                'std': np.std(final_scores),
                'min': np.min(final_scores),
                'max': np.max(final_scores),
                'median': np.median(final_scores),
                'q25': np.percentile(final_scores, 25),
                'q75': np.percentile(final_scores, 75)
            },
            'total_reward': {
                'mean': np.mean(rewards),
                'std': np.std(rewards),
                'min': np.min(rewards),
                'max': np.max(rewards),
                'median': np.median(rewards)
            },
            'steps': {
                'mean': np.mean(steps),
                'std': np.std(steps),
                'min': np.min(steps),
                'max': np.max(steps),
                'median': np.median(steps)
            },
            'episode_time': {
                'mean': np.mean(times),
                'std': np.std(times),
                'total': np.sum(times)
            }
        }
    
    def compare_agents(self, agents: List[BaseAgent], environment: BaseEnvironment,
                      num_episodes: int, save_results: bool = True) -> Dict[str, Any]:
        """
        比较多个智能体的性能
        
        Args:
            agents: 智能体列表
            environment: 评估环境
            num_episodes: 每个智能体的评估回合数
            save_results: 是否保存结果到文件
            
        Returns:
            比较结果
        """
        print(f"开始比较 {len(agents)} 个智能体...")
        
        # 评估所有智能体
        agent_results = {}
        for agent in agents:
            result = self.evaluate(agent, environment, num_episodes)
            agent_results[agent.name] = result
        
        # 提取性能数据进行统计比较
        performance_data = {}
        for agent_name, result in agent_results.items():
            performance_data[agent_name] = [
                ep['final_score'] for ep in result['raw_data'] or []
            ]
        
        # 统计比较
        if len(performance_data) >= 2:
            statistical_comparison = self.statistical_analyzer.compare_multiple_groups(performance_data)
        else:
            statistical_comparison = None
        
        # 性能排名
        ranking = self._rank_agents(agent_results)
        
        # 汇总比较结果
        comparison_result = {
            'num_agents': len(agents),
            'num_episodes_per_agent': num_episodes,
            'agent_results': agent_results,
            'statistical_comparison': statistical_comparison,
            'ranking': ranking,
            'summary': self._generate_comparison_summary(ranking, statistical_comparison)
        }
        
        # 保存结果
        if save_results:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"agent_comparison_{timestamp}.json"
            self.save_results(comparison_result, filename)
            print(f"比较结果已保存到 {filename}")
        
        return comparison_result
    
    def _rank_agents(self, agent_results: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """对智能体进行排名"""
        ranking = []
        
        for agent_name, result in agent_results.items():
            metrics = result['metrics']
            ranking.append({
                'agent_name': agent_name,
                'final_score': metrics.get('LearningEffectiveness', 0),
                'success_rate': metrics.get('SuccessRate', 0),
                'efficiency': metrics.get('LearningEfficiency', 0),
                'stability': 1.0 / (1.0 + metrics.get('Stability', 1)),  # 转换为正向指标
                'composite_score': self.metric_calculator.calculate_composite_score(
                    result['raw_data'] or []
                )
            })
        
        # 按综合得分排序
        ranking.sort(key=lambda x: x['composite_score'], reverse=True)
        
        # 添加排名
        for i, agent_info in enumerate(ranking):
            agent_info['rank'] = i + 1
        
        return ranking
    
    def _generate_comparison_summary(self, ranking: List[Dict[str, Any]], 
                                   statistical_comparison: Optional[Dict[str, Any]]) -> str:
        """生成比较总结"""
        if not ranking:
            return "无智能体参与比较"
        
        summary = f"智能体性能排名（共{len(ranking)}个）：\n"
        
        for i, agent_info in enumerate(ranking, 1):
            name = agent_info['agent_name']
            score = agent_info['composite_score']
            summary += f"{i}. {name}: {score:.4f}\n"
        
        if statistical_comparison and len(ranking) >= 2:
            summary += f"\n统计分析：\n"
            summary += statistical_comparison.get('summary', '无统计分析结果')
        
        return summary
    
    def save_results(self, results: Dict[str, Any], filename: str):
        """保存评估结果到JSON文件"""
        try:
            # 处理不可序列化的对象
            serializable_results = self._make_serializable(results)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存结果时出错: {e}")
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        else:
            return obj


class ParallelEvaluator(Evaluator):
    """并行评估器：支持多进程并行评估"""
    
    def __init__(self, num_processes: Optional[int] = None, **kwargs):
        super().__init__(**kwargs)
        self.num_processes = num_processes or mp.cpu_count()
    
    def evaluate(self, agent: BaseAgent, environment: BaseEnvironment, 
                num_episodes: int, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """并行评估智能体性能"""
        print(f"开始并行评估智能体 {agent.name}，使用 {self.num_processes} 个进程...")
        
        # 将回合分配给不同进程
        episodes_per_process = num_episodes // self.num_processes
        remaining_episodes = num_episodes % self.num_processes
        
        process_episodes = [episodes_per_process] * self.num_processes
        for i in range(remaining_episodes):
            process_episodes[i] += 1
        
        start_time = time.time()
        all_episode_data = []
        
        # 使用进程池执行评估
        with ProcessPoolExecutor(max_workers=self.num_processes) as executor:
            futures = []
            
            for i, episodes in enumerate(process_episodes):
                if episodes > 0:
                    future = executor.submit(
                        self._evaluate_episodes_batch,
                        agent, environment, episodes, i
                    )
                    futures.append(future)
            
            # 收集结果
            completed = 0
            for future in as_completed(futures):
                try:
                    batch_data = future.result()
                    all_episode_data.extend(batch_data)
                    completed += len(batch_data)
                    
                    if progress_callback:
                        progress_callback(completed, num_episodes, None)
                    
                    progress = completed / num_episodes * 100
                    print(f"并行进度: {progress:.1f}% ({completed}/{num_episodes})")
                    
                except Exception as e:
                    print(f"并行评估出错: {e}")
        
        evaluation_time = time.time() - start_time
        
        # 后续处理与标准评估器相同
        metrics = self.metric_calculator.calculate_all_metrics(all_episode_data)
        
        # 其余处理逻辑...
        result = {
            'agent_name': agent.name,
            'num_episodes': num_episodes,
            'evaluation_time': evaluation_time,
            'metrics': metrics,
            'raw_data': all_episode_data if self.save_detailed_logs else None,
            'summary_statistics': self._calculate_summary_statistics(all_episode_data),
            'agent_info': agent.get_info(),
            'parallel_info': {
                'num_processes': self.num_processes,
                'episodes_per_process': process_episodes
            }
        }
        
        print(f"并行评估完成！用时 {evaluation_time:.2f} 秒")
        return result
    
    def _evaluate_episodes_batch(self, agent: BaseAgent, environment: BaseEnvironment,
                                num_episodes: int, process_id: int) -> List[Dict[str, Any]]:
        """在单个进程中评估一批回合"""
        # 注意：这里需要重新创建agent和environment的副本
        # 因为多进程间不能共享对象
        
        episode_data = []
        
        for episode in range(num_episodes):
            episode_result = self._run_single_episode(agent, environment, episode)
            episode_data.append(episode_result)
        
        return episode_data
