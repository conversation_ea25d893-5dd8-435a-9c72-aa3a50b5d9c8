"""
Learning Path Simulator

一个基于认知科学理论的学习路径规划算法评估仿真平台。

主要特性:
- 🧠 基于认知科学理论的数学建模
- 📊 严格的统计分析和显著性检验
- 🎯 多维度性能评估指标
- 🔄 高度可扩展的架构设计
- 🚀 支持大规模并行仿真

快速开始:
```python
from LearningPathSimulator import LearningEnvironment, GreedyAgent, Evaluator

# 创建环境
env = LearningEnvironment(num_knowledge_points=5)

# 创建智能体
agent = GreedyAgent(env.action_space_size)

# 评估性能
evaluator = Evaluator()
result = evaluator.evaluate(agent, env, num_episodes=100)

print(f"平均得分: {result['metrics']['LearningEffectiveness']:.3f}")
```
"""

# 版本信息
__version__ = "1.0.0"
__author__ = "Learning Path Simulator Team"
__email__ = "<EMAIL>"
__description__ = "A cognitive science-based learning path planning algorithm evaluation platform"

# 核心模块导入
from .core.base import BaseAgent, BaseEnvironment
from .core.mdp import MarkovDecisionProcess, MDPState
from .core.cognitive_models import (
    EbbinghausForgettingCurve,
    VygotskyZPD,
    SwellerCognitiveLoad,
    MetacognitionModel,
    SpacingEffectModel,
    FlowStateModel,
    MotivationModel
)

# 环境模块
from .environments.learning_env import (
    LearningEnvironment,
    AdvancedLearningEnvironment,
    LearnerProfile
)

# 智能体模块
from .agents.rule_based import (
    RandomAgent,
    GreedyAgent,
    SmartGreedyAgent,
    ZPDAgent,
    ProgressBalancedAgent,
    MultiCriteriaAgent,
    AdaptiveGreedyAgent
)

# 评估模块
from .evaluation.evaluator import Evaluator, ParallelEvaluator
from .evaluation.metrics import (
    MetricCalculator,
    LearningEfficiencyMetric,
    LearningEffectivenessMetric,
    StabilityMetric,
    SuccessRateMetric
)
from .evaluation.statistics import (
    StatisticalAnalyzer,
    WelchTTest,
    CohenDEffect,
    BonferroniCorrection,
    ConfidenceInterval
)

# 公开API
__all__ = [
    # 版本信息
    '__version__',
    '__author__',
    '__description__',
    
    # 核心类
    'BaseAgent',
    'BaseEnvironment',
    'MarkovDecisionProcess',
    'MDPState',
    
    # 认知模型
    'EbbinghausForgettingCurve',
    'VygotskyZPD',
    'SwellerCognitiveLoad',
    'MetacognitionModel',
    'SpacingEffectModel',
    'FlowStateModel',
    'MotivationModel',
    
    # 环境
    'LearningEnvironment',
    'AdvancedLearningEnvironment',
    'LearnerProfile',
    
    # 智能体
    'RandomAgent',
    'GreedyAgent',
    'SmartGreedyAgent',
    'ZPDAgent',
    'ProgressBalancedAgent',
    'MultiCriteriaAgent',
    'AdaptiveGreedyAgent',
    
    # 评估
    'Evaluator',
    'ParallelEvaluator',
    'MetricCalculator',
    'StatisticalAnalyzer',
    
    # 指标
    'LearningEfficiencyMetric',
    'LearningEffectivenessMetric',
    'StabilityMetric',
    'SuccessRateMetric',
    
    # 统计
    'WelchTTest',
    'CohenDEffect',
    'BonferroniCorrection',
    'ConfidenceInterval'
]

# 便捷函数
def quick_evaluate(agent_class, environment_config=None, num_episodes=100, **agent_kwargs):
    """
    快速评估智能体性能的便捷函数
    
    Args:
        agent_class: 智能体类
        environment_config: 环境配置字典
        num_episodes: 评估回合数
        **agent_kwargs: 智能体初始化参数
        
    Returns:
        评估结果字典
    """
    # 默认环境配置
    if environment_config is None:
        environment_config = {
            'num_knowledge_points': 5,
            'learning_rate': 0.3,
            'success_threshold': 0.6,
            'min_success_kps': 3,
            'max_steps': 30
        }
    
    # 创建环境
    env = LearningEnvironment(**environment_config)
    
    # 创建智能体
    agent = agent_class(env.action_space_size, **agent_kwargs)
    
    # 创建评估器
    evaluator = Evaluator()
    
    # 执行评估
    result = evaluator.evaluate(agent, env, num_episodes)
    
    return result


def compare_agents_quick(agent_configs, environment_config=None, num_episodes=50):
    """
    快速比较多个智能体的便捷函数
    
    Args:
        agent_configs: 智能体配置列表，每个元素为(agent_class, kwargs)
        environment_config: 环境配置字典
        num_episodes: 每个智能体的评估回合数
        
    Returns:
        比较结果字典
    """
    # 默认环境配置
    if environment_config is None:
        environment_config = {
            'num_knowledge_points': 5,
            'learning_rate': 0.3,
            'success_threshold': 0.6,
            'min_success_kps': 3,
            'max_steps': 30
        }
    
    # 创建环境
    env = LearningEnvironment(**environment_config)
    
    # 创建智能体列表
    agents = []
    for agent_class, kwargs in agent_configs:
        agent = agent_class(env.action_space_size, **kwargs)
        agents.append(agent)
    
    # 创建评估器
    evaluator = Evaluator()
    
    # 执行比较
    result = evaluator.compare_agents(agents, env, num_episodes)
    
    return result


def get_default_agents(action_space_size):
    """
    获取默认的智能体集合用于比较
    
    Args:
        action_space_size: 动作空间大小
        
    Returns:
        智能体列表
    """
    return [
        RandomAgent(action_space_size),
        GreedyAgent(action_space_size),
        SmartGreedyAgent(action_space_size),
        ZPDAgent(action_space_size),
        ProgressBalancedAgent(action_space_size),
        MultiCriteriaAgent(action_space_size)
    ]


def run_benchmark(environment_config=None, num_episodes=100, save_results=True):
    """
    运行标准基准测试
    
    Args:
        environment_config: 环境配置
        num_episodes: 评估回合数
        save_results: 是否保存结果
        
    Returns:
        基准测试结果
    """
    print("🚀 运行学习路径规划算法基准测试")
    print("=" * 60)
    
    # 默认环境配置
    if environment_config is None:
        environment_config = {
            'num_knowledge_points': 5,
            'learning_rate': 0.3,
            'success_threshold': 0.6,
            'min_success_kps': 3,
            'max_steps': 30
        }
    
    # 创建环境
    env = LearningEnvironment(**environment_config)
    print(f"📚 环境: {env.num_kps}个知识点，目标{env.min_success_kps}个达到{env.success_threshold}")
    
    # 获取默认智能体
    agents = get_default_agents(env.action_space_size)
    print(f"🤖 智能体: {[agent.name for agent in agents]}")
    
    # 执行比较
    evaluator = Evaluator()
    result = evaluator.compare_agents(agents, env, num_episodes, save_results)
    
    # 显示结果
    print(f"\n🏆 基准测试结果:")
    for agent_info in result['ranking']:
        name = agent_info['agent_name']
        score = agent_info['composite_score']
        print(f"  {agent_info['rank']}. {name}: {score:.4f}")
    
    return result


# 模块级别的配置
class Config:
    """全局配置类"""
    
    # 默认随机种子
    DEFAULT_RANDOM_SEED = 42
    
    # 默认评估参数
    DEFAULT_NUM_EPISODES = 100
    DEFAULT_CONFIDENCE_LEVEL = 0.95
    DEFAULT_SIGNIFICANCE_LEVEL = 0.05
    
    # 默认环境参数
    DEFAULT_ENVIRONMENT_CONFIG = {
        'num_knowledge_points': 5,
        'learning_rate': 0.3,
        'forgetting_rate': 0.02,
        'success_threshold': 0.6,
        'min_success_kps': 3,
        'max_steps': 30
    }
    
    # 性能优化参数
    PARALLEL_THRESHOLD = 1000  # 超过此回合数时使用并行评估
    MAX_PROCESSES = None       # 最大进程数，None表示使用CPU核心数


# 添加配置到公开API
__all__.append('Config')

# 模块初始化信息
print(f"Learning Path Simulator v{__version__} loaded successfully! 🎉")
print(f"Use help(LearningPathSimulator) for detailed documentation.")
