#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证真实AFM模型是否正确工作
"""

import numpy as np
import torch
from optimized_simulator import RealAFMAgent

def test_afm_predictions():
    """测试AFM模型的预测是否有差异性"""
    print("=== 验证真实AFM模型预测功能 ===")
    
    # 创建AFM智能体
    agent = RealAFMAgent(action_space_size=5, user_id=0)
    
    if agent.model is None:
        print("❌ AFM模型未加载，无法进行测试")
        return False
    
    print(f"✅ AFM模型加载成功")
    
    # 测试不同知识点的预测差异
    print(f"\n📊 测试不同知识点的学习效果预测:")
    predictions = []
    for i in range(5):
        pred = agent.predict_learning_effect(i)
        predictions.append(pred)
        print(f"  知识点{i}: {pred:.6f}")
    
    # 检查预测是否有差异
    pred_std = np.std(predictions)
    pred_range = max(predictions) - min(predictions)
    
    print(f"\n📈 预测统计:")
    print(f"  平均值: {np.mean(predictions):.6f}")
    print(f"  标准差: {pred_std:.6f}")
    print(f"  范围: {pred_range:.6f}")
    
    if pred_std > 0.001:  # 如果标准差大于0.001，说明有差异
        print(f"✅ AFM模型预测有差异性，模型正常工作")
        return True
    else:
        print(f"❌ AFM模型预测无差异，可能存在问题")
        return False

def test_different_users():
    """测试不同用户的预测差异"""
    print(f"\n=== 测试不同用户的预测差异 ===")
    
    user_predictions = {}
    for user_id in [0, 1, 2]:
        agent = RealAFMAgent(action_space_size=5, user_id=user_id)
        if agent.model is None:
            continue
            
        predictions = []
        for i in range(5):
            pred = agent.predict_learning_effect(i)
            predictions.append(pred)
        
        user_predictions[user_id] = predictions
        print(f"用户{user_id}: {[f'{p:.4f}' for p in predictions]}")
    
    # 检查不同用户间的差异
    if len(user_predictions) >= 2:
        user_ids = list(user_predictions.keys())
        diff = np.mean(np.abs(np.array(user_predictions[user_ids[0]]) - 
                             np.array(user_predictions[user_ids[1]])))
        print(f"用户间平均差异: {diff:.6f}")
        
        if diff > 0.001:
            print(f"✅ 不同用户预测有差异，个性化功能正常")
            return True
        else:
            print(f"❌ 不同用户预测无差异，个性化可能有问题")
            return False
    
    return False

def test_decision_making():
    """测试决策过程"""
    print(f"\n=== 测试AFM智能体决策过程 ===")
    
    agent = RealAFMAgent(action_space_size=5, user_id=0)
    
    # 测试不同学习状态下的决策
    test_states = [
        np.array([0.1, 0.2, 0.3, 0.4, 0.5]),  # 递增状态
        np.array([0.5, 0.4, 0.3, 0.2, 0.1]),  # 递减状态
        np.array([0.9, 0.1, 0.9, 0.1, 0.9]),  # 交替状态
    ]
    
    for i, state in enumerate(test_states):
        action = agent.get_action(state)
        print(f"状态{i+1} {state} -> 选择动作: {action}")
        
        # 显示决策详情
        afm_preds = []
        needs = []
        scores = []
        
        for j in range(5):
            afm_pred = agent.predict_learning_effect(j)
            need = 1.0 - state[j]
            score = afm_pred * 0.7 + need * 0.3
            
            afm_preds.append(afm_pred)
            needs.append(need)
            scores.append(score)
        
        print(f"  AFM预测: {[f'{p:.3f}' for p in afm_preds]}")
        print(f"  掌握需求: {[f'{n:.3f}' for n in needs]}")
        print(f"  综合得分: {[f'{s:.3f}' for s in scores]}")
        print()

def main():
    """主测试函数"""
    print("🔍 开始验证真实AFM模型...")
    
    test1 = test_afm_predictions()
    test2 = test_different_users()
    test_decision_making()
    
    print(f"\n📋 测试总结:")
    print(f"  预测差异性测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"  用户个性化测试: {'✅ 通过' if test2 else '❌ 失败'}")
    
    if test1 and test2:
        print(f"\n🎉 真实AFM模型验证成功！模型正常工作并具有个性化能力。")
    else:
        print(f"\n⚠️  AFM模型可能存在问题，需要进一步检查。")

if __name__ == "__main__":
    main()
