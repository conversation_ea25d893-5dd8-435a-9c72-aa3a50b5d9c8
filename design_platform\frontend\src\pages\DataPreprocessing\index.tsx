import React, { useState, useEffect } from 'react';
import { Card, Steps, Button, Typography, Progress, Table, Tag, Row, Col, Statistic, message, Spin } from 'antd';
import { CheckCircleOutlined, LoadingOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import './index.css';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

interface PreprocessingStep {
  key: string;
  title: string;
  description: string;
  status: 'wait' | 'process' | 'finish' | 'error';
  progress: number;
  details?: any;
}

const DataPreprocessing: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [processing, setProcessing] = useState(false);
  const [steps, setSteps] = useState<PreprocessingStep[]>([
    {
      key: 'load',
      title: '数据加载',
      description: '从原始数据集加载学生答题记录',
      status: 'wait',
      progress: 0
    },
    {
      key: 'clean',
      title: '数据清洗',
      description: '清理无效记录，处理缺失值',
      status: 'wait',
      progress: 0
    },
    {
      key: 'qmatrix',
      title: 'Q-Matrix构建',
      description: '构建题目-知识点关联矩阵',
      status: 'wait',
      progress: 0
    },
    {
      key: 'response',
      title: 'Response Matrix生成',
      description: '生成学生答题响应矩阵',
      status: 'wait',
      progress: 0
    },
    {
      key: 'validate',
      title: '数据验证',
      description: '验证数据完整性和一致性',
      status: 'wait',
      progress: 0
    }
  ]);

  const [dataStats, setDataStats] = useState({
    originalRecords: 0,
    cleanedRecords: 0,
    students: 0,
    items: 0,
    knowledgeComponents: 0
  });

  const navigate = useNavigate();

  const startPreprocessing = async () => {
    setProcessing(true);
    
    // 模拟数据预处理过程
    for (let i = 0; i < steps.length; i++) {
      setCurrentStep(i);
      
      // 更新当前步骤状态
      setSteps(prev => prev.map((step, index) => ({
        ...step,
        status: index === i ? 'process' : index < i ? 'finish' : 'wait'
      })));

      // 模拟处理进度
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setSteps(prev => prev.map((step, index) => ({
          ...step,
          progress: index === i ? progress : index < i ? 100 : 0
        })));
      }

      // 更新数据统计
      if (i === 0) { // 数据加载
        setDataStats(prev => ({ ...prev, originalRecords: 325637 }));
      } else if (i === 1) { // 数据清洗
        setDataStats(prev => ({ ...prev, cleanedRecords: 298543, students: 4163 }));
      } else if (i === 2) { // Q-Matrix构建
        setDataStats(prev => ({ ...prev, items: 17751, knowledgeComponents: 123 }));
      }

      // 标记步骤完成
      setSteps(prev => prev.map((step, index) => ({
        ...step,
        status: index === i ? 'finish' : index < i ? 'finish' : 'wait'
      })));
    }

    setProcessing(false);
    message.success('数据预处理完成！');
  };

  const proceedToModelSelection = () => {
    navigate('/model-selection');
  };

  const getStepIcon = (step: PreprocessingStep) => {
    if (step.status === 'finish') {
      return <CheckCircleOutlined />;
    } else if (step.status === 'process') {
      return <LoadingOutlined />;
    }
    return null;
  };

  const preprocessingColumns = [
    {
      title: '处理步骤',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          'wait': { color: 'default', text: '等待中' },
          'process': { color: 'processing', text: '处理中' },
          'finish': { color: 'success', text: '已完成' },
          'error': { color: 'error', text: '错误' }
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    }
  ];

  return (
    <div className="data-preprocessing-container">
      <div className="preprocessing-header">
        <Title level={2}>
          🔧 数据预处理 - ASSISTments 2009-2010
        </Title>
        <Paragraph>
          将原始数据集转换为适合ORCDF模型训练的格式，包括数据清洗、Q-Matrix构建和Response Matrix生成。
        </Paragraph>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <Card title="预处理流程" className="preprocessing-flow-card">
            <Steps current={currentStep} direction="vertical">
              {steps.map((step, index) => (
                <Step
                  key={step.key}
                  title={step.title}
                  description={
                    <div>
                      <div>{step.description}</div>
                      {step.status === 'process' && (
                        <Progress 
                          percent={step.progress} 
                          size="small" 
                          style={{ marginTop: 8 }}
                        />
                      )}
                    </div>
                  }
                  status={step.status}
                  icon={getStepIcon(step)}
                />
              ))}
            </Steps>

            <div className="preprocessing-actions">
              {!processing && currentStep === 0 && (
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={startPreprocessing}
                  className="start-button"
                >
                  开始数据预处理
                </Button>
              )}

              {!processing && currentStep === steps.length && (
                <Button
                  type="primary"
                  size="large"
                  onClick={proceedToModelSelection}
                  className="proceed-button"
                >
                  进入模型选择
                </Button>
              )}

              {processing && (
                <div className="processing-info">
                  <Spin size="large" />
                  <Text style={{ marginLeft: 16 }}>正在处理数据，请稍候...</Text>
                </div>
              )}
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="数据统计" className="data-stats-card">
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="原始记录"
                  value={dataStats.originalRecords}
                  suffix="条"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="清洗后记录"
                  value={dataStats.cleanedRecords}
                  suffix="条"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="学生数量"
                  value={dataStats.students}
                  suffix="人"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="题目数量"
                  value={dataStats.items}
                  suffix="题"
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="知识点数量"
                  value={dataStats.knowledgeComponents}
                  suffix="个"
                />
              </Col>
            </Row>
          </Card>

          <Card title="处理详情" className="processing-details-card" style={{ marginTop: 16 }}>
            <Table
              dataSource={steps}
              columns={preprocessingColumns}
              pagination={false}
              size="small"
              rowKey="key"
            />
          </Card>
        </Col>
      </Row>

      <Card title="预处理说明" className="preprocessing-info-card" style={{ marginTop: 24 }}>
        <Row gutter={[24, 16]}>
          <Col xs={24} md={8}>
            <div className="info-item">
              <Title level={5}>📊 数据加载</Title>
              <Paragraph>
                从原始CSV文件中加载学生答题记录，包含学生ID、题目ID、答题结果、时间戳等信息。
              </Paragraph>
            </div>
          </Col>
          <Col xs={24} md={8}>
            <div className="info-item">
              <Title level={5}>🧹 数据清洗</Title>
              <Paragraph>
                移除重复记录、处理缺失值、过滤异常数据，确保数据质量满足模型训练要求。
              </Paragraph>
            </div>
          </Col>
          <Col xs={24} md={8}>
            <div className="info-item">
              <Title level={5}>🔗 Q-Matrix构建</Title>
              <Paragraph>
                根据题目内容和专家知识，构建题目与知识点的关联矩阵，这是认知诊断的核心。
              </Paragraph>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default DataPreprocessing;
