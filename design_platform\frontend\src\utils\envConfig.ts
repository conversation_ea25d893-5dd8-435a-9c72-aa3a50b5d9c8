/**
 * 环境配置工具 - 处理内网穿透环境下的API配置
 */

export interface EnvConfig {
  apiUrl: string;
  backendUrl: string;
  isExternalAccess: boolean;
  needsConfiguration: boolean;
}

/**
 * 检测当前环境并返回配置信息
 * 优先级：本地环境自动配置 > localStorage > 环境变量 > 默认值
 */
export const detectEnvironment = (): EnvConfig => {
  const currentHost = window.location.hostname;
  const isExternalAccess = currentHost.includes('ngrok') ||
                          currentHost.includes('cpolar') ||
                          currentHost.includes('frp') ||
                          currentHost.includes('tunnel');

  // 1. 本地环境优先：如果是本地访问，直接使用本地配置
  if (!isExternalAccess && (currentHost === 'localhost' || currentHost === '127.0.0.1')) {
    console.log('🏠 检测到本地环境，使用本地后端配置');
    return {
      apiUrl: 'http://localhost:8000/api/v1',
      backendUrl: 'http://localhost:8000',
      isExternalAccess: false,
      needsConfiguration: false
    };
  }

  // 2. 外网访问：检查localStorage配置（用户动态配置）
  const localBackendUrl = localStorage.getItem('BACKEND_URL');
  if (isExternalAccess && localBackendUrl) {
    console.log('🌐 检测到外网环境，使用localStorage配置:', localBackendUrl);
    return {
      apiUrl: `${localBackendUrl}/api/v1`,
      backendUrl: localBackendUrl,
      isExternalAccess: true,
      needsConfiguration: false
    };
  }

  // 3. 外网访问：检查环境变量配置（静态配置）
  if (isExternalAccess && process.env.REACT_APP_API_URL && process.env.REACT_APP_BACKEND_URL) {
    console.log('🌐 检测到外网环境，使用环境变量配置:', process.env.REACT_APP_BACKEND_URL);
    return {
      apiUrl: process.env.REACT_APP_API_URL,
      backendUrl: process.env.REACT_APP_BACKEND_URL,
      isExternalAccess: true,
      needsConfiguration: false
    };
  }

  // 4. 外网访问但没有配置，需要用户配置
  if (isExternalAccess) {
    console.log('🌐 检测到外网环境，但没有配置，需要用户配置');
    return {
      apiUrl: 'http://localhost:8000/api/v1', // 默认值
      backendUrl: 'http://localhost:8000',
      isExternalAccess: true,
      needsConfiguration: true
    };
  }

  // 5. 其他情况默认本地配置
  console.log('🔧 使用默认本地配置');
  return {
    apiUrl: 'http://localhost:8000/api/v1',
    backendUrl: 'http://localhost:8000',
    isExternalAccess: false,
    needsConfiguration: false
  };
};

/**
 * 保存后端配置
 */
export const saveBackendConfig = (backendUrl: string): void => {
  const normalizedUrl = backendUrl.endsWith('/') ? backendUrl.slice(0, -1) : backendUrl;
  localStorage.setItem('BACKEND_URL', normalizedUrl);
  
  // 同时设置到环境变量（仅在当前会话有效）
  (window as any).__RUNTIME_CONFIG__ = {
    REACT_APP_API_URL: `${normalizedUrl}/api/v1`,
    REACT_APP_BACKEND_URL: normalizedUrl
  };
};

/**
 * 获取运行时配置
 */
export const getRuntimeConfig = (): Partial<EnvConfig> => {
  const runtimeConfig = (window as any).__RUNTIME_CONFIG__;
  if (runtimeConfig) {
    return {
      apiUrl: runtimeConfig.REACT_APP_API_URL,
      backendUrl: runtimeConfig.REACT_APP_BACKEND_URL
    };
  }
  return {};
};

/**
 * 测试后端连接
 */
export const testBackendConnection = async (backendUrl: string): Promise<boolean> => {
  try {
    const testUrl = backendUrl.endsWith('/') ? `${backendUrl}health` : `${backendUrl}/health`;
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await fetch(testUrl, {
      method: 'GET',
      signal: controller.signal,
    });

    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    console.error('后端连接测试失败:', error);
    return false;
  }
};
