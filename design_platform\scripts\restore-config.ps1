# 恢复前端API配置脚本
# 使用方法: .\restore-config.ps1 -BackupDir "backup-directory-path"

param(
    [Parameter(Mandatory=$false)]
    [string]$BackupDir
)

Write-Host "🔄 恢复前端API配置..." -ForegroundColor Green

# 如果没有指定备份目录，查找最新的备份
if (-not $BackupDir) {
    $backupDirs = Get-ChildItem "../frontend/" -Directory | Where-Object { $_.Name -match "config-backup-\d{8}-\d{6}" } | Sort-Object Name -Descending
    if ($backupDirs.Count -eq 0) {
        Write-Host "❌ 未找到备份目录" -ForegroundColor Red
        Write-Host "请手动指定备份目录: .\restore-config.ps1 -BackupDir 'path'" -ForegroundColor Yellow
        exit 1
    }
    $BackupDir = $backupDirs[0].FullName
    Write-Host "📁 使用最新备份: $($backupDirs[0].Name)" -ForegroundColor Yellow
}

# 验证备份目录存在
if (-not (Test-Path $BackupDir)) {
    Write-Host "❌ 备份目录不存在: $BackupDir" -ForegroundColor Red
    exit 1
}

Write-Host "📁 备份目录: $BackupDir" -ForegroundColor Blue

# 定义目标文件路径
$targetFiles = @{
    "api.ts" = "../frontend/src/services/api.ts"
    "index.tsx" = "../frontend/src/components/EmbeddingShowcase/index.tsx"
    "TrainingWorkflow.tsx" = "../frontend/src/pages/Dashboard/TrainingWorkflow.tsx"
    "EduBrainDashboard.tsx" = "../frontend/src/pages/Dashboard/EduBrainDashboard.tsx"
}

# 恢复文件
Write-Host "🔄 恢复配置文件..." -ForegroundColor Blue
foreach ($backupFile in $targetFiles.Keys) {
    $backupPath = Join-Path $BackupDir $backupFile
    $targetPath = $targetFiles[$backupFile]
    
    if (Test-Path $backupPath) {
        Copy-Item $backupPath $targetPath -Force
        Write-Host "✅ 恢复: $backupFile" -ForegroundColor Green
    } else {
        Write-Host "⚠️  备份文件不存在: $backupFile" -ForegroundColor Yellow
    }
}

# 删除环境变量文件
$envFile = "../frontend/.env.local"
if (Test-Path $envFile) {
    Remove-Item $envFile -Force
    Write-Host "✅ 删除: .env.local" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 配置恢复完成!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 下一步操作:" -ForegroundColor Magenta
Write-Host "1. 重启前端服务: npm start" -ForegroundColor White
Write-Host "2. 现在API地址已恢复为 http://localhost:8000" -ForegroundColor White
Write-Host ""
Write-Host "🗑️  清理备份:" -ForegroundColor Yellow
Write-Host "如不再需要备份，可删除目录: $BackupDir" -ForegroundColor Gray
