"""
实验相关的Pydantic模式
"""
from typing import Optional, Dict, Any
from pydantic import BaseModel, validator
from datetime import datetime
from enum import Enum

from app.models.experiment import ExperimentStatus, ModelType, DatasetType


class ExperimentBase(BaseModel):
    """实验基础模式"""
    name: str
    description: Optional[str] = None
    dataset_type: DatasetType
    model_type: ModelType


class ExperimentConfig(BaseModel):
    """实验配置模式"""
    # 训练参数
    batch_size: int = 256
    epochs: int = 10
    learning_rate: float = 0.001
    weight_decay: float = 0.0
    
    # 模型特定参数
    latent_dim: int = 32
    gcn_layers: Optional[int] = 3  # 仅用于图模型
    keep_prob: float = 1.0
    
    # ORCDF特定参数
    ssl_weight: Optional[float] = 1e-3
    ssl_temp: Optional[float] = 0.5
    flip_ratio: Optional[float] = 0.15
    
    # 其他参数
    test_size: float = 0.2
    seed: int = 42
    device: str = "cuda:0"
    
    @validator('test_size')
    def validate_test_size(cls, v):
        if not 0 < v < 1:
            raise ValueError('test_size必须在0和1之间')
        return v
    
    @validator('epochs')
    def validate_epochs(cls, v):
        if v <= 0:
            raise ValueError('epochs必须大于0')
        return v


class ExperimentCreate(ExperimentBase):
    """创建实验模式"""
    config: ExperimentConfig
    auto_start: bool = False


class ExperimentUpdate(BaseModel):
    """更新实验模式"""
    name: Optional[str] = None
    description: Optional[str] = None
    config: Optional[ExperimentConfig] = None


class ExperimentResponse(ExperimentBase):
    """实验响应模式"""
    id: int
    status: ExperimentStatus
    progress: float
    config: Optional[Dict[str, Any]] = None
    results: Optional[Dict[str, Any]] = None
    metrics: Optional[Dict[str, Any]] = None
    model_path: Optional[str] = None
    log_path: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    task_id: Optional[str] = None

    class Config:
        from_attributes = True


class ExperimentProgress(BaseModel):
    """实验进度模式"""
    experiment_id: int
    status: ExperimentStatus
    progress: float
    current_epoch: Optional[int] = None
    total_epochs: Optional[int] = None
    current_loss: Optional[float] = None
    best_metric: Optional[float] = None
    estimated_time_remaining: Optional[int] = None  # 秒
    last_updated: datetime


class ExperimentResult(BaseModel):
    """实验结果模式"""
    experiment_id: int
    metrics: Dict[str, float]
    training_history: Dict[str, list]
    model_info: Dict[str, Any]
    dataset_info: Dict[str, Any]
    config: Dict[str, Any]
