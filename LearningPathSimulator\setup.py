#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Learning Path Simulator Setup Script
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# 读取requirements文件
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="learning-path-simulator",
    version="1.0.0",
    author="Learning Path Simulator Team",
    author_email="<EMAIL>",
    description="A cognitive science-based learning path planning algorithm evaluation platform",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/learningpathsim/learning-path-simulator",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Education",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Education",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.7",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=6.2.0",
            "pytest-cov>=2.12.0",
            "flake8>=3.9.0",
            "black>=21.6.0",
            "isort>=5.9.0",
            "mypy>=0.910",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=0.5.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "lps-benchmark=LearningPathSimulator.cli:run_benchmark",
            "lps-demo=LearningPathSimulator.cli:run_demo",
        ],
    },
    include_package_data=True,
    zip_safe=False,
)
