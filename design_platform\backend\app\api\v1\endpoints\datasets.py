"""
数据集管理API端点
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
import time
import random

from app.core.database import get_db
from app.models.experiment import Dataset, DatasetType
from app.services.dataset_service import DatasetService
from app.schemas.dataset import DatasetCreate, DatasetResponse, DatasetStats

router = APIRouter()


@router.get("/", response_model=List[DatasetResponse])
async def get_datasets(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """获取数据集列表"""
    service = DatasetService(db)
    datasets = service.get_datasets(skip=skip, limit=limit, is_active=is_active)
    return datasets


@router.get("/available")
async def get_available_datasets(db: Session = Depends(get_db)):
    """获取所有可用数据集信息"""
    service = DatasetService(db)
    datasets_info = service.get_available_datasets_info()
    return datasets_info


@router.get("/{dataset_name}/validate")
async def validate_dataset(dataset_name: str, db: Session = Depends(get_db)):
    """验证数据集"""
    service = DatasetService(db)
    validation_result = service.validate_dataset(dataset_name)
    return validation_result


@router.get("/{dataset_name}/training-data")
async def get_training_data(dataset_name: str, db: Session = Depends(get_db)):
    """获取用于训练的数据集"""
    service = DatasetService(db)
    training_data = service.get_dataset_for_training(dataset_name)

    if not training_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"数据集 {dataset_name} 不存在或不适合训练"
        )

    return training_data


@router.get("/{dataset_name}/visualization-data")
async def get_visualization_data(dataset_name: str, db: Session = Depends(get_db)):
    """获取用于可视化的数据集"""
    service = DatasetService(db)
    viz_data = service.get_dataset_for_visualization(dataset_name)

    if not viz_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"数据集 {dataset_name} 不存在"
        )

    return viz_data


@router.get("/{dataset_id}", response_model=DatasetResponse)
async def get_dataset(dataset_id: int, db: Session = Depends(get_db)):
    """获取单个数据集详情"""
    service = DatasetService(db)
    dataset = service.get_dataset(dataset_id)
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    return dataset


@router.get("/{dataset_id}/stats", response_model=DatasetStats)
async def get_dataset_stats(dataset_id: int, db: Session = Depends(get_db)):
    """获取数据集统计信息"""
    service = DatasetService(db)
    dataset = service.get_dataset(dataset_id)
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    
    stats = service.get_dataset_statistics(dataset_id)
    return stats


@router.post("/", response_model=DatasetResponse)
async def create_dataset(
    dataset: DatasetCreate,
    db: Session = Depends(get_db)
):
    """创建新数据集"""
    service = DatasetService(db)
    try:
        new_dataset = service.create_dataset(dataset)
        return new_dataset
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{dataset_id}", response_model=DatasetResponse)
async def update_dataset(
    dataset_id: int,
    dataset_update: DatasetCreate,
    db: Session = Depends(get_db)
):
    """更新数据集信息"""
    service = DatasetService(db)
    dataset = service.update_dataset(dataset_id, dataset_update)
    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    return dataset


@router.delete("/{dataset_id}")
async def delete_dataset(dataset_id: int, db: Session = Depends(get_db)):
    """删除数据集"""
    service = DatasetService(db)
    success = service.delete_dataset(dataset_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )
    return {"message": "数据集删除成功"}


@router.post("/sync")
async def sync_datasets(db: Session = Depends(get_db)):
    """同步数据集信息"""
    service = DatasetService(db)
    synced_count = service.sync_datasets_from_filesystem()
    return {"message": f"同步完成，共同步 {synced_count} 个数据集"}








class PreprocessRequest(BaseModel):
    """数据预处理请求模型"""
    preprocessing_options: Optional[Dict[str, Any]] = {}
    force_reprocess: Optional[bool] = False


@router.post("/{dataset_id}/preprocess")
async def preprocess_dataset(
    dataset_id: int,
    request: PreprocessRequest = PreprocessRequest(),
    db: Session = Depends(get_db)
):
    """数据预处理接口 - 真实数据版本"""
    service = DatasetService(db)
    dataset = service.get_dataset(dataset_id)

    if not dataset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="数据集不存在"
        )

    try:
        # 使用真实数据处理
        training_data = service.get_dataset_for_training(dataset.name)

        if not training_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="数据集不适合训练或处理失败"
            )

        # 真实的预处理步骤
        preprocessing_steps = [
            "加载原始数据",
            "数据完整性检查",
            "清理缺失值和异常值",
            "ID重编码和映射",
            "训练/验证/测试集划分",
            "Q矩阵处理",
            "数据质量评估"
        ]

        # 从真实数据获取统计信息
        stats = training_data.get('statistics', {})
        basic_info = stats.get('basic_info', {})
        data_quality = stats.get('data_quality', {})
        dataset_info = training_data.get('dataset_info', {})

        results = {
            "dataset_id": dataset_id,
            "dataset_name": dataset.name,
            "status": "success",
            "message": "真实数据预处理完成",
            "preprocessing_steps": preprocessing_steps,
            "statistics": {
                "total_students": basic_info.get('student_num', 0),
                "total_questions": basic_info.get('exercise_num', 0),
                "total_interactions": basic_info.get('response_num', 0),
                "knowledge_concepts": basic_info.get('knowledge_num', 0),
                "data_quality_score": data_quality.get('validity', 0.95),
                "completeness": data_quality.get('completeness', 1.0),
                "consistency": data_quality.get('consistency', 1.0),
                "sparsity": stats.get('sparsity', 0),
                "train_size": dataset_info.get('train_size', 0),
                "val_size": dataset_info.get('val_size', 0),
                "test_size": dataset_info.get('test_size', 0),
                "data_issues": data_quality.get('issues', [])
            },
            "preprocessing_options": request.preprocessing_options,
            "timestamp": time.time(),
            "data_source": "real_dataset"
        }

        return results

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"数据预处理失败: {str(e)}"
        )


@router.post("/{dataset_name}/prepare-training")
async def prepare_training_data(
    dataset_name: str,
    test_size: float = 0.2,
    validation_size: float = 0.1,
    seed: int = 42,
    db: Session = Depends(get_db)
):
    """准备训练数据"""
    service = DatasetService(db)

    try:
        # 验证数据集
        validation = service.validate_dataset(dataset_name)
        if not validation.get('valid', False):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"数据集验证失败: {validation.get('error', '未知错误')}"
            )

        # 获取训练数据
        training_data = service.get_dataset_for_training(dataset_name)
        if not training_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"无法为数据集 {dataset_name} 准备训练数据"
            )

        return {
            "dataset_name": dataset_name,
            "status": "success",
            "message": "训练数据准备完成",
            "data": training_data,
            "parameters": {
                "test_size": test_size,
                "validation_size": validation_size,
                "seed": seed
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"准备训练数据失败: {str(e)}"
        )
