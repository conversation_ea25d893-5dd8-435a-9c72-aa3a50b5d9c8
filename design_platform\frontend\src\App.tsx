import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import Layout from './components/Layout/index';
import Login from './pages/Login/index';
import DashboardHome from './pages/Dashboard/DashboardHome';
import TrainingWorkflow from './pages/Dashboard/TrainingWorkflow';
import EduBrainDashboard from './pages/Dashboard/EduBrainDashboard';
import SimpleDashboard from './pages/Dashboard/SimpleDashboard';
import SystemSettings from './pages/Dashboard/SystemSettings';
import DatasetManagement from './pages/DatasetManagement/index';
import ExperimentConfig from './pages/ExperimentConfig/index';
import ExperimentMonitor from './pages/ExperimentMonitor/index';
import ResultAnalysis from './pages/ResultAnalysis/index';
import ApiTest from './pages/ApiTest';
import ApiTestNew from './pages/ApiTestNew';
import CognitiveDiagnosis from './pages/CognitiveDiagnosis/index';
import CognitiveDiagnosisCenter from './pages/CognitiveDiagnosisCenter/index';
import ConfigTest from './components/ConfigTest/index';
import './App.css';

// 检查用户是否已登录
const isAuthenticated = () => {
  return localStorage.getItem('user') !== null;
};

// 受保护的路由组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return isAuthenticated() ? <>{children}</> : <Navigate to="/login" replace />;
};

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          {/* 登录页面 */}
          <Route path="/login" element={<Login />} />

          {/* 配置测试工具 - 无需登录 */}
          <Route path="/config-test" element={<ConfigTest />} />

          {/* 主应用布局 - 登录后直接进入 */}
          <Route path="/*" element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<SimpleDashboard />} />
                  <Route path="/dashboard/overview" element={<DashboardHome />} />
                  <Route path="/dashboard/training" element={<TrainingWorkflow />} />
                  <Route path="/dashboard/legacy" element={<EduBrainDashboard />} />
                  <Route path="/dashboard/settings" element={<SystemSettings />} />
                  <Route path="/datasets" element={<DatasetManagement />} />
                  <Route path="/experiments" element={<ExperimentMonitor />} />
                  <Route path="/experiment/config" element={<ExperimentConfig />} />
                  <Route path="/experiment/monitor" element={<ExperimentMonitor />} />
                  <Route path="/results" element={<ResultAnalysis />} />
                  <Route path="/analysis" element={<ResultAnalysis />} />
                  <Route path="/cognitive-diagnosis" element={<CognitiveDiagnosis />} />
                  <Route path="/cognitive-diagnosis-center" element={<CognitiveDiagnosisCenter />} />
                  <Route path="/api-test" element={<ApiTest />} />
                  <Route path="/api-test-new" element={<ApiTestNew />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </ConfigProvider>
  );
};

export default App;
