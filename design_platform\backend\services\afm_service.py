"""
AFM学习路径推荐服务
基于Attentional Factorization Machines的个性化学习路径推荐
"""

import torch
import torch.nn as nn
import numpy as np
import pickle
import os
from typing import List, Dict, Tuple, Optional
from sklearn.preprocessing import StandardScaler
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class RecommendationResult:
    """推荐结果数据类"""
    knowledge_point_id: str
    confidence_score: float
    difficulty_level: str
    estimated_duration: int  # 预估学习时长(分钟)

@dataclass
class LearningPath:
    """学习路径数据类"""
    user_id: str
    recommended_sequence: List[RecommendationResult]
    total_duration: int
    difficulty: str
    created_at: str

class AFMLayer(nn.Module):
    """AFM注意力层"""
    def __init__(self, embedding_size, attention_factor=4, l2_reg=0.0, drop_rate=0.0):
        super(AFM<PERSON>ayer, self).__init__()
        self.embedding_size = embedding_size
        self.attention_factor = attention_factor
        self.l2_reg = l2_reg
        self.drop_rate = drop_rate

        self.attention_W = nn.Parameter(torch.Tensor(embedding_size, attention_factor))
        self.attention_b = nn.Parameter(torch.Tensor(attention_factor))
        self.projection_h = nn.Parameter(torch.Tensor(attention_factor, 1))
        self.projection_p = nn.Parameter(torch.Tensor(embedding_size, 1))

        nn.init.xavier_normal_(self.attention_W)
        nn.init.xavier_normal_(self.projection_h)
        nn.init.xavier_normal_(self.projection_p)
        nn.init.zeros_(self.attention_b)

        self.drop = nn.Dropout(drop_rate)
        self.relu = nn.ReLU()
        self.softmax = nn.Softmax(dim=1)

    def forward(self, inputs):
        x = torch.stack(inputs, dim=1)  # [B, F, D]
        
        p = x.unsqueeze(2)  # [B, F, 1, D]
        q = x.unsqueeze(1)  # [B, 1, F, D]
        inner_product = p * q  # [B, F, F, D]
        
        rows, cols = torch.triu_indices(x.size(1), x.size(1), offset=1)
        bi_interaction = inner_product[:, rows, cols]  # [B, C(F,2), D]

        if bi_interaction.dim() == 4:
            bi_interaction = bi_interaction.squeeze(2)

        attention_temp = self.relu(
            torch.einsum('bnd,df->bnf', bi_interaction, self.attention_W) + self.attention_b
        )
        normalized_att_score = self.softmax(
            torch.einsum('bnf,fg->bng', attention_temp, self.projection_h)
        )
        attention_output = torch.sum(normalized_att_score * bi_interaction, dim=1)
        attention_output = self.drop(attention_output)
        afm_out = torch.einsum('bd,dg->bg', attention_output, self.projection_p)

        return afm_out

class AFM(nn.Module):
    """AFM模型主类"""
    def __init__(self, input_dim, use_attention=True, attention_factor=8, l2_reg=0.00001, drop_rate=0.5):
        super(AFM, self).__init__()
        self.use_attention = use_attention

        if self.use_attention:
            self.fm = AFMLayer(input_dim, attention_factor, l2_reg, drop_rate)
        else:
            # 简化的FM层
            self.fm = nn.Linear(input_dim, 1)

        self.out = nn.Sigmoid()
        self.dropout = nn.Dropout(drop_rate)

    def forward(self, X):
        if self.use_attention:
            x_list = torch.split(X, 1, dim=1)
            logit = self.fm(x_list)
        else:
            logit = self.fm(X)

        y_pred = self.out(logit)
        y_pred = torch.clamp(y_pred, min=1e-7, max=1-1e-7)
        return y_pred

class AFMRecommendationService:
    """AFM推荐服务主类"""
    
    def __init__(self, model_path: str = None, config: Dict = None):
        self.model = None
        self.scaler = StandardScaler()
        self.knowledge_embeddings = None
        self.knowledge_metadata = {}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 默认配置
        self.config = config or {
            'embedding_dim': 102,
            'attention_factor': 8,
            'drop_rate': 0.2,
            'top_k': 10
        }
        
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
        else:
            logger.warning("Model path not provided or doesn't exist. Model needs to be loaded manually.")

    def load_model(self, model_path: str):
        """加载训练好的AFM模型"""
        try:
            self.model = torch.load(model_path, map_location=self.device)
            self.model.eval()
            logger.info(f"AFM model loaded successfully from {model_path}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise

    def load_knowledge_embeddings(self, embeddings_path: str, metadata_path: str = None):
        """加载知识点嵌入和元数据"""
        try:
            with open(embeddings_path, 'rb') as f:
                self.knowledge_embeddings = pickle.load(f)
            
            if metadata_path and os.path.exists(metadata_path):
                with open(metadata_path, 'rb') as f:
                    self.knowledge_metadata = pickle.load(f)
            
            logger.info(f"Knowledge embeddings loaded: {self.knowledge_embeddings.shape}")
        except Exception as e:
            logger.error(f"Failed to load knowledge embeddings: {e}")
            raise

    def create_user_embedding(self, diagnosis_data: Dict) -> np.ndarray:
        """根据认知诊断结果创建用户嵌入"""
        try:
            # 从认知诊断数据中提取特征
            features = []
            
            # 基础特征
            features.append(diagnosis_data.get('overall_ability', 0.5))
            
            # 知识点掌握情况
            knowledge_diagnosis = diagnosis_data.get('knowledge_diagnosis', {})
            knowledge_scores = []
            for kc, data in knowledge_diagnosis.items():
                mastery_prob = data.get('mastery_probability', 0.5)
                knowledge_scores.append(mastery_prob)
            
            # 补齐到固定维度
            target_dim = self.config['embedding_dim']
            if len(knowledge_scores) < target_dim - 1:
                knowledge_scores.extend([0.5] * (target_dim - 1 - len(knowledge_scores)))
            elif len(knowledge_scores) > target_dim - 1:
                knowledge_scores = knowledge_scores[:target_dim - 1]
            
            features.extend(knowledge_scores)
            
            # 确保维度正确
            if len(features) != target_dim:
                features = features[:target_dim] if len(features) > target_dim else features + [0.5] * (target_dim - len(features))
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            logger.error(f"Failed to create user embedding: {e}")
            # 返回默认嵌入
            return np.full(self.config['embedding_dim'], 0.5, dtype=np.float32)

    def recommend_learning_path(self, user_id: str, diagnosis_data: Dict, top_k: int = None) -> LearningPath:
        """为用户推荐学习路径"""
        if self.model is None:
            raise ValueError("Model not loaded. Please load model first.")
        
        if self.knowledge_embeddings is None:
            raise ValueError("Knowledge embeddings not loaded. Please load embeddings first.")
        
        top_k = top_k or self.config['top_k']
        
        try:
            # 创建用户嵌入
            user_embedding = self.create_user_embedding(diagnosis_data)
            
            # 计算推荐分数
            recommendations = self._calculate_recommendations(user_embedding, top_k)
            
            # 生成学习路径
            learning_path = self._generate_learning_path(user_id, recommendations)
            
            return learning_path
            
        except Exception as e:
            logger.error(f"Failed to generate recommendations for user {user_id}: {e}")
            raise

    def _calculate_recommendations(self, user_embedding: np.ndarray, top_k: int) -> List[Tuple[int, float]]:
        """计算推荐分数"""
        scores = []
        
        with torch.no_grad():
            for idx in range(len(self.knowledge_embeddings)):
                knowledge_emb = self.knowledge_embeddings[idx]
                
                # 构造特征
                features = np.stack([user_embedding, knowledge_emb], axis=0)
                features = features.reshape(1, 2, -1)  # [1, 2, embedding_dim]
                
                # 预测
                x = torch.FloatTensor(features).to(self.device)
                score = self.model(x).cpu().numpy()[0, 0]
                scores.append((idx, float(score)))
        
        # 排序并返回top_k
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores[:top_k]

    def _generate_learning_path(self, user_id: str, recommendations: List[Tuple[int, float]]) -> LearningPath:
        """生成学习路径对象"""
        recommended_sequence = []
        total_duration = 0
        
        for idx, score in recommendations:
            # 获取知识点元数据
            metadata = self.knowledge_metadata.get(idx, {})
            
            # 根据置信度确定难度等级
            if score > 0.8:
                difficulty = "easy"
                duration = 15
            elif score > 0.6:
                difficulty = "medium" 
                duration = 25
            else:
                difficulty = "hard"
                duration = 35
            
            recommendation = RecommendationResult(
                knowledge_point_id=str(idx),
                confidence_score=score,
                difficulty_level=difficulty,
                estimated_duration=duration
            )
            
            recommended_sequence.append(recommendation)
            total_duration += duration
        
        # 确定整体难度
        avg_score = sum(r.confidence_score for r in recommended_sequence) / len(recommended_sequence)
        overall_difficulty = "easy" if avg_score > 0.7 else "medium" if avg_score > 0.5 else "hard"
        
        return LearningPath(
            user_id=user_id,
            recommended_sequence=recommended_sequence,
            total_duration=total_duration,
            difficulty=overall_difficulty,
            created_at=str(np.datetime64('now'))
        )

    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'model_loaded': self.model is not None,
            'embeddings_loaded': self.knowledge_embeddings is not None,
            'embedding_dim': self.config['embedding_dim'],
            'device': str(self.device),
            'knowledge_points_count': len(self.knowledge_embeddings) if self.knowledge_embeddings is not None else 0
        }

# 全局服务实例
afm_service = AFMRecommendationService()
