import React, { useEffect, useState } from 'react';
import { Row, Col, Card, Statistic, Table, Tag, Progress } from 'antd';
import {
  ExperimentOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';

// 动态API配置 - 支持内网穿透
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (process.env.REACT_APP_API_URL) {
    return process.env.REACT_APP_API_URL;
  }

  // 检测当前访问域名
  const currentHost = window.location.hostname;

  // 如果是内网穿透域名，使用相对路径或提示用户配置
  if (currentHost.includes('ngrok') || currentHost.includes('cpolar') || currentHost.includes('frp')) {
    // 内网穿透环境，需要用户手动配置后端地址
    const backendUrl = localStorage.getItem('BACKEND_URL');
    if (backendUrl) {
      return `${backendUrl}/api/v1`;
    }

    // 如果没有配置，提示用户配置
    console.warn('🌐 检测到内网穿透环境，请配置后端地址');
    return 'http://localhost:8000/api/v1'; // 默认值
  }

  // 本地开发环境
  return 'http://localhost:8000/api/v1';
};

interface DashboardStats {
  totalExperiments: number;
  runningExperiments: number;
  completedExperiments: number;
  totalDatasets: number;
}

interface RecentExperiment {
  id: number;
  name: string;
  status: string;
  progress: number;
  dataset: string;
  model: string;
  createdAt: string;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalExperiments: 0,
    runningExperiments: 0,
    completedExperiments: 0,
    totalDatasets: 0,
  });
  
  const [recentExperiments, setRecentExperiments] = useState<RecentExperiment[]>([]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // 加载实验数据
      const apiBaseUrl = getApiBaseUrl();
      const experimentsResponse = await fetch(`${apiBaseUrl}/experiments/`);
      if (experimentsResponse.ok) {
        const experimentsData = await experimentsResponse.json();
        console.log('实验数据:', experimentsData);

        let experiments = [];
        if (Array.isArray(experimentsData)) {
          experiments = experimentsData;
        } else if (experimentsData.experiments && Array.isArray(experimentsData.experiments)) {
          experiments = experimentsData.experiments;
        } else if (experimentsData.data && Array.isArray(experimentsData.data)) {
          experiments = experimentsData.data;
        }

        // 计算统计数据
        const totalExperiments = experiments.length;
        const runningExperiments = experiments.filter((exp: any) => exp.status === 'running').length;
        const completedExperiments = experiments.filter((exp: any) => exp.status === 'completed').length;

        // 转换为仪表盘格式
        const recentExps = experiments.slice(0, 5).map((exp: any) => ({
          id: exp.id,
          name: exp.name || `实验 ${exp.id}`,
          status: exp.status || 'pending',
          progress: exp.progress || 0,
          dataset: exp.dataset_name || exp.dataset || '未知',
          model: exp.model_type || exp.model || '未知',
          createdAt: exp.created_at || exp.createdAt || new Date().toISOString(),
        }));

        setRecentExperiments(recentExps);

        // 加载数据集数据
        const datasetsResponse = await fetch('http://localhost:8000/api/v1/datasets/');
        let totalDatasets = 0;
        if (datasetsResponse.ok) {
          const datasetsData = await datasetsResponse.json();
          console.log('数据集数据:', datasetsData);

          if (Array.isArray(datasetsData)) {
            totalDatasets = datasetsData.filter((ds: any) => ds.is_active !== false).length;
          } else if (datasetsData.datasets && Array.isArray(datasetsData.datasets)) {
            totalDatasets = datasetsData.datasets.filter((ds: any) => ds.is_active !== false).length;
          } else if (datasetsData.data && Array.isArray(datasetsData.data)) {
            totalDatasets = datasetsData.data.filter((ds: any) => ds.is_active !== false).length;
          }
        }

        setStats({
          totalExperiments,
          runningExperiments,
          completedExperiments,
          totalDatasets,
        });
      } else {
        // 如果API失败，使用模拟数据
        setStats({
          totalExperiments: 0,
          runningExperiments: 0,
          completedExperiments: 0,
          totalDatasets: 0,
        });
        setRecentExperiments([]);
      }
    } catch (error) {
      console.error('加载仪表盘数据失败:', error);
      // 使用模拟数据作为后备
      setStats({
        totalExperiments: 0,
        runningExperiments: 0,
        completedExperiments: 0,
        totalDatasets: 0,
      });
      setRecentExperiments([]);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      pending: { color: 'default', text: '等待中' },
      failed: { color: 'error', text: '失败' },
    };
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ColumnsType<RecentExperiment> = [
    {
      title: '实验名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress} size="small" />
      ),
    },
    {
      title: '数据集',
      dataIndex: 'dataset',
      key: 'dataset',
    },
    {
      title: '模型',
      dataIndex: 'model',
      key: 'model',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
  ];

  return (
    <div>
      <h1>🧠 知擎EduBrain 系统仪表板</h1>
      
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总实验数"
              value={stats.totalExperiments}
              prefix={<ExperimentOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中实验"
              value={stats.runningExperiments}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成实验"
              value={stats.completedExperiments}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据集数量"
              value={stats.totalDatasets}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Card title="最近实验" style={{ marginBottom: 24 }}>
        <Table
          columns={columns}
          dataSource={recentExperiments}
          rowKey="id"
          pagination={false}
        />
      </Card>
    </div>
  );
};

export default Dashboard;
