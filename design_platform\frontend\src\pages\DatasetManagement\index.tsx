import React, { useEffect, useState } from 'react';
import {
  Table,
  Card,
  Button,
  Tag,
  Space,
  Modal,
  Descriptions,
  Statistic,
  Row,
  Col,
  message,
  Spin,
} from 'antd';
import {
  DatabaseOutlined,
  EyeOutlined,
  SyncOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { datasetService } from '../../services/api';

interface Dataset {
  id: number;
  name: string;
  display_name: string;
  description: string;
  student_num: number;
  exercise_num: number;
  knowledge_num: number;
  response_num: number;
  is_active: boolean;
  is_demo: boolean;
  created_at: string;
  // 新增真实数据相关字段
  dataset_type?: string;
  has_q_matrix?: boolean;
  has_embeddings?: boolean;
  has_hierarchy?: boolean;
  data_quality?: {
    completeness: number;
    consistency: number;
    validity: number;
    issues: string[];
  };
  sparsity?: number;
}

interface DatasetStats {
  student_num: number;
  exercise_num: number;
  knowledge_num: number;
  response_num: number;
  avg_responses_per_student: number;
  avg_responses_per_exercise: number;
  sparsity: number;
  difficulty_distribution: Record<string, number>;
  knowledge_coverage: Record<string, any>;
}

const DatasetManagement: React.FC = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(false);
  const [statsModalVisible, setStatsModalVisible] = useState(false);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [datasetStats, setDatasetStats] = useState<DatasetStats | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  // 新增真实数据相关状态
  const [availableDatasets, setAvailableDatasets] = useState<any[]>([]);
  const [validationResults, setValidationResults] = useState<Record<string, any>>({});
  const [showRealDataView, setShowRealDataView] = useState(false);

  useEffect(() => {
    loadDatasets();
    loadAvailableDatasets();
  }, []);

  const loadDatasets = async () => {
    setLoading(true);
    try {
      const response = await datasetService.getDatasets();
      setDatasets(response.data);
    } catch (error) {
      message.error('加载数据集失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载可用的真实数据集
  const loadAvailableDatasets = async () => {
    try {
      const response = await datasetService.getAvailableDatasets();
      setAvailableDatasets(response.data.available_datasets || []);

      // 验证每个数据集
      const validationPromises = response.data.available_datasets.map(async (dataset: any) => {
        try {
          const validationResponse = await datasetService.validateDataset(dataset.name);
          return { [dataset.name]: validationResponse.data };
        } catch (error) {
          return { [dataset.name]: { valid: false, error: 'Validation failed' } };
        }
      });

      const validationResults = await Promise.all(validationPromises);
      const validationMap = validationResults.reduce((acc, result) => ({ ...acc, ...result }), {});
      setValidationResults(validationMap);

    } catch (error) {
      console.error('加载可用数据集失败:', error);
    }
  };

  const handleSyncDatasets = async () => {
    setLoading(true);
    try {
      await datasetService.syncDatasets();
      message.success('数据集同步成功');
      loadDatasets();
      loadAvailableDatasets(); // 同时刷新真实数据集
    } catch (error) {
      message.error('数据集同步失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewStats = async (dataset: Dataset) => {
    setSelectedDataset(dataset);
    setStatsModalVisible(true);
    setStatsLoading(true);

    try {
      const response = await datasetService.getDatasetStats(dataset.id);
      setDatasetStats(response.data);
    } catch (error) {
      message.error('获取数据集统计信息失败');
    } finally {
      setStatsLoading(false);
    }
  };

  // 处理真实数据集详情查看
  const handleViewRealDatasetStats = async (dataset: any) => {
    setSelectedDataset({
      ...dataset,
      id: 0, // 真实数据集没有数据库ID
    });
    setStatsModalVisible(true);
    setStatsLoading(true);

    try {
      // 获取真实数据集的可视化数据
      const response = await datasetService.getVisualizationData(dataset.name);

      // 转换为统计信息格式
      const stats = response.data.statistics;
      setDatasetStats({
        student_num: stats.basic_info.student_num,
        exercise_num: stats.basic_info.exercise_num,
        knowledge_num: stats.basic_info.knowledge_num,
        response_num: stats.basic_info.response_num,
        avg_responses_per_student: stats.response_stats?.avg_responses_per_student || 0,
        avg_responses_per_exercise: stats.response_stats?.avg_responses_per_exercise || 0,
        sparsity: stats.sparsity || 0,
        difficulty_distribution: stats.difficulty_distribution || {},
        knowledge_coverage: stats.q_matrix_stats || {},
      });
    } catch (error) {
      message.error('获取真实数据集统计信息失败');
    } finally {
      setStatsLoading(false);
    }
  };

  const columns: ColumnsType<Dataset> = [
    {
      title: '数据集名称',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text: string, record: Dataset) => (
        <Space>
          <DatabaseOutlined />
          <span>{text}</span>
          {record.is_demo && <Tag color="blue">演示</Tag>}
        </Space>
      ),
    },
    {
      title: '标识符',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <code>{text}</code>,
    },
    {
      title: '学生数',
      dataIndex: 'student_num',
      key: 'student_num',
      render: (num: number) => num?.toLocaleString() || '-',
    },
    {
      title: '题目数',
      dataIndex: 'exercise_num',
      key: 'exercise_num',
      render: (num: number) => num?.toLocaleString() || '-',
    },
    {
      title: '知识点数',
      dataIndex: 'knowledge_num',
      key: 'knowledge_num',
      render: (num: number) => num?.toLocaleString() || '-',
    },
    {
      title: '响应数',
      dataIndex: 'response_num',
      key: 'response_num',
      render: (num: number) => num?.toLocaleString() || '-',
    },
    {
      title: '数据类型',
      dataIndex: 'dataset_type',
      key: 'dataset_type',
      render: (type: string, record: Dataset) => {
        const typeColors: Record<string, string> = {
          'assistments': 'blue',
          'junyi': 'green',
          'neurips': 'purple',
          'custom': 'orange'
        };
        return (
          <Space>
            <Tag color={typeColors[type] || 'default'}>
              {type?.toUpperCase() || '未知'}
            </Tag>
            {record.has_q_matrix && <Tag color="cyan">Q矩阵</Tag>}
            {record.has_embeddings && <Tag color="magenta">嵌入</Tag>}
          </Space>
        );
      },
    },
    {
      title: '数据质量',
      key: 'data_quality',
      render: (record: Dataset) => {
        if (!record.data_quality) return '-';
        const quality = record.data_quality;
        const avgQuality = (quality.completeness + quality.consistency + quality.validity) / 3;
        const color = avgQuality > 0.8 ? 'success' : avgQuality > 0.6 ? 'warning' : 'error';
        return (
          <Tag color={color}>
            {(avgQuality * 100).toFixed(0)}%
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'success' : 'default'}>
          {active ? '活跃' : '非活跃'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Dataset) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewStats(record)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card
        title="数据集管理"
        extra={
          <Space>
            <Button
              type={showRealDataView ? 'default' : 'primary'}
              onClick={() => setShowRealDataView(false)}
            >
              数据库视图
            </Button>
            <Button
              type={showRealDataView ? 'primary' : 'default'}
              onClick={() => setShowRealDataView(true)}
            >
              真实数据视图
            </Button>
            <Button
              type="primary"
              icon={<SyncOutlined />}
              onClick={handleSyncDatasets}
              loading={loading}
            >
              同步数据集
            </Button>
          </Space>
        }
      >
        {showRealDataView ? (
          // 真实数据视图
          <div>
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic
                  title="可用数据集"
                  value={availableDatasets.length}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="训练就绪"
                  value={availableDatasets.filter(d =>
                    validationResults[d.name]?.valid && d.student_num > 100
                  ).length}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="总学生数"
                  value={availableDatasets.reduce((sum, d) => sum + (d.student_num || 0), 0)}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="总响应数"
                  value={availableDatasets.reduce((sum, d) => sum + (d.response_num || 0), 0)}
                />
              </Col>
            </Row>

            <Table
              columns={[
                {
                  title: '数据集名称',
                  dataIndex: 'display_name',
                  key: 'display_name',
                  render: (text: string, record: any) => (
                    <Space>
                      <DatabaseOutlined />
                      <span>{text}</span>
                      <Tag color="blue">{record.type}</Tag>
                    </Space>
                  ),
                },
                {
                  title: '学生数',
                  dataIndex: 'student_num',
                  key: 'student_num',
                  render: (num: number) => num?.toLocaleString() || '-',
                },
                {
                  title: '题目数',
                  dataIndex: 'exercise_num',
                  key: 'exercise_num',
                  render: (num: number) => num?.toLocaleString() || '-',
                },
                {
                  title: '知识点数',
                  dataIndex: 'knowledge_num',
                  key: 'knowledge_num',
                  render: (num: number) => num?.toLocaleString() || '-',
                },
                {
                  title: '数据特性',
                  key: 'features',
                  render: (record: any) => (
                    <Space>
                      {record.has_q_matrix && <Tag color="cyan">Q矩阵</Tag>}
                      {record.has_embeddings && <Tag color="magenta">嵌入</Tag>}
                      {record.has_hierarchy && <Tag color="purple">层次</Tag>}
                    </Space>
                  ),
                },
                {
                  title: '数据质量',
                  key: 'quality',
                  render: (record: any) => {
                    const validation = validationResults[record.name];
                    if (!validation) return <Tag color="default">未验证</Tag>;

                    if (validation.valid) {
                      const quality = validation.metadata?.data_quality;
                      if (quality) {
                        const avgQuality = (quality.completeness + quality.consistency + quality.validity) / 3;
                        const color = avgQuality > 0.8 ? 'success' : avgQuality > 0.6 ? 'warning' : 'error';
                        return <Tag color={color}>{(avgQuality * 100).toFixed(0)}%</Tag>;
                      }
                      return <Tag color="success">有效</Tag>;
                    } else {
                      return <Tag color="error">无效</Tag>;
                    }
                  },
                },
                {
                  title: '稀疏度',
                  dataIndex: 'sparsity',
                  key: 'sparsity',
                  render: (sparsity: number) => {
                    if (sparsity === undefined) return '-';
                    const color = sparsity > 0.95 ? 'error' : sparsity > 0.9 ? 'warning' : 'success';
                    return <Tag color={color}>{(sparsity * 100).toFixed(1)}%</Tag>;
                  },
                },
                {
                  title: '操作',
                  key: 'action',
                  render: (record: any) => (
                    <Space>
                      <Button
                        type="link"
                        icon={<EyeOutlined />}
                        onClick={() => handleViewRealDatasetStats(record)}
                      >
                        详情
                      </Button>
                    </Space>
                  ),
                },
              ]}
              dataSource={availableDatasets}
              rowKey="name"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个真实数据集`,
              }}
            />
          </div>
        ) : (
          // 原有数据库视图
          <Table
            columns={columns}
            dataSource={datasets}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个数据集`,
            }}
          />
        )}
      </Card>

      <Modal
        title={`数据集详情 - ${selectedDataset?.display_name}`}
        open={statsModalVisible}
        onCancel={() => setStatsModalVisible(false)}
        footer={null}
        width={800}
      >
        {statsLoading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
          </div>
        ) : datasetStats ? (
          <div>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Statistic
                  title="学生数量"
                  value={datasetStats.student_num}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="题目数量"
                  value={datasetStats.exercise_num}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="知识点数量"
                  value={datasetStats.knowledge_num}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="响应数量"
                  value={datasetStats.response_num}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
            </Row>

            <Descriptions title="统计信息" bordered column={2}>
              <Descriptions.Item label="平均每学生响应数">
                {datasetStats.avg_responses_per_student.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="平均每题目响应数">
                {datasetStats.avg_responses_per_exercise.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="数据稀疏度">
                {(datasetStats.sparsity * 100).toFixed(2)}%
              </Descriptions.Item>
              <Descriptions.Item label="平均难度">
                {datasetStats.difficulty_distribution.mean?.toFixed(3) || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="难度标准差">
                {datasetStats.difficulty_distribution.std?.toFixed(3) || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item label="最简单题目正确率">
                {datasetStats.difficulty_distribution.max?.toFixed(3) || 'N/A'}
              </Descriptions.Item>
            </Descriptions>

            {selectedDataset?.description && (
              <div style={{ marginTop: 16 }}>
                <h4>描述</h4>
                <p>{selectedDataset.description}</p>
              </div>
            )}
          </div>
        ) : (
          <div>暂无统计信息</div>
        )}
      </Modal>
    </div>
  );
};

export default DatasetManagement;
