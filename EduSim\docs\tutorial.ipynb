{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'ItemSpace'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[1;32m<ipython-input-1-ba5efaa502e2>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mgym\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 2\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEnvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mKSS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mKSSAgent\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[0menv\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mgym\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmake\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'KSS-v0'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mlearner_num\u001b[0m\u001b[1;33m=\u001b[0m\u001b[1;36m4000\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[0magent\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mKSSAgent\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0menv\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0maction_space\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mgym\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0menvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mregistration\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mregister\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 5\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mEnvs\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[1;33m*\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      6\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mSimOS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtrain_eval\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mMetaAgent\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mspaces\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mItemSpace\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\Envs\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;31m# create by tongs<PERSON><PERSON> on 2019/6/25\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 4\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mKSS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mKSSEnv\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mTMS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mTMSEnv\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\Envs\\KSS\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;31m# 2020/4/29 @ tongshiwei\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 4\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mEnv\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mKSSEnv\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mkss_train_eval\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mAgent\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mKSSAgent\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\Envs\\KSS\\Env.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEnvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmeta\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mEnv\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 8\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mLearner\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mLearnerGroup\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      9\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mKS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mget_knowledge_structure\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mQBank\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mQBank\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\Envs\\KSS\\Learner.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mnetworkx\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0mnx\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      8\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEnvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmeta\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mMetaLearner\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mMetaLearnerGroup\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mMetaLearningModel\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 9\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEnvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mshared\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mKSS_KES\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mKS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0minfluence_control\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     10\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[0m__all__\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;33m[\u001b[0m\u001b[1;34m\"Learner\"\u001b[0m\u001b[1;33m]\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\Envs\\shared\\KSS_KES\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mKS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mKS\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mReward\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mepisode_reward\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 6\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mtrain_eval\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mkss_kes_train_eval\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\Envs\\shared\\KSS_KES\\train_eval.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mlogging\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 5\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mSimOS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtrain_eval\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      6\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mutils\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mboard_episode_callback\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mtensorboardX\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mSummaryWriter\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\SimOS\\__init__.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      2\u001b[0m \u001b[1;31m# 2020/4/30 @ tongshiwei\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 4\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mSimOS\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mtrain_eval\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mMetaAgent\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mRandomAgent\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      5\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mconfig\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mas_level\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mF:\\Program\\Python\\EduSim\\EduSim\\SimOS\\SimOS.py\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEnvs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmeta\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mEnv\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mEnv\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 8\u001b[1;33m \u001b[1;32mfrom\u001b[0m \u001b[0mEduSim\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mItemSpace\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      9\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[1;33m.\u001b[0m\u001b[0mconfig\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mas_level\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     10\u001b[0m \u001b[1;32mfrom\u001b[0m \u001b[0mlongling\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mML\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mtoolkit\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmonitor\u001b[0m \u001b[1;32mimport\u001b[0m \u001b[0mConsoleProgressMonitor\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mEMAValue\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mImportError\u001b[0m: cannot import name 'ItemSpace'"]}], "source": ["import gym\n", "from EduSim.Envs.KSS import KSSAgent\n", "\n", "env = gym.make('KSS-v0', learner_num=4000)\n", "agent = KSSAgent(env.action_space)\n", "max_episode_num = 1000\n", "n_step = False\n", "max_steps = 20\n", "train = True\n", "\n", "episode = 0\n", "\n", "while True:\n", "    if max_episode_num is not None and episode > max_episode_num:\n", "        break\n", "\n", "    try:\n", "        agent.begin_episode(env.begin_episode())\n", "        episode += 1\n", "    except ValueError:  # pragma: no cover\n", "        break\n", "\n", "    # recommend and learn\n", "    if n_step is True:\n", "        # generate a learning path\n", "        learning_path = agent.n_step(max_steps)\n", "        env.n_step(learning_path)\n", "    else:\n", "        # generate a learning path step by step\n", "        for _ in range(max_steps):\n", "            try:\n", "                learning_item = agent.step()\n", "            except ValueError:  # pragma: no cover\n", "                break\n", "            interaction, _ ,_, _ = env.step(learning_item)\n", "            agent.observe(**interaction[\"performance\"])\n", "\n", "    # test the learner to see the learning effectiveness\n", "    agent.episode_reward(env.end_episode()[\"reward\"])\n", "    agent.end_episode()\n", "\n", "    if train is True:\n", "        agent.tune()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 0}