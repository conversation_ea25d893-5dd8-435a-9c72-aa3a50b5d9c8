"""
序列化优化工具
"""
import json
import numpy as np
import pandas as pd
from typing import Any, Dict, List, Union
import logging

logger = logging.getLogger(__name__)

class NumpyEncoder(json.JSONEncoder):
    """Numpy数组JSON编码器"""
    
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, pd.Series):
            return obj.tolist()
        return super().default(obj)

def safe_serialize(data: Any) -> Dict[str, Any]:
    """安全序列化数据"""
    
    def convert_value(value):
        """转换单个值"""
        if isinstance(value, np.ndarray):
            return value.tolist()
        elif isinstance(value, (np.integer, np.int64, np.int32)):
            return int(value)
        elif isinstance(value, (np.floating, np.float64, np.float32)):
            return float(value)
        elif isinstance(value, np.bool_):
            return bool(value)
        elif isinstance(value, pd.DataFrame):
            return value.to_dict('records')
        elif isinstance(value, pd.Series):
            return value.tolist()
        elif isinstance(value, dict):
            return {str(k): convert_value(v) for k, v in value.items()}
        elif isinstance(value, (list, tuple)):
            return [convert_value(item) for item in value]
        else:
            return value
    
    try:
        converted_data = convert_value(data)
        
        # 验证可以序列化
        json.dumps(converted_data, cls=NumpyEncoder)
        
        return converted_data
    
    except Exception as e:
        logger.error(f"序列化失败: {e}")
        
        # 返回安全的错误信息
        return {
            'error': 'Serialization failed',
            'message': str(e),
            'data_type': str(type(data))
        }

def optimize_training_data_response(training_data: Dict[str, Any]) -> Dict[str, Any]:
    """优化训练数据响应"""
    
    optimized_data = {}
    
    # 处理元数据
    if 'metadata' in training_data:
        metadata = training_data['metadata']
        optimized_data['metadata'] = {
            'dataset_name': metadata.get('dataset_name', ''),
            'display_name': metadata.get('display_name', ''),
            'type': metadata.get('type', ''),
            'student_num': int(metadata.get('student_num', 0)),
            'exercise_num': int(metadata.get('exercise_num', 0)),
            'knowledge_num': int(metadata.get('knowledge_num', 0)),
            'response_num': int(metadata.get('response_num', 0)),
            'sparsity': float(metadata.get('sparsity', 0.0)),
            'has_q_matrix': bool(metadata.get('has_q_matrix', False)),
            'has_embeddings': bool(metadata.get('has_embeddings', False))
        }
    
    # 处理训练数据
    if 'training_data' in training_data:
        train_data = training_data['training_data']
        
        # 基本信息
        optimized_data['training_data'] = {
            'student_num': int(train_data.get('student_num', 0)),
            'exercise_num': int(train_data.get('exercise_num', 0)),
            'knowledge_num': int(train_data.get('knowledge_num', 0)),
        }
        
        # 数据集大小信息（不包含实际数据）
        if 'train_data' in train_data:
            train_array = train_data['train_data']
            if isinstance(train_array, list) and train_array:
                optimized_data['training_data']['train_size'] = len(train_array)
                optimized_data['training_data']['train_sample'] = train_array[:5]  # 只返回前5条样本
        
        if 'val_data' in train_data:
            val_array = train_data['val_data']
            if isinstance(val_array, list) and val_array:
                optimized_data['training_data']['val_size'] = len(val_array)
                optimized_data['training_data']['val_sample'] = val_array[:5]
        
        if 'test_data' in train_data:
            test_array = train_data['test_data']
            if isinstance(test_array, list) and test_array:
                optimized_data['training_data']['test_size'] = len(test_array)
                optimized_data['training_data']['test_sample'] = test_array[:5]
        
        # ID映射（转换为字符串键）
        if 'student_id_map' in train_data:
            student_map = train_data['student_id_map']
            if isinstance(student_map, dict):
                optimized_data['training_data']['student_id_map_size'] = len(student_map)
                # 只返回前10个映射作为示例
                sample_items = list(student_map.items())[:10]
                optimized_data['training_data']['student_id_map_sample'] = {
                    str(k): int(v) for k, v in sample_items
                }
        
        if 'exercise_id_map' in train_data:
            exercise_map = train_data['exercise_id_map']
            if isinstance(exercise_map, dict):
                optimized_data['training_data']['exercise_id_map_size'] = len(exercise_map)
                sample_items = list(exercise_map.items())[:10]
                optimized_data['training_data']['exercise_id_map_sample'] = {
                    str(k): int(v) for k, v in sample_items
                }
        
        # Q矩阵信息
        if 'q_matrix' in train_data:
            q_matrix = train_data['q_matrix']
            if isinstance(q_matrix, list) and q_matrix:
                optimized_data['training_data']['q_matrix_shape'] = [len(q_matrix), len(q_matrix[0]) if q_matrix[0] else 0]
                optimized_data['training_data']['q_matrix_sample'] = q_matrix[:3]  # 前3行
    
    return optimized_data

def create_data_summary(data: Any) -> Dict[str, Any]:
    """创建数据摘要"""
    
    summary = {
        'type': str(type(data).__name__),
        'size': 0,
        'shape': None,
        'sample': None
    }
    
    try:
        if isinstance(data, np.ndarray):
            summary['size'] = data.size
            summary['shape'] = list(data.shape)
            summary['dtype'] = str(data.dtype)
            if data.size > 0:
                summary['sample'] = data.flat[:min(5, data.size)].tolist()
        
        elif isinstance(data, list):
            summary['size'] = len(data)
            if data:
                summary['sample'] = data[:min(5, len(data))]
        
        elif isinstance(data, dict):
            summary['size'] = len(data)
            summary['keys'] = list(data.keys())[:10]  # 前10个键
        
        elif isinstance(data, pd.DataFrame):
            summary['size'] = len(data)
            summary['shape'] = list(data.shape)
            summary['columns'] = list(data.columns)
            if not data.empty:
                summary['sample'] = data.head(3).to_dict('records')
        
        elif isinstance(data, str):
            summary['size'] = len(data)
            summary['sample'] = data[:100] + '...' if len(data) > 100 else data
        
        else:
            summary['sample'] = str(data)[:100]
    
    except Exception as e:
        summary['error'] = str(e)
    
    return summary

def test_serialization():
    """测试序列化功能"""
    
    # 测试数据
    test_data = {
        'numpy_array': np.array([[1, 2, 3], [4, 5, 6]]),
        'numpy_int': np.int64(42),
        'numpy_float': np.float32(3.14),
        'numpy_bool': np.bool_(True),
        'regular_list': [1, 2, 3],
        'regular_dict': {'a': 1, 'b': 2},
        'mixed_dict': {
            'data': np.array([1, 2, 3]),
            'count': np.int32(10),
            'rate': np.float64(0.95)
        }
    }
    
    print("=== 序列化测试 ===")
    
    # 测试安全序列化
    safe_data = safe_serialize(test_data)
    print(f"✓ 安全序列化成功")
    print(f"  原始键数: {len(test_data)}")
    print(f"  序列化后键数: {len(safe_data)}")
    
    # 测试JSON序列化
    try:
        json_str = json.dumps(safe_data, indent=2)
        print(f"✓ JSON序列化成功")
        print(f"  JSON长度: {len(json_str)} 字符")
    except Exception as e:
        print(f"✗ JSON序列化失败: {e}")
    
    # 测试数据摘要
    summary = create_data_summary(test_data['numpy_array'])
    print(f"✓ 数据摘要创建成功")
    print(f"  摘要: {summary}")
    
    return True

if __name__ == "__main__":
    test_serialization()
