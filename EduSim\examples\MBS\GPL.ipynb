{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": "{'action_space': [0,\n 1,\n 2,\n 3,\n 4,\n 5,\n 6,\n 7,\n 8,\n 9,\n 10,\n 11,\n 12,\n 13,\n 14,\n 15,\n 16,\n 17,\n 18,\n 19,\n 20,\n 21,\n 22,\n 23,\n 24,\n 25,\n 26,\n 27,\n 28,\n 29]}"}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import gym\n", "from EduSim.Envs.MBS import GPLEnv, MBSAgent, mbs_train_eval\n", "\n", "env: GPLEnv = gym.make(\"MBS-GPL-v0\", seed=10, n_steps=200)\n", "agent = MBSAgent(env.action_space)\n", "\n", "env"]}, {"cell_type": "code", "execution_count": 2, "outputs": [{"data": {"text/plain": "[0,\n 1,\n 2,\n 3,\n 4,\n 5,\n 6,\n 7,\n 8,\n 9,\n 10,\n 11,\n 12,\n 13,\n 14,\n 15,\n 16,\n 17,\n 18,\n 19,\n 20,\n 21,\n 22,\n 23,\n 24,\n 25,\n 26,\n 27,\n 28,\n 29]"}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["env.action_space"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 3, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Episode|    Total-E   Episode-Reward             Progress           \n", "       100|        100         0.851272     [01:25<00:00, 1.18it/s]    \n", "done\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Expected Reward: 0.8226666666666662\n"]}], "source": ["from longling import set_logging_info\n", "set_logging_info()\n", "mbs_train_eval(\n", "    agent,\n", "    env,\n", "    max_steps=200,\n", "    max_episode_num=100,\n", "    level=\"summary\",\n", ")\n", "print(\"done\")"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "3.7.6-final"}}, "nbformat": 4, "nbformat_minor": 0}