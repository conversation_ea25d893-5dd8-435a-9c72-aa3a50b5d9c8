"""
API路由汇总
"""
from fastapi import APIRouter

from app.api.v1.endpoints import datasets, experiments, models, visualization, dashboard, cognitive_diagnosis, llm, demo_results, embedding, system, auth, learning_path

api_router = APIRouter()

# 注册各个模块的路由
# 认证路由（不需要前缀，直接在根路径下）
api_router.include_router(auth.router, tags=["auth"])

api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])
api_router.include_router(datasets.router, prefix="/datasets", tags=["datasets"])
api_router.include_router(experiments.router, prefix="/experiments", tags=["experiments"])
api_router.include_router(models.router, prefix="/models", tags=["models"])
api_router.include_router(visualization.router, prefix="/visualization", tags=["visualization"])
api_router.include_router(cognitive_diagnosis.router, prefix="/cognitive-diagnosis", tags=["cognitive-diagnosis"])
api_router.include_router(llm.router, prefix="/llm", tags=["llm"])
api_router.include_router(demo_results.router, prefix="/demo-results", tags=["demo-results"])
api_router.include_router(embedding.router, prefix="/embedding", tags=["embedding"])
api_router.include_router(system.router, prefix="/system", tags=["system"])
api_router.include_router(learning_path.router, prefix="/learning-path", tags=["learning-path"])
