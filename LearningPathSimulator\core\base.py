"""
基础类定义模块

定义了仿真器的基础抽象类，为所有组件提供统一接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional
import numpy as np


class BaseAgent(ABC):
    """智能体基类
    
    所有路径规划算法都需要继承此类并实现相应方法。
    """
    
    def __init__(self, action_space_size: int, name: str = "BaseAgent"):
        """
        初始化智能体
        
        Args:
            action_space_size: 动作空间大小（知识点数量）
            name: 智能体名称
        """
        self.action_space_size = action_space_size
        self.name = name
        self.episode_count = 0
        self.total_reward = 0.0
        
    @abstractmethod
    def get_action(self, observation: np.ndarray) -> int:
        """
        根据当前观察选择动作
        
        Args:
            observation: 当前学习状态向量 [0,1]^n
            
        Returns:
            选择的动作（知识点索引）
        """
        pass
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """
        更新智能体参数（可选实现）
        
        Args:
            observation: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_observation: 下一状态
            done: 是否结束
        """
        self.total_reward += reward
    
    def reset(self):
        """重置智能体状态"""
        self.episode_count += 1
    
    def get_info(self) -> Dict[str, Any]:
        """获取智能体信息"""
        return {
            'name': self.name,
            'episode_count': self.episode_count,
            'total_reward': self.total_reward,
            'avg_reward': self.total_reward / max(1, self.episode_count)
        }


class BaseEnvironment(ABC):
    """环境基类
    
    定义了学习环境的标准接口。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化环境
        
        Args:
            config: 环境配置参数
        """
        self.config = config
        self.current_state = None
        self.step_count = 0
        self.episode_count = 0
        
    @abstractmethod
    def reset(self) -> np.ndarray:
        """
        重置环境到初始状态
        
        Returns:
            初始观察状态
        """
        pass
    
    @abstractmethod
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict[str, Any]]:
        """
        执行一个动作
        
        Args:
            action: 要执行的动作
            
        Returns:
            observation: 新的状态
            reward: 获得的奖励
            done: 是否结束
            info: 额外信息
        """
        pass
    
    @abstractmethod
    def get_state_info(self) -> Dict[str, Any]:
        """获取当前状态的详细信息"""
        pass
    
    def render(self, mode: str = 'human'):
        """渲染环境（可选实现）"""
        pass


class BaseEvaluator(ABC):
    """评估器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
    @abstractmethod
    def evaluate(self, agent: BaseAgent, environment: BaseEnvironment, 
                num_episodes: int) -> Dict[str, Any]:
        """
        评估智能体性能
        
        Args:
            agent: 待评估的智能体
            environment: 评估环境
            num_episodes: 评估回合数
            
        Returns:
            评估结果字典
        """
        pass


class BaseMetric(ABC):
    """评估指标基类"""
    
    def __init__(self, name: str):
        self.name = name
        
    @abstractmethod
    def calculate(self, data: List[Dict[str, Any]]) -> float:
        """
        计算指标值
        
        Args:
            data: 评估数据列表
            
        Returns:
            指标值
        """
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取指标描述"""
        pass


class BaseStatisticalTest(ABC):
    """统计检验基类"""
    
    def __init__(self, name: str, alpha: float = 0.05):
        self.name = name
        self.alpha = alpha
        
    @abstractmethod
    def test(self, group1: List[float], group2: List[float]) -> Dict[str, Any]:
        """
        执行统计检验
        
        Args:
            group1: 第一组数据
            group2: 第二组数据
            
        Returns:
            检验结果字典，包含p值、统计量等
        """
        pass


class BaseCognitiveModel(ABC):
    """认知模型基类"""
    
    def __init__(self, name: str, parameters: Dict[str, float]):
        self.name = name
        self.parameters = parameters
        
    @abstractmethod
    def apply(self, state: np.ndarray, action: int, **kwargs) -> float:
        """
        应用认知模型
        
        Args:
            state: 当前学习状态
            action: 学习动作
            **kwargs: 其他参数
            
        Returns:
            模型输出值
        """
        pass
    
    def update_parameters(self, new_parameters: Dict[str, float]):
        """更新模型参数"""
        self.parameters.update(new_parameters)


class BaseRewardFunction(ABC):
    """奖励函数基类"""
    
    def __init__(self, name: str, weight: float = 1.0):
        self.name = name
        self.weight = weight
        
    @abstractmethod
    def calculate(self, old_state: np.ndarray, action: int, 
                 new_state: np.ndarray, info: Dict[str, Any]) -> float:
        """
        计算奖励值
        
        Args:
            old_state: 动作前状态
            action: 执行的动作
            new_state: 动作后状态
            info: 额外信息
            
        Returns:
            奖励值
        """
        pass


class BaseVisualization(ABC):
    """可视化基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
    @abstractmethod
    def plot(self, data: Dict[str, Any], save_path: Optional[str] = None):
        """
        绘制图表
        
        Args:
            data: 要可视化的数据
            save_path: 保存路径（可选）
        """
        pass
