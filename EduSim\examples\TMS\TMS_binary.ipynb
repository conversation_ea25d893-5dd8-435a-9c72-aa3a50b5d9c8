{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": "{'mode': 'with_measurement_error',\n 'skill_num': 2,\n 'test_item_for_each_skill': 4}"}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 4.1 Study I\n", "\n", "import gym\n", "from EduSim.Envs.TMS import TMSEnv, TMSAgent, tms_train_eval\n", "\n", "env: TMSEnv = gym.make(\"TMS-v1\", name=\"binary\", seed=10)\n", "agent = TMSAgent(env.action_space)\n", "\n", "env"]}, {"cell_type": "code", "execution_count": 5, "outputs": [{"data": {"text/plain": "['d0', 'd1']"}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["env.action_space"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}}, {"cell_type": "code", "execution_count": 6, "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Episode|    Total-E   Episode-Reward             Progress           \n", "   Episode|    Total-E   Episode-Reward             Progress           \n", "     10000|      10000         0.435759    [00:45<00:00, 221.16it/s]   \n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:root:Expected Reward: 0.5966\n"]}], "source": ["from longling import set_logging_info\n", "set_logging_info()\n", "tms_train_eval(\n", "    agent,\n", "    env,\n", "    max_steps=2,\n", "    max_episode_num=100 * 100,  # 4.1.2\n", "    level=\"summary\",\n", "    board_dir=\"./tms_binary\"\n", ")\n", "print(\"done\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.6"}}, "nbformat": 4, "nbformat_minor": 1}