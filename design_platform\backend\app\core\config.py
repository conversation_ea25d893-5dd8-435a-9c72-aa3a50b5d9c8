"""
应用配置文件
"""
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import validator
import os


class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    PROJECT_NAME: str = "知擎EduBrain双驱协同认知诊断与规划系统"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 服务器配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 数据库配置
    DATABASE_URL: str = "sqlite:///./edubrain_platform.db"

    # LLM配置
    SILICONFLOW_API_KEY: str = "sk-nncubqwtmpkzknkqepoyhqapagmllicumpbpwsxsqaapvtsz"
    LLM_MODEL: str = "Qwen/Qwen2.5-7B-Instruct"
    LLM_BASE_URL: str = "https://api.siliconflow.cn/v1/chat/completions"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # CORS配置 - 支持内网穿透
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:3001",
        # 支持所有ngrok域名 (HTTP和HTTPS)
        "http://*.ngrok.io",
        "https://*.ngrok.io",
        "http://*.ngrok-free.app",
        "https://*.ngrok-free.app",
        # 支持所有cpolar域名 (HTTP和HTTPS)
        "http://*.cpolar.top",
        "https://*.cpolar.top",
        "http://*.cpolar.cn",
        "https://*.cpolar.cn",
        "http://*.cpolar.io",
        "https://*.cpolar.io",
        # 支持所有frp域名 (HTTP和HTTPS)
        "http://*.frp.fun",
        "https://*.frp.fun",
        "http://*.frps.cn",
        "https://*.frps.cn",
        # 开发环境允许所有来源（仅用于演示）
        "*"
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # 文件存储配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    
    # 数据集配置
    DATASETS_DIR: str = "../../datasets"
    MODELS_DIR: str = "models"
    RESULTS_DIR: str = "results"
    
    # 训练配置
    DEFAULT_BATCH_SIZE: int = 256
    DEFAULT_EPOCHS: int = 10
    DEFAULT_LEARNING_RATE: float = 0.001
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建设置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.MODELS_DIR, exist_ok=True)
os.makedirs(settings.RESULTS_DIR, exist_ok=True)
os.makedirs("logs", exist_ok=True)
