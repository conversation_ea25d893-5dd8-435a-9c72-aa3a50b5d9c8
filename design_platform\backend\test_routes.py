#!/usr/bin/env python3
"""
测试路由配置
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

def test_route_import():
    """测试路由导入"""
    try:
        from app.api.v1.endpoints.datasets import router
        print("✓ 数据集路由导入成功")
        
        # 检查路由
        routes = []
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append(f"{list(route.methods)[0]} {route.path}")
        
        print(f"发现 {len(routes)} 个路由:")
        for route in routes:
            print(f"  {route}")
        
        return True
        
    except Exception as e:
        print(f"✗ 路由导入失败: {e}")
        return False

def test_app_import():
    """测试应用导入"""
    try:
        from app.main import app
        print("✓ 应用导入成功")
        
        # 检查所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        print(f"应用总路由数: {len(routes)}")
        
        # 查找数据集相关路由
        dataset_routes = [r for r in routes if '/datasets' in r]
        print(f"数据集路由: {len(dataset_routes)}")
        for route in dataset_routes:
            print(f"  {route}")
        
        return True
        
    except Exception as e:
        print(f"✗ 应用导入失败: {e}")
        return False

if __name__ == "__main__":
    print("测试路由配置...")
    
    success_count = 0
    
    if test_route_import():
        success_count += 1
    
    if test_app_import():
        success_count += 1
    
    print(f"\n测试结果: {success_count}/2")
    
    if success_count == 2:
        print("✓ 路由配置正常")
    else:
        print("✗ 路由配置有问题")
