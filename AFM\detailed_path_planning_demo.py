#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的路径规划策略对比演示
展示不同智能体的决策过程和路径规划逻辑
"""

import numpy as np
import pickle
import random
from typing import List, Dict, Tuple
import json
from datetime import datetime

# 导入AFM模型
from AFM import AFM
from optimized_simulator import OptimizedLearningEnvironment, evaluate_agent_optimized

class DetailedAFMAgent:
    """详细的AFM智能体 - 使用真实AFM模型预测"""

    def __init__(self, user_embedding, knowledge_embeddings, action_space_size: int, user_id: int = 0):
        self.user_embedding = user_embedding.flatten()
        self.knowledge_embeddings = knowledge_embeddings
        self.action_space_size = action_space_size
        self.step_count = 0
        self.user_id = user_id

        # 尝试使用真实AFM模型
        self.real_afm_agent = self._create_real_afm_agent()

        print(f"🤖 AFM智能体初始化:")
        print(f"  用户嵌入维度: {self.user_embedding.shape}")
        print(f"  知识点嵌入数量: {len(self.knowledge_embeddings)}")
        print(f"  使用真实AFM模型: {'是' if self.real_afm_agent else '否'}")

    def _create_real_afm_agent(self):
        """创建真实AFM智能体"""
        try:
            from optimized_simulator import RealAFMAgent
            agent = RealAFMAgent(action_space_size=self.action_space_size, user_id=self.user_id)
            if agent.model is not None:
                print("✓ 成功创建真实AFM智能体")
                return agent
            else:
                print("✗ AFM模型加载失败")
                return None
        except Exception as e:
            print(f"✗ 创建真实AFM智能体失败: {e}")
            print("  将使用备用的模拟AFM模型")
            return None

    def _create_mock_afm_model(self):
        """创建模拟AFM模型（备用）"""
        class MockAFM:
            def __init__(self):
                # 模拟AFM的权重参数
                self.linear_weights = np.random.normal(0, 0.1, 100)
                self.interaction_weights = np.random.normal(0, 0.05, 50)
                self.attention_weights = np.random.uniform(0.1, 1.0, 50)

            def predict(self, user_vec, kp_vec):
                # 模拟AFM的预测过程
                # 1. 特征拼接
                features = np.concatenate([user_vec[:50], kp_vec[:50]])

                # 2. 线性部分
                linear_part = np.dot(features, self.linear_weights[:len(features)])

                # 3. 特征交互部分
                interactions = []
                for i in range(0, len(features), 2):
                    if i+1 < len(features):
                        interaction = features[i] * features[i+1]
                        interactions.append(interaction)

                if interactions:
                    interactions = np.array(interactions[:len(self.interaction_weights)])
                    # 注意力加权
                    attention_scores = interactions * self.attention_weights[:len(interactions)]
                    interaction_part = np.sum(attention_scores)
                else:
                    interaction_part = 0

                # 4. 最终预测（sigmoid激活）
                prediction = 1 / (1 + np.exp(-(linear_part + interaction_part)))
                return prediction

        return MockAFM()
    
    def reset(self):
        self.step_count = 0
        
    def get_action(self, observation: np.ndarray) -> int:
        """AFM路径规划：基于真实或模拟AFM模型预测选择最佳学习动作"""

        print(f"\n🧠 AFM智能体第{self.step_count+1}步决策:")
        print(f"当前掌握程度: {[f'{x:.2f}' for x in observation]}")

        if self.real_afm_agent:
            # 使用真实AFM智能体
            best_action = self.real_afm_agent.get_action(observation)
            print(f"  → 真实AFM模型选择动作{best_action}")
        else:
            # 使用备用的模拟AFM模型
            if not hasattr(self, 'afm_model'):
                self.afm_model = self._create_mock_afm_model()

            action_scores = []
            predictions = []

            for action in range(self.action_space_size):
                # 获取知识点嵌入
                if action < len(self.knowledge_embeddings):
                    kp_embedding = self.knowledge_embeddings[action].flatten()
                else:
                    kp_embedding = np.random.normal(0, 0.1, len(self.user_embedding))

                # AFM模型预测学习效果
                afm_prediction = self.afm_model.predict(self.user_embedding, kp_embedding)
                predictions.append(afm_prediction)

                # 结合当前掌握程度
                mastery_need = 1.0 - observation[action]

                # 综合得分：AFM预测权重更高
                combined_score = afm_prediction * 0.8 + mastery_need * 0.2
                action_scores.append(combined_score)

                print(f"  动作{action}: AFM预测={afm_prediction:.3f}, 掌握需求={mastery_need:.3f}, 综合={combined_score:.3f}")

            best_action = np.argmax(action_scores)
            print(f"  → 模拟AFM模型选择动作{best_action}")

        self.step_count += 1
        return best_action

class FixedPathAgent:
    """固定路径智能体 - 预先规划好的学习序列"""
    
    def __init__(self, action_space_size: int, strategy: str = "sequential"):
        self.action_space_size = action_space_size
        self.strategy = strategy
        self.current_step = 0
        
        # 根据策略生成固定路径
        self.learning_path = self._generate_fixed_path()
        
        print(f"📋 固定路径智能体初始化:")
        print(f"  策略: {strategy}")
        print(f"  学习路径: {self.learning_path}")
        
    def _generate_fixed_path(self) -> List[int]:
        """生成固定学习路径"""
        if self.strategy == "sequential":
            # 顺序学习：0→1→2→3→4
            return list(range(self.action_space_size)) * 3  # 重复3次
        elif self.strategy == "reverse":
            # 逆序学习：4→3→2→1→0
            return list(range(self.action_space_size-1, -1, -1)) * 3
        elif self.strategy == "difficulty_based":
            # 基于假设难度：简单→困难
            return [0, 1, 0, 2, 1, 3, 2, 4, 3, 4] * 2
        else:
            return list(range(self.action_space_size)) * 3
    
    def reset(self):
        self.current_step = 0
        
    def get_action(self, observation: np.ndarray) -> int:
        """固定路径规划：按预定序列学习"""
        
        if self.current_step < len(self.learning_path):
            action = self.learning_path[self.current_step]
        else:
            # 路径结束后重复最后一个动作
            action = self.learning_path[-1]
        
        print(f"📋 固定路径第{self.current_step+1}步: 动作{action} (预定路径)")
        
        self.current_step += 1
        return action

class AdaptiveGreedyAgent:
    """自适应贪心智能体 - 考虑依赖关系的动态规划"""
    
    def __init__(self, dependency_matrix: np.ndarray):
        self.dependency_matrix = dependency_matrix
        self.step_count = 0
        
        print(f"🎯 自适应贪心智能体初始化:")
        print(f"  依赖关系矩阵: {dependency_matrix.shape}")
        
    def reset(self):
        self.step_count = 0
        
    def get_action(self, observation: np.ndarray) -> int:
        """自适应贪心路径规划：动态选择最需要且条件满足的知识点"""
        
        print(f"\n🎯 自适应贪心第{self.step_count+1}步决策:")
        print(f"当前掌握程度: {[f'{x:.2f}' for x in observation]}")
        
        scores = []
        
        for i in range(len(observation)):
            # 1. 基础需求得分（掌握程度越低越需要）
            need_score = 1 - observation[i]
            
            # 2. 前置条件满足度
            prereq_score = 1.0
            prereq_info = []
            
            for j in range(len(observation)):
                if self.dependency_matrix[i, j] > 0:
                    dependency_weight = self.dependency_matrix[i, j]
                    if observation[j] < 0.4:  # 前置条件不满足
                        prereq_score *= (1 - dependency_weight * 0.5)
                        prereq_info.append(f"依赖KP{j}(未满足)")
                    else:
                        prereq_info.append(f"依赖KP{j}(已满足)")
            
            # 3. 学习效率得分（避免重复学习已掌握的）
            efficiency_score = 1.0
            if observation[i] > 0.8:  # 已经很好掌握了
                efficiency_score = 0.1
            
            # 4. 综合得分
            combined_score = need_score * prereq_score * efficiency_score
            scores.append(combined_score)
            
            prereq_str = ", ".join(prereq_info) if prereq_info else "无依赖"
            print(f"  动作{i}: 需求={need_score:.3f}, 前置={prereq_score:.3f}, 效率={efficiency_score:.3f}, 综合={combined_score:.3f} ({prereq_str})")
        
        best_action = np.argmax(scores)
        print(f"  → 选择动作{best_action} (综合得分最高)")
        
        self.step_count += 1
        return best_action

class RandomAgent:
    """随机智能体 - 完全随机选择"""
    
    def __init__(self, action_space_size: int):
        self.action_space_size = action_space_size
        self.step_count = 0
        
    def reset(self):
        self.step_count = 0
        
    def get_action(self, observation: np.ndarray) -> int:
        """随机路径规划：完全随机选择"""
        action = random.randint(0, self.action_space_size - 1)
        print(f"🎲 随机智能体第{self.step_count+1}步: 动作{action} (随机选择)")
        self.step_count += 1
        return action

class SimpleGreedyAgent:
    """简单贪心智能体 - 总是选择掌握程度最低的"""
    
    def __init__(self):
        self.step_count = 0
        
    def reset(self):
        self.step_count = 0
        
    def get_action(self, observation: np.ndarray) -> int:
        """简单贪心路径规划：总是选择掌握程度最低的知识点"""
        action = np.argmin(observation)
        print(f"⚡ 简单贪心第{self.step_count+1}步: 动作{action} (掌握程度最低: {observation[action]:.3f})")
        self.step_count += 1
        return action

def run_detailed_episode(agent, env: OptimizedLearningEnvironment, agent_name: str, max_steps: int = 20):
    """运行详细的单回合演示"""
    
    print(f"\n{'='*60}")
    print(f"🎮 {agent_name} 详细学习过程演示")
    print(f"{'='*60}")
    
    obs = env.reset()
    agent.reset()
    
    episode_reward = 0
    steps = 0
    done = False
    
    print(f"初始状态: {[f'{x:.3f}' for x in obs]}")
    print(f"学习目标: {env.min_success_kps}个知识点达到{env.success_threshold}")
    
    while not done and steps < max_steps:
        print(f"\n--- 第 {steps+1} 步 ---")
        
        action = agent.get_action(obs)
        obs, reward, done, info = env.step(action)
        episode_reward += reward
        steps += 1
        
        success_kps = np.sum(obs > env.success_threshold)
        
        print(f"执行结果:")
        print(f"  奖励: {reward:.4f}")
        print(f"  新状态: {[f'{x:.3f}' for x in obs]}")
        print(f"  成功知识点数: {success_kps}/{env.min_success_kps}")
        print(f"  是否完成: {done}")
        
        if done:
            final_score = env.get_final_score()
            is_successful = env.is_successful()
            print(f"\n🎯 回合结束!")
            print(f"  最终得分: {final_score:.4f}")
            print(f"  是否成功: {'✅' if is_successful else '❌'}")
            print(f"  总步数: {steps}")
            print(f"  总奖励: {episode_reward:.4f}")
    
    return {
        'final_score': env.get_final_score(),
        'success': env.is_successful(),
        'steps': steps,
        'total_reward': episode_reward
    }

def load_embeddings():
    """加载嵌入文件"""
    try:
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings_raw = pickle.load(f)
        
        with open('itm_emb_np.pkl', 'rb') as f:
            knowledge_embeddings_raw = pickle.load(f)
        
        user_embeddings = np.array(user_embeddings_raw)
        knowledge_embeddings = np.array(knowledge_embeddings_raw)
        
        return user_embeddings, knowledge_embeddings
        
    except Exception as e:
        print(f"嵌入加载失败: {e}")
        return None, None

def main():
    """主演示函数"""
    
    print("🎯 详细路径规划策略对比演示")
    print("=" * 80)
    
    # 创建环境
    env = OptimizedLearningEnvironment(num_knowledge_points=5, seed=42)
    
    print(f"\n🏫 学习环境配置:")
    print(f"  知识点数量: {env.num_kps}")
    print(f"  知识点难度: {[f'{d:.3f}' for d in env.difficulty]}")
    print(f"  成功标准: {env.min_success_kps}个知识点达到{env.success_threshold}")
    print(f"  最大步数: {env.max_steps}")
    print(f"  学习率: {env.learning_rate}")
    
    print(f"\n📊 依赖关系矩阵:")
    for i, row in enumerate(env.dependency_matrix):
        print(f"  KP{i}: {[f'{x:.1f}' for x in row]}")
    
    # 加载嵌入
    user_embeddings, knowledge_embeddings = load_embeddings()
    
    # 创建不同的智能体
    agents = []
    
    if user_embeddings is not None:
        agents.append(("AFM智能体", DetailedAFMAgent(
            user_embeddings[0], knowledge_embeddings, env.action_space_size
        )))
    
    agents.extend([
        ("自适应贪心智能体", AdaptiveGreedyAgent(env.dependency_matrix)),
        ("固定路径智能体(顺序)", FixedPathAgent(env.action_space_size, "sequential")),
        ("固定路径智能体(难度)", FixedPathAgent(env.action_space_size, "difficulty_based")),
        ("简单贪心智能体", SimpleGreedyAgent()),
        ("随机智能体", RandomAgent(env.action_space_size))
    ])
    
    # 运行详细演示
    results = {}
    
    for agent_name, agent in agents:
        result = run_detailed_episode(agent, env, agent_name, max_steps=15)
        results[agent_name] = result
    
    # 总结对比
    print(f"\n{'='*80}")
    print(f"📊 路径规划策略对比总结")
    print(f"{'='*80}")
    
    print(f"\n🏆 性能排名:")
    sorted_results = sorted(results.items(), key=lambda x: x[1]['final_score'], reverse=True)
    
    for i, (agent_name, result) in enumerate(sorted_results):
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"  {i+1}. {agent_name}:")
        print(f"     最终得分: {result['final_score']:.4f}")
        print(f"     学习步数: {result['steps']}")
        print(f"     总奖励: {result['total_reward']:.4f}")
        print(f"     结果: {status}")
    
    print(f"\n🔍 策略分析:")
    print(f"  🤖 AFM智能体: 基于机器学习模型预测，个性化程度最高")
    print(f"  🎯 自适应贪心: 考虑依赖关系，逻辑清晰，适应性强")
    print(f"  📋 固定路径: 预先规划，执行稳定，但缺乏适应性")
    print(f"  ⚡ 简单贪心: 策略简单，计算高效，但忽略依赖关系")
    print(f"  🎲 随机策略: 完全随机，作为基线对比")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"detailed_path_planning_results_{timestamp}.json"
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': timestamp,
            'description': '详细路径规划策略对比演示',
            'environment_config': {
                'num_knowledge_points': env.num_kps,
                'success_threshold': env.success_threshold,
                'min_success_kps': env.min_success_kps,
                'learning_rate': env.learning_rate
            },
            'results': results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细结果已保存到: {filename}")

if __name__ == "__main__":
    main()
