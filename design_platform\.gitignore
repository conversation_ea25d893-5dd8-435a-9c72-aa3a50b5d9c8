# 依赖目录
node_modules/
venv/
env/
.env

# 构建输出
build/
dist/
out/

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 缓存目录
.cache/
.parcel-cache/
.npm
.eslintcache

# Python 缓存
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 测试覆盖率
coverage/
.nyc_output
.coverage
.pytest_cache/
htmlcov/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 数据库文件（开发时保留，生产环境需要备份）
# *.db
# *.sqlite
# *.sqlite3

# 上传文件
uploads/*
!uploads/.gitkeep

# 实验结果
results/experiment_*/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 进程 ID 文件
.backend.pid
.frontend.pid
