"""
实验相关数据模型
"""
from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum

from app.core.database import Base


class ExperimentStatus(str, Enum):
    """实验状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ModelType(str, Enum):
    """模型类型枚举"""
    ORCDF = "orcdf"
    NCDM = "ncdm"
    KANCD = "kancd"
    KSCD = "kscd"
    CDMFKC = "cdmfkc"
    MIRT = "mirt"
    IRT = "irt"


class DatasetType(str, Enum):
    """数据集类型枚举"""
    ASSIST0910 = "Assist0910"
    ASSIST17 = "Assist17"
    JUNYI = "Junyi"
    NEURIPS2020 = "NeurIPS2020"


class Experiment(Base):
    """实验记录模型"""
    __tablename__ = "experiments"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text)
    
    # 实验配置
    dataset_id = Column(Integer, ForeignKey("datasets.id"), nullable=False)
    dataset_type = Column(String(50), nullable=False)
    model_type = Column(String(50), nullable=False)
    config = Column(JSON)  # 存储训练配置参数
    
    # 实验状态
    status = Column(String(20), default=ExperimentStatus.PENDING)
    progress = Column(Float, default=0.0)
    
    # 实验结果
    results = Column(JSON)  # 存储实验结果
    metrics = Column(JSON)  # 存储评估指标
    
    # 文件路径
    model_path = Column(String(500))
    log_path = Column(String(500))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    
    # 其他信息
    error_message = Column(Text)
    task_id = Column(String(255))  # Celery任务ID

    # 关联关系
    dataset = relationship("Dataset", back_populates="experiments")

    def __repr__(self):
        return f"<Experiment(id={self.id}, name='{self.name}', status='{self.status}')>"


class Dataset(Base):
    """数据集信息模型"""
    __tablename__ = "datasets"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False, index=True)
    display_name = Column(String(200), nullable=False)
    description = Column(Text)
    
    # 数据集统计信息
    student_num = Column(Integer)
    exercise_num = Column(Integer)
    knowledge_num = Column(Integer)
    response_num = Column(Integer)
    
    # 文件路径
    data_path = Column(String(500))
    config_path = Column(String(500))
    
    # 数据集状态
    is_active = Column(Boolean, default=True)
    is_demo = Column(Boolean, default=False)  # 是否用于实际演示
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    experiments = relationship("Experiment", back_populates="dataset")

    def __repr__(self):
        return f"<Dataset(id={self.id}, name='{self.name}')>"


class Student(Base):
    """学生模型"""
    __tablename__ = "students"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(100), unique=True, index=True)  # 学生ID
    name = Column(String(255))
    grade = Column(String(50))
    class_name = Column(String(100))

    # 认知状态
    knowledge_state = Column(JSON)  # 知识点掌握状态
    ability_level = Column(Float)   # 能力水平
    learning_style = Column(String(50))  # 学习风格

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class KnowledgeComponent(Base):
    """知识点模型"""
    __tablename__ = "knowledge_components"

    id = Column(Integer, primary_key=True, index=True)
    kc_id = Column(String(100), unique=True, index=True)  # 知识点ID
    name = Column(String(255), nullable=False)
    description = Column(Text)
    subject = Column(String(100))  # 学科
    difficulty = Column(Float)     # 难度系数

    # 知识点关系
    prerequisites = Column(JSON)   # 前置知识点
    related_kcs = Column(JSON)     # 相关知识点

    created_at = Column(DateTime(timezone=True), server_default=func.now())


class Question(Base):
    """题目模型"""
    __tablename__ = "questions"

    id = Column(Integer, primary_key=True, index=True)
    question_id = Column(String(100), unique=True, index=True)
    content = Column(Text, nullable=False)
    question_type = Column(String(50))  # 题目类型
    difficulty = Column(Float)

    # Q-Matrix相关
    knowledge_components = Column(JSON)  # 涉及的知识点
    q_matrix = Column(JSON)             # Q矩阵行

    # 统计信息
    total_attempts = Column(Integer, default=0)
    correct_attempts = Column(Integer, default=0)

    created_at = Column(DateTime(timezone=True), server_default=func.now())


class DiagnosisType(str, Enum):
    """诊断类型枚举"""
    BASIC = "basic"
    LLM = "llm"


class DiagnosisRecord(Base):
    """诊断记录模型"""
    __tablename__ = "diagnosis_records"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(String(100), nullable=False, index=True)
    experiment_id = Column(Integer, ForeignKey("experiments.id"), nullable=False)

    # 诊断类型和结果
    diagnosis_type = Column(String(20), default=DiagnosisType.BASIC)  # basic 或 llm
    overall_ability = Column(Float, nullable=False)
    confidence_level = Column(Float)

    # 详细诊断数据
    knowledge_diagnosis = Column(JSON)  # 知识点诊断结果
    diagnosis_data = Column(JSON)       # 完整诊断数据
    llm_report = Column(JSON)           # LLM生成的报告（如果有）

    # 时间戳
    diagnosis_time = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    experiment = relationship("Experiment", backref="diagnosis_records")
