#!/usr/bin/env python3
"""
简化的认知诊断测试
"""
import sys
import os
import numpy as np

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

def test_diagnosis_algorithms():
    """测试诊断算法"""
    print("=== 测试诊断算法 ===")
    
    try:
        # 模拟学生响应数据
        student_responses = np.array([
            [0, 0, 1],  # 学生0，题目0，答对
            [0, 1, 0],  # 学生0，题目1，答错
            [0, 2, 1],  # 学生0，题目2，答对
            [0, 3, 1],  # 学生0，题目3，答对
            [0, 4, 0],  # 学生0，题目4，答错
        ])
        
        # 模拟Q矩阵（题目-知识点关联）
        q_matrix = np.array([
            [1, 0, 0],  # 题目0关联知识点0
            [0, 1, 0],  # 题目1关联知识点1
            [1, 1, 0],  # 题目2关联知识点0和1
            [0, 0, 1],  # 题目3关联知识点2
            [1, 0, 1],  # 题目4关联知识点0和2
        ])
        
        print(f"✓ 测试数据准备完成")
        print(f"  学生响应: {len(student_responses)} 条")
        print(f"  Q矩阵形状: {q_matrix.shape}")
        
        # 简单的认知诊断算法
        knowledge_mastery = calculate_knowledge_mastery(student_responses, q_matrix)
        
        print(f"✓ 知识点掌握程度计算完成")
        for i, mastery in enumerate(knowledge_mastery):
            print(f"  知识点{i}: {mastery:.3f}")
        
        # 计算总体能力
        overall_ability = np.mean(knowledge_mastery)
        print(f"✓ 总体能力: {overall_ability:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 诊断算法测试失败: {e}")
        return False

def calculate_knowledge_mastery(responses, q_matrix):
    """计算知识点掌握程度"""
    num_knowledge = q_matrix.shape[1]
    knowledge_mastery = []
    
    for k in range(num_knowledge):
        # 找到涉及该知识点的题目
        related_items = np.where(q_matrix[:, k] == 1)[0]
        
        if len(related_items) == 0:
            knowledge_mastery.append(0.5)  # 默认值
            continue
        
        # 计算该知识点相关题目的正确率
        correct_count = 0
        total_count = 0
        
        for item_id in related_items:
            item_responses = responses[responses[:, 1] == item_id]
            if len(item_responses) > 0:
                correct_count += np.sum(item_responses[:, 2])
                total_count += len(item_responses)
        
        if total_count > 0:
            mastery = correct_count / total_count
        else:
            mastery = 0.5
        
        knowledge_mastery.append(mastery)
    
    return np.array(knowledge_mastery)

def test_model_prediction_simulation():
    """测试模型预测模拟"""
    print("\n=== 测试模型预测模拟 ===")
    
    try:
        # 模拟不同类型的模型预测结果
        
        # 1. 单值预测（总体能力）
        single_prediction = 0.75
        knowledge_vector = expand_to_knowledge_vector(single_prediction, 5)
        print(f"✓ 单值预测扩展: {single_prediction} -> {knowledge_vector}")
        
        # 2. 知识点向量预测
        vector_prediction = np.array([0.8, 0.6, 0.9, 0.7, 0.5])
        processed_vector = process_knowledge_vector(vector_prediction)
        print(f"✓ 向量预测处理: {vector_prediction} -> {processed_vector}")
        
        # 3. 矩阵预测（多学生或多维度）
        matrix_prediction = np.random.rand(3, 5)
        aggregated_vector = aggregate_matrix_prediction(matrix_prediction)
        print(f"✓ 矩阵预测聚合: {matrix_prediction.shape} -> {aggregated_vector}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型预测模拟测试失败: {e}")
        return False

def expand_to_knowledge_vector(single_value, num_knowledge):
    """将单个值扩展为知识点向量"""
    # 在单个值基础上添加随机扰动
    vector = []
    for i in range(num_knowledge):
        noise = np.random.normal(0, 0.1)
        value = max(0.05, min(0.95, single_value + noise))
        vector.append(value)
    return np.array(vector)

def process_knowledge_vector(vector):
    """处理知识点向量"""
    # 确保值在合理范围内
    processed = np.clip(vector, 0.05, 0.95)
    return processed

def aggregate_matrix_prediction(matrix):
    """聚合矩阵预测结果"""
    # 计算每列的平均值
    aggregated = np.mean(matrix, axis=0)
    return process_knowledge_vector(aggregated)

def test_diagnosis_confidence():
    """测试诊断置信度计算"""
    print("\n=== 测试诊断置信度计算 ===")
    
    try:
        # 模拟不同情况的置信度计算
        
        # 1. 基于响应数量的置信度
        response_counts = [5, 10, 20, 50]
        for count in response_counts:
            confidence = calculate_confidence_by_responses(count)
            print(f"  响应数量 {count} -> 置信度: {confidence:.3f}")
        
        # 2. 基于预测一致性的置信度
        predictions = [
            [0.8, 0.8, 0.8],  # 高一致性
            [0.5, 0.7, 0.9],  # 中等一致性
            [0.2, 0.5, 0.9],  # 低一致性
        ]
        
        for i, pred in enumerate(predictions):
            consistency = calculate_prediction_consistency(pred)
            print(f"  预测 {pred} -> 一致性: {consistency:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 诊断置信度测试失败: {e}")
        return False

def calculate_confidence_by_responses(response_count):
    """基于响应数量计算置信度"""
    # 响应越多，置信度越高，但有上限
    base_confidence = 0.5
    increment = min(0.4, response_count * 0.02)
    return base_confidence + increment

def calculate_prediction_consistency(predictions):
    """计算预测一致性"""
    if len(predictions) <= 1:
        return 1.0
    
    # 计算标准差，标准差越小一致性越高
    std = np.std(predictions)
    consistency = max(0.1, 1.0 - std)
    return consistency

def test_mastery_level_classification():
    """测试掌握程度分级"""
    print("\n=== 测试掌握程度分级 ===")
    
    try:
        test_probabilities = [0.2, 0.4, 0.6, 0.8, 0.95]
        
        for prob in test_probabilities:
            level = classify_mastery_level(prob)
            print(f"  掌握概率 {prob} -> 等级: {level}")
        
        return True
        
    except Exception as e:
        print(f"✗ 掌握程度分级测试失败: {e}")
        return False

def classify_mastery_level(probability):
    """分类掌握程度等级"""
    if probability >= 0.8:
        return "熟练掌握"
    elif probability >= 0.6:
        return "基本掌握"
    elif probability >= 0.4:
        return "部分掌握"
    else:
        return "未掌握"

def test_diagnosis_report_generation():
    """测试诊断报告生成"""
    print("\n=== 测试诊断报告生成 ===")
    
    try:
        # 模拟诊断结果
        diagnosis_result = {
            'student_id': 'student_001',
            'overall_ability': 0.72,
            'knowledge_mastery': [0.8, 0.6, 0.9, 0.5, 0.7],
            'confidence': 0.85,
            'response_count': 25
        }
        
        # 生成诊断报告
        report = generate_diagnosis_report(diagnosis_result)
        
        print(f"✓ 诊断报告生成完成")
        print(f"  报告长度: {len(report)} 字符")
        print(f"  报告预览: {report[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ 诊断报告生成测试失败: {e}")
        return False

def generate_diagnosis_report(diagnosis_result):
    """生成诊断报告"""
    student_id = diagnosis_result['student_id']
    overall_ability = diagnosis_result['overall_ability']
    knowledge_mastery = diagnosis_result['knowledge_mastery']
    confidence = diagnosis_result['confidence']
    
    report = f"""
学生认知诊断报告

学生ID: {student_id}
诊断时间: {np.datetime64('now')}

总体能力评估: {overall_ability:.2f} (置信度: {confidence:.2f})

知识点掌握情况:
"""
    
    knowledge_names = ['代数运算', '几何推理', '函数理解', '概率统计', '逻辑推理']
    
    for i, mastery in enumerate(knowledge_mastery):
        if i < len(knowledge_names):
            name = knowledge_names[i]
        else:
            name = f'知识点{i+1}'
        
        level = classify_mastery_level(mastery)
        report += f"- {name}: {mastery:.2f} ({level})\n"
    
    # 添加建议
    weak_areas = [i for i, m in enumerate(knowledge_mastery) if m < 0.6]
    if weak_areas:
        report += "\n学习建议:\n"
        for area in weak_areas:
            if area < len(knowledge_names):
                name = knowledge_names[area]
            else:
                name = f'知识点{area+1}'
            report += f"- 需要加强 {name} 的学习和练习\n"
    
    return report

if __name__ == "__main__":
    print("开始测试简化认知诊断系统...")
    
    success_count = 0
    total_tests = 5
    
    # 运行所有测试
    if test_diagnosis_algorithms():
        success_count += 1
    
    if test_model_prediction_simulation():
        success_count += 1
    
    if test_diagnosis_confidence():
        success_count += 1
    
    if test_mastery_level_classification():
        success_count += 1
    
    if test_diagnosis_report_generation():
        success_count += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ 认知诊断核心算法工作正常！")
        print("\n核心功能:")
        print("✓ 知识点掌握程度计算")
        print("✓ 模型预测结果处理")
        print("✓ 诊断置信度评估")
        print("✓ 掌握程度分级")
        print("✓ 诊断报告生成")
    else:
        print("✗ 部分测试失败，需要检查")
    
    print("测试完成!")
