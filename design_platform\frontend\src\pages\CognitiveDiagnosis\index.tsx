import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Space,
  message,
  Divider,
  Typography,
  Alert,
  Spin,
  Row,
  Col,
  Tag,
  Tabs,
  Table,
  Modal,
  Popconfirm,
  Empty,
  Tooltip
} from 'antd';
import {
  ExperimentOutlined,
  UserOutlined,
  RobotOutlined,
  BulbOutlined,
  HistoryOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  FileTextOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { experimentService, cognitiveService } from '../../services/api';
import DiagnosisReport from '../../components/DiagnosisReport';
import { generateDiagnosisPDF } from '../../utils/pdfGenerator';

const { Title, Text } = Typography;
const { Option } = Select;

interface Experiment {
  id: number;
  name: string;
  status: string;
  model_type: string;
  dataset_name: string;
}

const CognitiveDiagnosis: React.FC = () => {
  const [form] = Form.useForm();
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const [loading, setLoading] = useState(false);
  const [diagnosisResult, setDiagnosisResult] = useState<any>(null);
  const [experimentsLoading, setExperimentsLoading] = useState(true);

  // 报告管理相关状态
  const [activeTab, setActiveTab] = useState('new-diagnosis');
  const [reports, setReports] = useState<any[]>([]);
  const [reportsLoading, setReportsLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [reportModalVisible, setReportModalVisible] = useState(false);

  useEffect(() => {
    loadExperiments();
    if (activeTab === 'report-history') {
      loadReports();
    }
  }, [activeTab]);

  const loadExperiments = async () => {
    try {
      setExperimentsLoading(true);
      const response = await experimentService.getExperiments();
      // 只显示已完成的实验
      const completedExperiments = response.data.filter(
        (exp: Experiment) => exp.status === 'completed'
      );
      setExperiments(completedExperiments);
    } catch (error) {
      console.error('加载实验列表失败:', error);
      message.error('加载实验列表失败');
    } finally {
      setExperimentsLoading(false);
    }
  };

  const loadReports = async () => {
    try {
      setReportsLoading(true);
      const response = await cognitiveService.getDiagnosisReports({
        limit: 50,
        offset: 0
      });
      if (response.data.success) {
        setReports(response.data.reports);
      }
    } catch (error) {
      console.error('加载报告列表失败:', error);
      message.error('加载报告列表失败');
    } finally {
      setReportsLoading(false);
    }
  };

  const handleViewReport = async (reportId: number) => {
    try {
      const response = await cognitiveService.getDiagnosisReport(reportId);
      if (response.data.success) {
        setSelectedReport(response.data.report);
        setReportModalVisible(true);
      }
    } catch (error) {
      console.error('加载报告详情失败:', error);
      message.error('加载报告详情失败');
    }
  };

  const handleDeleteReport = async (reportId: number) => {
    try {
      await cognitiveService.deleteDiagnosisReport(reportId);
      message.success('报告删除成功');
      loadReports(); // 重新加载列表
    } catch (error) {
      console.error('删除报告失败:', error);
      message.error('删除报告失败');
    }
  };

  const handleDownloadPDF = async (report: any) => {
    try {
      message.loading({ content: '正在生成PDF...', key: 'pdf-generation' });

      // 尝试使用高级PDF生成
      await generateDiagnosisPDF(report);

      message.success({ content: 'PDF下载成功！', key: 'pdf-generation' });
    } catch (error) {
      console.error('PDF生成失败:', error);
      message.destroy('pdf-generation');

      // 显示错误信息
      message.error('PDF生成失败，请稍后重试');
    }
  };

  const handleDiagnose = async (values: any) => {
    try {
      setLoading(true);
      setDiagnosisResult(null);

      console.log('🎯 开始诊断:', values);

      // 使用带LLM报告的诊断API
      const response = await cognitiveService.diagnoseStudentWithReport({
        student_id: values.student_id,
        experiment_id: values.experiment_id
      });

      console.log('📤 收到响应:', response.data);

      if (response.data.success) {
        setDiagnosisResult(response.data.diagnosis);

        // 检查是否有LLM报告
        const llmReport = response.data.diagnosis?.llm_report;
        if (llmReport && response.data.diagnosis?.report_generated) {
          message.success('AI诊断报告生成完成！');
        } else {
          message.success('基础诊断完成！');
          if (response.data.diagnosis?.report_error) {
            console.warn('LLM报告生成失败:', response.data.diagnosis.report_error);
          }
        }
      } else {
        message.error('诊断失败');
      }
    } catch (error) {
      console.error('❌ 诊断失败:', error);
      message.error('诊断过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshReport = async () => {
    if (!diagnosisResult) return;

    const formValues = form.getFieldsValue();
    await handleDiagnose(formValues);
  };

  const predefinedStudents = [
    'student_001',
    'student_002', 
    'student_003',
    'student_004',
    'student_005'
  ];

  // 报告表格列定义
  const reportColumns = [
    {
      title: '报告ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '学生ID',
      dataIndex: 'student_id',
      key: 'student_id',
      width: 120,
    },
    {
      title: '实验ID',
      dataIndex: 'experiment_id',
      key: 'experiment_id',
      width: 100,
    },
    {
      title: '诊断类型',
      dataIndex: 'diagnosis_type',
      key: 'diagnosis_type',
      width: 100,
      render: (type: string) => (
        <Tag color={type === 'LLM' ? 'blue' : 'default'}>
          {type === 'LLM' ? 'AI智能' : '基础'}
        </Tag>
      ),
    },
    {
      title: '整体能力',
      dataIndex: 'overall_ability',
      key: 'overall_ability',
      width: 100,
      render: (ability: number) => (
        <span style={{ color: ability > 0.7 ? '#52c41a' : ability > 0.5 ? '#faad14' : '#ff4d4f' }}>
          {(ability * 100).toFixed(1)}%
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: any, record: any) => (
        <Space>
          <Tooltip title="查看报告">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewReport(record.id)}
            />
          </Tooltip>
          <Tooltip title="下载PDF">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownloadPDF(record)}
            />
          </Tooltip>
          <Tooltip title="删除报告">
            <Popconfirm
              title="确定要删除这个报告吗？"
              onConfirm={() => handleDeleteReport(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{
      padding: '24px',
      maxWidth: '1400px',
      margin: '0 auto',
      background: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <style>{`
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
        .diagnosis-card {
          transition: all 0.3s ease;
        }
        .diagnosis-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0,0,0,0.15) !important;
        }
      `}</style>
      <div style={{
        marginBottom: '24px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '12px',
        padding: '32px',
        color: 'white',
        textAlign: 'center'
      }}>
        <Title level={1} style={{ color: 'white', marginBottom: '8px' }}>
          <RobotOutlined style={{ marginRight: '12px' }} />
          AI智能诊断与规划中心
        </Title>
        <Text style={{ fontSize: '16px', color: 'rgba(255,255,255,0.9)' }}>
          基于EduBrain抗过平滑特征丰富方法进行学生认知诊断，生成个性化AI分析报告，并提供智能学习路径规划
        </Text>
        <div style={{ marginTop: '16px' }}>
          <Tag color="rgba(255,255,255,0.2)" style={{ color: 'white', border: '1px solid rgba(255,255,255,0.3)' }}>
            🧠 智能诊断
          </Tag>
          <Tag color="rgba(255,255,255,0.2)" style={{ color: 'white', border: '1px solid rgba(255,255,255,0.3)', marginLeft: '8px' }}>
            📊 数据分析
          </Tag>
          <Tag color="rgba(255,255,255,0.2)" style={{ color: 'white', border: '1px solid rgba(255,255,255,0.3)', marginLeft: '8px' }}>
            🎯 学习规划
          </Tag>
          <Tag color="rgba(255,255,255,0.2)" style={{ color: 'white', border: '1px solid rgba(255,255,255,0.3)', marginLeft: '8px' }}>
            📄 报告管理
          </Tag>
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        size="large"
        items={[
          {
            key: 'new-diagnosis',
            label: (
              <span>
                <PlusOutlined />
                新建诊断
              </span>
            ),
            children: (
              <Row gutter={24}>
                <Col xs={24} lg={8}>
                  <Card
                    title={
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <ExperimentOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                        诊断配置
                      </div>
                    }
                    className="diagnosis-card"
                    style={{
                      marginBottom: '24px',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                      background: 'white'
                    }}
                  >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleDiagnose}
              initialValues={{
                student_id: 'student_001'
              }}
            >
              <Form.Item
                label="选择实验"
                name="experiment_id"
                rules={[{ required: true, message: '请选择一个已完成的实验' }]}
              >
                <Select
                  placeholder="选择已完成的实验"
                  loading={experimentsLoading}
                  showSearch
                  filterOption={(input, option) =>
                    option?.children?.toString().toLowerCase().includes(input.toLowerCase()) || false
                  }
                >
                  {experiments.map(exp => (
                    <Option key={exp.id} value={exp.id}>
                      <div>
                        <div>{exp.name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {exp.model_type} | {exp.dataset_name}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                label="学生ID"
                name="student_id"
                rules={[{ required: true, message: '请输入学生ID' }]}
              >
                <Select
                  placeholder="选择或输入学生ID"
                  showSearch
                  allowClear
                >
                  {predefinedStudents.map(studentId => (
                    <Option key={studentId} value={studentId}>
                      {studentId}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  icon={<BulbOutlined />}
                  size="large"
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    borderRadius: '8px',
                    background: loading ? undefined : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    border: 'none',
                    boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
                  }}
                >
                  {loading ? '正在进行AI智能诊断...' : '🚀 开始AI智能诊断'}
                </Button>
              </Form.Item>
            </Form>

            {experiments.length === 0 && !experimentsLoading && (
              <Alert
                message="暂无可用实验"
                description="请先完成模型训练实验，然后再进行认知诊断。"
                type="warning"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </Card>

          <Card title="功能特色" size="small">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Tag color="blue" icon={<ExperimentOutlined />}>
                  EduBrain抗过平滑特征丰富方法
                </Tag>
                <Text style={{ fontSize: '12px' }}>
                  抗过平滑特征丰富认知诊断
                </Text>
              </div>
              <div>
                <Tag color="green" icon={<RobotOutlined />}>
                  AI分析
                </Tag>
                <Text style={{ fontSize: '12px' }}>
                  大模型生成个性化报告
                </Text>
              </div>
              <div>
                <Tag color="orange" icon={<BulbOutlined />}>
                  学习建议
                </Tag>
                <Text style={{ fontSize: '12px' }}>
                  智能学习路径规划
                </Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          {loading && (
            <Card style={{ borderRadius: '8px', boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }}>
              <div style={{ textAlign: 'center', padding: '80px 40px' }}>
                <div style={{
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '50%',
                  width: '80px',
                  height: '80px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 24px',
                  animation: 'pulse 2s infinite'
                }}>
                  <RobotOutlined style={{ fontSize: '32px', color: 'white' }} />
                </div>
                <Title level={4} style={{ marginBottom: '8px' }}>
                  AI正在进行智能诊断
                </Title>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  正在分析学习数据，生成个性化诊断报告...
                </Text>
                <div style={{ marginTop: '24px' }}>
                  <Spin size="large" />
                </div>
              </div>
            </Card>
          )}

          {diagnosisResult && !loading && (
            <DiagnosisReport
              diagnosisData={diagnosisResult}
              onRefresh={handleRefreshReport}
            />
          )}

          {!diagnosisResult && !loading && (
            <Card style={{ borderRadius: '8px', boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }}>
              <div style={{ textAlign: 'center', padding: '80px 40px' }}>
                <div style={{
                  background: 'linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%)',
                  borderRadius: '50%',
                  width: '80px',
                  height: '80px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  margin: '0 auto 24px'
                }}>
                  <FileTextOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                </div>
                <Title level={4} style={{ marginBottom: '8px', color: '#666' }}>
                  准备开始AI智能诊断
                </Title>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  请在左侧配置区域选择实验和学生ID，然后点击开始诊断
                </Text>
                <div style={{ marginTop: '24px' }}>
                  <Tag color="blue">🧠 智能分析</Tag>
                  <Tag color="green">📊 可视化报告</Tag>
                  <Tag color="orange">💡 学习建议</Tag>
                </div>
              </div>
            </Card>
          )}
        </Col>
      </Row>
            )
          },
          {
            key: 'report-history',
            label: (
              <span>
                <HistoryOutlined />
                报告历史
              </span>
            ),
            children: (
              <div>
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Title level={4}>诊断报告历史</Title>
                    <Text type="secondary">查看和管理所有的认知诊断报告</Text>
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setActiveTab('new-diagnosis')}
                  >
                    新建诊断
                  </Button>
                </div>

                <Card>
                  <Table
                    columns={reportColumns}
                    dataSource={reports}
                    loading={reportsLoading}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`,
                    }}
                    locale={{
                      emptyText: (
                        <Empty
                          image={Empty.PRESENTED_IMAGE_SIMPLE}
                          description="暂无诊断报告"
                        >
                          <Button
                            type="primary"
                            onClick={() => setActiveTab('new-diagnosis')}
                          >
                            立即创建
                          </Button>
                        </Empty>
                      )
                    }}
                  />
                </Card>
              </div>
            )
          }
        ]}
      />

      {/* 报告详情模态框 */}
      <Modal
        title="诊断报告详情"
        open={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        footer={[
          <Button key="download" icon={<DownloadOutlined />} onClick={() => handleDownloadPDF(selectedReport)}>
            下载PDF
          </Button>,
          <Button key="close" onClick={() => setReportModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
        style={{ top: 20 }}
      >
        {selectedReport && (
          <DiagnosisReport
            diagnosisData={selectedReport}
            onRefresh={() => {
              setReportModalVisible(false);
              loadReports();
            }}
          />
        )}
      </Modal>
    </div>
  );
};

export default CognitiveDiagnosis;
