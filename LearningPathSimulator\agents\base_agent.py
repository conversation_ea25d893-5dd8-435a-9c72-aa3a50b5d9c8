"""
智能体基类实现

提供所有路径规划算法的统一接口。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional
import numpy as np
import time


class BaseAgent(ABC):
    """智能体基类
    
    所有路径规划算法都需要继承此类并实现相应方法。
    """
    
    def __init__(self, action_space_size: int, name: str = "BaseAgent", **kwargs):
        """
        初始化智能体
        
        Args:
            action_space_size: 动作空间大小（知识点数量）
            name: 智能体名称
            **kwargs: 其他参数
        """
        self.action_space_size = action_space_size
        self.name = name
        self.episode_count = 0
        self.total_reward = 0.0
        self.total_steps = 0
        self.decision_times = []
        self.action_history = []
        self.reward_history = []
        
        # 性能统计
        self.performance_stats = {
            'episodes': 0,
            'total_reward': 0.0,
            'total_steps': 0,
            'avg_reward_per_episode': 0.0,
            'avg_steps_per_episode': 0.0,
            'avg_decision_time': 0.0,
            'action_distribution': np.zeros(action_space_size),
            'success_rate': 0.0
        }
        
    @abstractmethod
    def get_action(self, observation: np.ndarray) -> int:
        """
        根据当前观察选择动作
        
        Args:
            observation: 当前学习状态向量 [0,1]^n
            
        Returns:
            选择的动作（知识点索引）
        """
        pass
    
    def get_action_with_timing(self, observation: np.ndarray) -> Tuple[int, float]:
        """
        获取动作并记录决策时间
        
        Args:
            observation: 当前学习状态向量
            
        Returns:
            (动作, 决策时间)
        """
        start_time = time.time()
        action = self.get_action(observation)
        decision_time = time.time() - start_time
        
        self.decision_times.append(decision_time)
        self.action_history.append(action)
        self.performance_stats['action_distribution'][action] += 1
        
        return action, decision_time
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """
        更新智能体参数（可选实现）
        
        Args:
            observation: 当前状态
            action: 执行的动作
            reward: 获得的奖励
            next_observation: 下一状态
            done: 是否结束
        """
        self.total_reward += reward
        self.total_steps += 1
        self.reward_history.append(reward)
        
        # 更新性能统计
        self.performance_stats['total_reward'] += reward
        self.performance_stats['total_steps'] += 1
    
    def reset(self):
        """重置智能体状态"""
        self.episode_count += 1
        self.performance_stats['episodes'] += 1
        
        # 计算平均值
        if self.performance_stats['episodes'] > 0:
            self.performance_stats['avg_reward_per_episode'] = (
                self.performance_stats['total_reward'] / self.performance_stats['episodes']
            )
            self.performance_stats['avg_steps_per_episode'] = (
                self.performance_stats['total_steps'] / self.performance_stats['episodes']
            )
        
        if self.decision_times:
            self.performance_stats['avg_decision_time'] = np.mean(self.decision_times)
    
    def get_info(self) -> Dict[str, Any]:
        """获取智能体信息"""
        return {
            'name': self.name,
            'episode_count': self.episode_count,
            'total_reward': self.total_reward,
            'total_steps': self.total_steps,
            'avg_reward': self.total_reward / max(1, self.episode_count),
            'avg_steps': self.total_steps / max(1, self.episode_count),
            'avg_decision_time': np.mean(self.decision_times) if self.decision_times else 0,
            'action_distribution': self.performance_stats['action_distribution'].tolist()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取详细性能统计"""
        stats = self.performance_stats.copy()
        
        # 计算动作分布百分比
        total_actions = np.sum(stats['action_distribution'])
        if total_actions > 0:
            stats['action_distribution_pct'] = (
                stats['action_distribution'] / total_actions * 100
            ).tolist()
        else:
            stats['action_distribution_pct'] = [0] * self.action_space_size
        
        # 奖励统计
        if self.reward_history:
            stats['reward_std'] = np.std(self.reward_history)
            stats['reward_min'] = np.min(self.reward_history)
            stats['reward_max'] = np.max(self.reward_history)
        
        return stats
    
    def save_model(self, filepath: str):
        """保存模型（可选实现）"""
        pass
    
    def load_model(self, filepath: str):
        """加载模型（可选实现）"""
        pass
    
    def get_action_probabilities(self, observation: np.ndarray) -> np.ndarray:
        """
        获取动作概率分布（可选实现）
        
        Args:
            observation: 当前状态
            
        Returns:
            动作概率分布
        """
        # 默认实现：均匀分布
        return np.ones(self.action_space_size) / self.action_space_size
    
    def explain_decision(self, observation: np.ndarray, action: int) -> Dict[str, Any]:
        """
        解释决策过程（可选实现）
        
        Args:
            observation: 当前状态
            action: 选择的动作
            
        Returns:
            决策解释
        """
        return {
            'agent_name': self.name,
            'chosen_action': action,
            'state': observation.tolist(),
            'explanation': f"{self.name} 选择了动作 {action}"
        }
    
    def set_exploration_rate(self, rate: float):
        """设置探索率（用于支持探索的算法）"""
        pass
    
    def get_exploration_rate(self) -> float:
        """获取当前探索率"""
        return 0.0
    
    def set_learning_rate(self, rate: float):
        """设置学习率（用于学习型算法）"""
        pass
    
    def get_learning_rate(self) -> float:
        """获取当前学习率"""
        return 0.0


class MetaAgent(BaseAgent):
    """
    元智能体
    
    可以组合多个子智能体的决策。
    """
    
    def __init__(self, agents: List[BaseAgent], combination_strategy: str = "voting"):
        """
        初始化元智能体
        
        Args:
            agents: 子智能体列表
            combination_strategy: 组合策略 ("voting", "weighted", "expert")
        """
        if not agents:
            raise ValueError("至少需要一个子智能体")
        
        action_space_size = agents[0].action_space_size
        super().__init__(action_space_size, f"MetaAgent({combination_strategy})")
        
        self.agents = agents
        self.combination_strategy = combination_strategy
        self.agent_weights = np.ones(len(agents)) / len(agents)  # 均匀权重
        
    def get_action(self, observation: np.ndarray) -> int:
        """基于组合策略选择动作"""
        if self.combination_strategy == "voting":
            return self._voting_strategy(observation)
        elif self.combination_strategy == "weighted":
            return self._weighted_strategy(observation)
        elif self.combination_strategy == "expert":
            return self._expert_strategy(observation)
        else:
            raise ValueError(f"未知的组合策略: {self.combination_strategy}")
    
    def _voting_strategy(self, observation: np.ndarray) -> int:
        """投票策略：选择得票最多的动作"""
        votes = np.zeros(self.action_space_size)
        
        for agent in self.agents:
            action = agent.get_action(observation)
            votes[action] += 1
        
        return np.argmax(votes)
    
    def _weighted_strategy(self, observation: np.ndarray) -> int:
        """加权策略：基于智能体权重的加权投票"""
        weighted_votes = np.zeros(self.action_space_size)
        
        for i, agent in enumerate(self.agents):
            action = agent.get_action(observation)
            weighted_votes[action] += self.agent_weights[i]
        
        return np.argmax(weighted_votes)
    
    def _expert_strategy(self, observation: np.ndarray) -> int:
        """专家策略：选择当前表现最好的智能体的决策"""
        # 基于历史性能选择最佳智能体
        best_agent_idx = np.argmax([agent.total_reward / max(1, agent.episode_count) 
                                   for agent in self.agents])
        return self.agents[best_agent_idx].get_action(observation)
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """更新所有子智能体"""
        super().update(observation, action, reward, next_observation, done)
        
        for agent in self.agents:
            agent.update(observation, action, reward, next_observation, done)
    
    def reset(self):
        """重置所有子智能体"""
        super().reset()
        for agent in self.agents:
            agent.reset()
    
    def update_agent_weights(self, new_weights: np.ndarray):
        """更新智能体权重"""
        if len(new_weights) != len(self.agents):
            raise ValueError("权重数量必须等于智能体数量")
        
        self.agent_weights = new_weights / np.sum(new_weights)  # 归一化
    
    def get_agent_performance(self) -> List[Dict[str, Any]]:
        """获取所有子智能体的性能"""
        return [agent.get_info() for agent in self.agents]


class AdaptiveAgent(BaseAgent):
    """
    自适应智能体基类
    
    可以根据环境反馈调整策略参数。
    """
    
    def __init__(self, action_space_size: int, name: str = "AdaptiveAgent", 
                 adaptation_rate: float = 0.1):
        super().__init__(action_space_size, name)
        self.adaptation_rate = adaptation_rate
        self.performance_window = []
        self.window_size = 10
        
    def adapt_parameters(self, recent_performance: float):
        """
        根据最近性能调整参数
        
        Args:
            recent_performance: 最近的性能指标
        """
        self.performance_window.append(recent_performance)
        
        if len(self.performance_window) > self.window_size:
            self.performance_window.pop(0)
        
        if len(self.performance_window) >= 2:
            # 计算性能趋势
            recent_avg = np.mean(self.performance_window[-3:])
            older_avg = np.mean(self.performance_window[:-3]) if len(self.performance_window) > 3 else recent_avg
            
            performance_trend = recent_avg - older_avg
            
            # 子类可以重写此方法来实现具体的参数调整
            self._adjust_parameters(performance_trend)
    
    def _adjust_parameters(self, trend: float):
        """调整参数的具体实现（子类重写）"""
        pass
    
    def update(self, observation: np.ndarray, action: int, 
               reward: float, next_observation: np.ndarray, done: bool):
        """更新并触发自适应"""
        super().update(observation, action, reward, next_observation, done)
        
        # 触发参数自适应
        if len(self.reward_history) > 0:
            recent_performance = np.mean(self.reward_history[-5:])
            self.adapt_parameters(recent_performance)
