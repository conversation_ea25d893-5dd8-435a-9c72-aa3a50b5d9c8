"""
认证API端点 - 简化版本
"""
from datetime import datetime, timedelta
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import hashlib
import secrets

from app.core.database import get_db
from app.core.config import settings
from app.schemas.auth import LoginRequest, LoginResponse, UserResponse

router = APIRouter()

# JWT Bearer token
security = HTTPBearer()

# 简化的用户数据库（演示用）
fake_users_db = {
    "admin": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "系统管理员",
        "password": "secret",  # 简化版本，实际应该加密
        "is_active": True,
        "is_superuser": True,
        "role": "admin"
    },
    "teacher": {
        "id": 2,
        "username": "teacher",
        "email": "<EMAIL>",
        "full_name": "教师用户",
        "password": "secret",
        "is_active": True,
        "is_superuser": False,
        "role": "teacher"
    },
    "demo": {
        "id": 3,
        "username": "demo",
        "email": "<EMAIL>",
        "full_name": "演示用户",
        "password": "secret",
        "is_active": True,
        "is_superuser": False,
        "role": "demo"
    },
    # 新增5个最高权限测试账号
    "test1": {
        "id": 4,
        "username": "test1",
        "email": "<EMAIL>",
        "full_name": "测试用户1",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "role": "admin"
    },
    "test2": {
        "id": 5,
        "username": "test2",
        "email": "<EMAIL>",
        "full_name": "测试用户2",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "role": "admin"
    },
    "test3": {
        "id": 6,
        "username": "test3",
        "email": "<EMAIL>",
        "full_name": "测试用户3",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "role": "admin"
    },
    "test4": {
        "id": 7,
        "username": "test4",
        "email": "<EMAIL>",
        "full_name": "测试用户4",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "role": "admin"
    },
    "test5": {
        "id": 8,
        "username": "test5",
        "email": "<EMAIL>",
        "full_name": "测试用户5",
        "password": "123456",
        "is_active": True,
        "is_superuser": True,
        "role": "admin"
    }
}

# 简化的token存储（实际应该使用Redis或数据库）
# 改为字典存储，支持多设备登录：token -> username
active_tokens = {}


def authenticate_user(username: str, password: str):
    """认证用户 - 简化版本"""
    user = fake_users_db.get(username)
    if not user:
        return False
    if password != user["password"]:  # 简化版本，直接比较明文密码
        return False
    return user


def create_access_token(username: str) -> str:
    """创建访问令牌 - 支持多设备登录"""
    # 生成一个简单的token（实际应该使用JWT）
    token = f"{username}_{secrets.token_urlsafe(32)}"
    active_tokens[token] = username  # 存储token到用户名的映射
    return token


def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户 - 支持多设备登录"""
    token = credentials.credentials

    if token not in active_tokens:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 从token映射中获取用户名
    username = active_tokens[token]
    user = fake_users_db.get(username)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user


@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录 - 简化版本"""
    user = authenticate_user(request.username, request.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token = create_access_token(user["username"])

    return LoginResponse(
        success=True,
        message="登录成功",
        token=access_token,
        token_type="bearer",
        user=UserResponse(
            id=user["id"],
            username=user["username"],
            email=user["email"],
            full_name=user["full_name"],
            is_active=user["is_active"],
            role=user["role"]
        )
    )


@router.get("/me", response_model=UserResponse)
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return UserResponse(
        id=current_user["id"],
        username=current_user["username"],
        email=current_user["email"],
        full_name=current_user["full_name"],
        is_active=current_user["is_active"],
        role=current_user["role"]
    )


@router.post("/logout")
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出"""
    token = credentials.credentials
    if token in active_tokens:
        del active_tokens[token]  # 移除token
    return {"success": True, "message": "登出成功"}


@router.get("/users")
async def get_users(current_user: dict = Depends(get_current_user)):
    """获取用户列表（需要管理员权限）"""
    if not current_user.get("is_superuser"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    users = []
    for user_data in fake_users_db.values():
        users.append(UserResponse(
            id=user_data["id"],
            username=user_data["username"],
            email=user_data["email"],
            full_name=user_data["full_name"],
            is_active=user_data["is_active"],
            role=user_data["role"]
        ))
    
    return users
