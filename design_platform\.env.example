# ORCDF设计平台环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# =============================================================================
# 基础配置
# =============================================================================
PROJECT_NAME=ORCDF设计平台
VERSION=1.0.0
API_V1_STR=/api/v1

# 服务器配置
HOST=0.0.0.0
PORT=8000

# =============================================================================
# 数据库配置
# =============================================================================
# 开发环境使用SQLite
DATABASE_URL=sqlite:///./orcdf_platform.db

# 生产环境使用PostgreSQL
# DATABASE_URL=****************************************************/orcdf_platform

# =============================================================================
# Redis配置
# =============================================================================
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# Celery配置
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥 - 生产环境请使用强密码
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# CORS配置
# =============================================================================
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001

# =============================================================================
# 文件存储配置
# =============================================================================
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600  # 100MB
DATASETS_DIR=../datasets
MODELS_DIR=models
RESULTS_DIR=results

# =============================================================================
# 训练配置
# =============================================================================
DEFAULT_BATCH_SIZE=256
DEFAULT_EPOCHS=10
DEFAULT_LEARNING_RATE=0.001

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# =============================================================================
# 前端配置
# =============================================================================
REACT_APP_API_URL=http://localhost:8000

# =============================================================================
# Docker配置
# =============================================================================
# PostgreSQL数据库配置
POSTGRES_DB=orcdf_platform
POSTGRES_USER=orcdf_user
POSTGRES_PASSWORD=orcdf_password

# =============================================================================
# 生产环境配置
# =============================================================================
# 域名配置
DOMAIN=localhost
HTTPS_ENABLED=false

# SSL证书路径
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# =============================================================================
# 监控配置
# =============================================================================
# 是否启用监控
MONITORING_ENABLED=false

# Prometheus配置
PROMETHEUS_PORT=9090

# =============================================================================
# 开发配置
# =============================================================================
# 调试模式
DEBUG=false

# 是否重新加载
RELOAD=true

# 测试数据库
TEST_DATABASE_URL=sqlite:///./test.db
