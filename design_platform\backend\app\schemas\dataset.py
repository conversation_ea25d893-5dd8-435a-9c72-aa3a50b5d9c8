"""
数据集相关的Pydantic模式
"""
from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class DatasetBase(BaseModel):
    """数据集基础模式"""
    name: str
    display_name: str
    description: Optional[str] = None
    is_active: bool = True
    is_demo: bool = False


class DatasetCreate(DatasetBase):
    """创建数据集模式"""
    pass


class DatasetUpdate(BaseModel):
    """更新数据集模式"""
    display_name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    is_demo: Optional[bool] = None


class DatasetResponse(DatasetBase):
    """数据集响应模式"""
    id: int
    student_num: Optional[int] = None
    exercise_num: Optional[int] = None
    knowledge_num: Optional[int] = None
    response_num: Optional[int] = None
    data_path: Optional[str] = None
    config_path: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class DatasetStats(BaseModel):
    """数据集统计信息"""
    student_num: int
    exercise_num: int
    knowledge_num: int
    response_num: int
    avg_responses_per_student: float
    avg_responses_per_exercise: float
    sparsity: float  # 稀疏度
    difficulty_distribution: dict  # 难度分布
    knowledge_coverage: dict  # 知识点覆盖情况
