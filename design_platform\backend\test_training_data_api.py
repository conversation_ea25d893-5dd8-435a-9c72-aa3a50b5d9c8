#!/usr/bin/env python3
"""
测试训练数据API
"""
import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

def test_data_processor():
    """测试数据处理器"""
    print("=== 测试数据处理器 ===")
    
    try:
        from app.services.data_processor import DataProcessor
        from app.core.config import settings
        
        # 创建数据处理器
        processor = DataProcessor(settings.DATASETS_DIR)
        
        # 获取可用数据集
        available_datasets = processor.get_available_datasets()
        print(f"✓ 可用数据集: {len(available_datasets)}")
        
        if not available_datasets:
            print("✗ 没有可用数据集")
            return False
        
        # 测试第一个数据集
        dataset_name = available_datasets[0]
        print(f"测试数据集: {dataset_name}")
        
        # 验证数据集
        validation = processor.validate_dataset(dataset_name)
        print(f"✓ 数据集验证: {validation['valid']}")
        
        if not validation['valid']:
            print("✗ 数据集无效")
            return False
        
        # 获取训练数据
        training_data = processor.get_dataset_for_training(dataset_name)
        
        if training_data:
            print("✓ 训练数据获取成功")
            print(f"  元数据键: {list(training_data['metadata'].keys())}")
            print(f"  训练数据键: {list(training_data['training_data'].keys())}")
            
            # 检查数据类型
            train_data = training_data['training_data']['train_data']
            print(f"  训练数据类型: {type(train_data)}")
            print(f"  训练数据长度: {len(train_data) if train_data else 0}")
            
            # 尝试JSON序列化
            try:
                json_str = json.dumps(training_data, indent=2)
                print("✓ JSON序列化成功")
                print(f"  JSON长度: {len(json_str)} 字符")
            except Exception as e:
                print(f"✗ JSON序列化失败: {e}")
                return False
            
        else:
            print("✗ 训练数据获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dataset_service():
    """测试数据集服务"""
    print("\n=== 测试数据集服务 ===")
    
    try:
        from app.services.dataset_service import DatasetService
        from app.database.database import SessionLocal
        
        # 创建数据库会话
        db = SessionLocal()
        
        try:
            # 创建服务
            service = DatasetService(db)
            
            # 获取可用数据集信息
            datasets_info = service.get_available_datasets_info()
            print(f"✓ 可用数据集信息获取成功")
            print(f"  可用数据集: {len(datasets_info.get('available_datasets', []))}")
            print(f"  训练就绪: {len(datasets_info.get('training_ready', []))}")
            
            # 测试第一个训练就绪的数据集
            training_ready = datasets_info.get('training_ready', [])
            if training_ready:
                dataset_name = training_ready[0]
                print(f"测试数据集: {dataset_name}")
                
                # 获取训练数据
                training_data = service.get_dataset_for_training(dataset_name)
                
                if training_data:
                    print("✓ 服务层训练数据获取成功")
                    
                    # 尝试JSON序列化
                    try:
                        json_str = json.dumps(training_data, indent=2)
                        print("✓ 服务层JSON序列化成功")
                    except Exception as e:
                        print(f"✗ 服务层JSON序列化失败: {e}")
                        return False
                else:
                    print("✗ 服务层训练数据获取失败")
                    return False
            else:
                print("✗ 没有训练就绪的数据集")
                return False
            
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"✗ 数据集服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试训练数据API...")
    
    success_count = 0
    total_tests = 2
    
    if test_data_processor():
        success_count += 1
    
    if test_dataset_service():
        success_count += 1
    
    print(f"\n=== 测试总结 ===")
    print(f"通过测试: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("✓ 训练数据API测试成功！")
    else:
        print("✗ 训练数据API存在问题")
    
    print("测试完成!")
