"""
学习规划服务
基于认知诊断结果生成个性化学习路径和建议
"""
import numpy as np
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import logging

from app.models.experiment import Student, KnowledgeComponent, Question
from app.services.cognitive_diagnosis_service import CognitiveDiagnosisService

logger = logging.getLogger(__name__)


class LearningPlanningService:
    """学习规划服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.diagnosis_service = CognitiveDiagnosisService(db)
    
    def generate_learning_path(self, student_id: str, diagnosis_result: Dict[str, Any]) -> Dict[str, Any]:
        """基于诊断结果生成学习路径"""
        
        knowledge_diagnosis = diagnosis_result.get("knowledge_diagnosis", {})
        overall_ability = diagnosis_result.get("overall_ability", 0.5)
        
        # 分析薄弱知识点
        weak_kcs = []
        strong_kcs = []
        
        for kc_name, diagnosis in knowledge_diagnosis.items():
            if diagnosis["mastery_probability"] < 0.6:
                weak_kcs.append({
                    "name": kc_name,
                    "mastery_prob": diagnosis["mastery_probability"],
                    "priority": 1.0 - diagnosis["mastery_probability"]
                })
            elif diagnosis["mastery_probability"] > 0.8:
                strong_kcs.append({
                    "name": kc_name,
                    "mastery_prob": diagnosis["mastery_probability"]
                })
        
        # 按优先级排序薄弱知识点
        weak_kcs.sort(key=lambda x: x["priority"], reverse=True)
        
        # 生成学习路径
        learning_path = self._create_learning_sequence(weak_kcs, strong_kcs, overall_ability)
        
        # 生成学习建议
        recommendations = self._generate_recommendations(weak_kcs, strong_kcs, overall_ability)
        
        # 估算学习时间
        estimated_time = self._estimate_learning_time(weak_kcs, overall_ability)
        
        return {
            "student_id": student_id,
            "generated_at": datetime.now().isoformat(),
            "learning_path": learning_path,
            "recommendations": recommendations,
            "estimated_time": estimated_time,
            "weak_knowledge_components": weak_kcs,
            "strong_knowledge_components": strong_kcs,
            "overall_ability_level": self._get_ability_level(overall_ability)
        }
    
    def _create_learning_sequence(self, weak_kcs: List[Dict], strong_kcs: List[Dict], ability: float) -> List[Dict[str, Any]]:
        """创建学习序列"""
        sequence = []
        
        # 根据能力水平调整学习策略
        if ability < 0.4:  # 低能力学生
            strategy = "基础强化"
            difficulty_preference = "easy"
        elif ability < 0.7:  # 中等能力学生
            strategy = "循序渐进"
            difficulty_preference = "medium"
        else:  # 高能力学生
            strategy = "挑战提升"
            difficulty_preference = "hard"
        
        # 为每个薄弱知识点创建学习阶段
        for i, kc in enumerate(weak_kcs[:5]):  # 最多处理5个薄弱知识点
            stage = {
                "stage": i + 1,
                "knowledge_component": kc["name"],
                "current_mastery": kc["mastery_prob"],
                "target_mastery": min(0.85, kc["mastery_prob"] + 0.3),
                "learning_strategy": strategy,
                "difficulty_level": difficulty_preference,
                "activities": self._generate_learning_activities(kc, difficulty_preference),
                "estimated_duration": self._estimate_stage_duration(kc, ability),
                "prerequisites": self._get_prerequisites(kc["name"]),
                "success_criteria": self._define_success_criteria(kc)
            }
            sequence.append(stage)
        
        return sequence
    
    def _generate_learning_activities(self, kc: Dict, difficulty: str) -> List[Dict[str, Any]]:
        """生成学习活动"""
        activities = []
        
        # 基础学习活动
        activities.append({
            "type": "concept_learning",
            "title": f"{kc['name']}概念学习",
            "description": f"学习{kc['name']}的基本概念和原理",
            "duration_minutes": 30,
            "resources": [
                {"type": "video", "title": f"{kc['name']}基础视频", "duration": 15},
                {"type": "text", "title": f"{kc['name']}概念解析", "pages": 5}
            ]
        })
        
        # 练习活动
        practice_count = {"easy": 10, "medium": 15, "hard": 20}.get(difficulty, 15)
        activities.append({
            "type": "practice",
            "title": f"{kc['name']}练习题",
            "description": f"完成{practice_count}道{kc['name']}相关练习题",
            "duration_minutes": practice_count * 2,
            "question_count": practice_count,
            "difficulty_level": difficulty
        })
        
        # 应用活动
        activities.append({
            "type": "application",
            "title": f"{kc['name']}应用练习",
            "description": f"在实际问题中应用{kc['name']}知识",
            "duration_minutes": 25,
            "scenarios": self._get_application_scenarios(kc["name"])
        })
        
        return activities
    
    def _generate_recommendations(self, weak_kcs: List[Dict], strong_kcs: List[Dict], ability: float) -> Dict[str, Any]:
        """生成学习建议"""
        
        recommendations = {
            "priority_focus": [],
            "learning_strategies": [],
            "study_schedule": {},
            "motivation_tips": []
        }
        
        # 优先关注建议
        if weak_kcs:
            recommendations["priority_focus"] = [
                f"重点关注{kc['name']}，当前掌握度仅为{kc['mastery_prob']:.1%}"
                for kc in weak_kcs[:3]
            ]
        
        # 学习策略建议
        if ability < 0.4:
            recommendations["learning_strategies"] = [
                "建议从基础概念开始，循序渐进",
                "多做简单练习题，建立信心",
                "寻求老师或同学帮助",
                "每天学习时间不宜过长，保持专注"
            ]
        elif ability < 0.7:
            recommendations["learning_strategies"] = [
                "保持当前学习节奏",
                "适当增加练习难度",
                "注重知识点之间的联系",
                "定期复习已掌握的内容"
            ]
        else:
            recommendations["learning_strategies"] = [
                "可以尝试更有挑战性的题目",
                "主动探索知识的深层应用",
                "帮助其他同学，巩固自己的理解",
                "关注跨学科的知识整合"
            ]
        
        # 学习时间安排
        daily_study_time = max(30, min(120, len(weak_kcs) * 20))  # 30-120分钟
        recommendations["study_schedule"] = {
            "daily_study_time_minutes": daily_study_time,
            "weekly_sessions": min(5, len(weak_kcs) + 2),
            "break_frequency": "每25分钟休息5分钟",
            "best_study_times": ["上午9-11点", "下午3-5点", "晚上7-9点"]
        }
        
        # 激励建议
        if len(strong_kcs) > 0:
            recommendations["motivation_tips"].append(
                f"你在{', '.join([kc['name'] for kc in strong_kcs[:2]])}方面表现优秀，继续保持！"
            )
        
        recommendations["motivation_tips"].extend([
            "设定小目标，每完成一个阶段给自己小奖励",
            "记录学习进度，看到自己的成长",
            "与同学组成学习小组，互相鼓励"
        ])
        
        return recommendations
    
    def _estimate_learning_time(self, weak_kcs: List[Dict], ability: float) -> Dict[str, Any]:
        """估算学习时间"""
        
        # 基础时间估算（小时）
        base_hours_per_kc = {"low": 8, "medium": 6, "high": 4}
        ability_level = "low" if ability < 0.4 else "medium" if ability < 0.7 else "high"
        
        total_hours = 0
        kc_estimates = []
        
        for kc in weak_kcs:
            # 根据当前掌握度调整时间
            mastery_gap = 0.8 - kc["mastery_prob"]  # 目标掌握度0.8
            hours_needed = base_hours_per_kc[ability_level] * mastery_gap * 2
            hours_needed = max(2, min(15, hours_needed))  # 限制在2-15小时之间
            
            total_hours += hours_needed
            kc_estimates.append({
                "knowledge_component": kc["name"],
                "estimated_hours": round(hours_needed, 1),
                "current_mastery": kc["mastery_prob"],
                "target_mastery": 0.8
            })
        
        # 计算完成时间
        daily_study_hours = 1.5 if ability < 0.4 else 2.0 if ability < 0.7 else 2.5
        days_needed = max(7, total_hours / daily_study_hours)
        
        return {
            "total_hours": round(total_hours, 1),
            "daily_study_hours": daily_study_hours,
            "estimated_days": round(days_needed),
            "estimated_weeks": round(days_needed / 7, 1),
            "completion_date": (datetime.now() + timedelta(days=days_needed)).strftime("%Y-%m-%d"),
            "knowledge_component_breakdown": kc_estimates
        }
    
    def _estimate_stage_duration(self, kc: Dict, ability: float) -> int:
        """估算单个阶段的持续时间（天）"""
        base_days = 7 if ability < 0.4 else 5 if ability < 0.7 else 3
        mastery_factor = (0.8 - kc["mastery_prob"]) * 2
        return max(2, int(base_days * mastery_factor))
    
    def _get_prerequisites(self, kc_name: str) -> List[str]:
        """获取知识点前置要求"""
        prerequisites_map = {
            "代数运算": ["基础数学运算"],
            "几何推理": ["空间想象能力", "基础几何概念"],
            "函数理解": ["代数运算", "坐标系概念"],
            "概率统计": ["基础数学运算", "逻辑推理"],
            "逻辑推理": ["基础思维能力"]
        }
        return prerequisites_map.get(kc_name, [])
    
    def _define_success_criteria(self, kc: Dict) -> List[str]:
        """定义成功标准"""
        return [
            f"在{kc['name']}相关测试中达到80%以上正确率",
            f"能够独立解决{kc['name']}的典型问题",
            f"理解{kc['name']}的核心概念和应用场景"
        ]
    
    def _get_application_scenarios(self, kc_name: str) -> List[str]:
        """获取应用场景"""
        scenarios_map = {
            "代数运算": ["解方程组", "化简表达式", "计算实际问题"],
            "几何推理": ["证明几何定理", "计算面积体积", "空间关系分析"],
            "函数理解": ["建立数学模型", "分析函数性质", "解决优化问题"],
            "概率统计": ["数据分析", "风险评估", "预测建模"],
            "逻辑推理": ["逻辑证明", "推理分析", "问题求解"]
        }
        return scenarios_map.get(kc_name, ["综合应用练习"])
    
    def _get_ability_level(self, ability: float) -> str:
        """获取能力水平描述"""
        if ability < 0.3:
            return "初学者"
        elif ability < 0.5:
            return "基础水平"
        elif ability < 0.7:
            return "中等水平"
        elif ability < 0.85:
            return "良好水平"
        else:
            return "优秀水平"
    
    def track_learning_progress(self, student_id: str, completed_activities: List[Dict]) -> Dict[str, Any]:
        """跟踪学习进度"""
        
        total_activities = len(completed_activities)
        completed_count = sum(1 for activity in completed_activities if activity.get("completed", False))
        
        progress_percentage = (completed_count / total_activities * 100) if total_activities > 0 else 0
        
        # 分析学习效果
        performance_scores = [activity.get("score", 0) for activity in completed_activities if activity.get("completed")]
        avg_performance = np.mean(performance_scores) if performance_scores else 0
        
        # 预测完成时间
        if completed_count > 0:
            avg_time_per_activity = np.mean([activity.get("time_spent", 30) for activity in completed_activities if activity.get("completed")])
            remaining_activities = total_activities - completed_count
            estimated_remaining_time = remaining_activities * avg_time_per_activity
        else:
            estimated_remaining_time = total_activities * 30  # 默认30分钟每个活动
        
        return {
            "student_id": student_id,
            "progress_percentage": round(progress_percentage, 1),
            "completed_activities": completed_count,
            "total_activities": total_activities,
            "average_performance": round(avg_performance, 2),
            "estimated_remaining_minutes": round(estimated_remaining_time),
            "learning_velocity": round(completed_count / max(1, len([a for a in completed_activities if a.get("date")])), 2),
            "recommendations": self._generate_progress_recommendations(progress_percentage, avg_performance)
        }
    
    def _generate_progress_recommendations(self, progress: float, performance: float) -> List[str]:
        """基于进度生成建议"""
        recommendations = []
        
        if progress < 30:
            recommendations.append("刚刚开始，保持学习节奏很重要")
        elif progress < 70:
            recommendations.append("进度良好，继续保持当前的学习状态")
        else:
            recommendations.append("即将完成，最后冲刺阶段要保持专注")
        
        if performance < 60:
            recommendations.append("建议复习基础概念，多做练习题")
        elif performance < 80:
            recommendations.append("表现不错，可以尝试一些有挑战性的题目")
        else:
            recommendations.append("表现优秀，可以考虑帮助其他同学或探索更深层次的内容")
        
        return recommendations
