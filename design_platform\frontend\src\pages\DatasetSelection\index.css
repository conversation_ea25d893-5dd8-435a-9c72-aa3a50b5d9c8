.dataset-selection-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dataset-selection-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  gap: 16px;
}

.dataset-selection-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dataset-selection-header h2 {
  color: #1890ff;
  margin-bottom: 16px;
}

.dataset-card {
  border-radius: 16px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  height: 100%;
}

.dataset-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.dataset-card.selected {
  border-color: #1890ff;
  box-shadow: 0 8px 30px rgba(24, 144, 255, 0.2);
}

.dataset-card.main-dataset {
  background: linear-gradient(135deg, #fff9e6 0%, #fff2cc 100%);
  border-color: #faad14;
}

.dataset-card.main-dataset.selected {
  border-color: #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.dataset-card-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.dataset-icon {
  font-size: 32px;
  color: #1890ff;
  padding: 12px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 12px;
  flex-shrink: 0;
}

.dataset-title-area {
  flex: 1;
}

.dataset-title {
  margin-bottom: 8px !important;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.main-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
}

.dataset-description {
  color: #666;
  margin-bottom: 16px;
  line-height: 1.6;
}

.dataset-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.feature-tag {
  border-radius: 12px;
  font-size: 12px;
}

.dataset-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 16px 0;
}

.dataset-stats .ant-statistic {
  text-align: center;
}

.dataset-stats .ant-statistic-title {
  font-size: 12px;
  color: #666;
}

.dataset-selection-footer {
  text-align: center;
  margin-top: 40px;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.proceed-button {
  height: 48px;
  padding: 0 40px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 24px;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  border: none;
  transition: all 0.3s;
}

.proceed-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(24, 144, 255, 0.3);
}

.proceed-button:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-selection-container {
    padding: 16px;
  }
  
  .dataset-selection-header {
    padding: 24px 16px;
  }
  
  .dataset-card-header {
    flex-direction: column;
    text-align: center;
  }
  
  .dataset-title {
    justify-content: center;
  }
  
  .dataset-stats {
    flex-direction: column;
    gap: 16px;
  }
}
