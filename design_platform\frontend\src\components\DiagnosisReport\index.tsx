import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Spin,
  Alert,
  Divider,
  Tag,
  Progress,
  Button,
  Space,
  Tooltip,
  message,
  Tabs
} from 'antd';
import {
  BulbOutlined,
  BookOutlined,
  TrophyOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  RobotOutlined,
  DownloadOutlined,
  RocketOutlined
} from '@ant-design/icons';
import { generateDiagnosisPDF } from '../../utils/pdfGenerator';
import PathRecommendation from '../LearningPath/PathRecommendation';
import PathVisualization from '../LearningPath/PathVisualization';

const { Title, Paragraph, Text } = Typography;

interface DiagnosisData {
  student_id: string;
  overall_ability: number;
  knowledge_diagnosis: Record<string, any>;
  llm_report?: {
    summary: string;
    detailed_analysis: string;
    recommendations: string;
    learning_suggestions: string;
    generated_at: string;
  };
  report_generated?: boolean;
  diagnosis_time: string;
}

interface DiagnosisReportProps {
  diagnosisData: DiagnosisData;
  onRefresh?: () => void;
  loading?: boolean;
}

const DiagnosisReport: React.FC<DiagnosisReportProps> = ({
  diagnosisData,
  onRefresh,
  loading = false
}) => {
  const [regenerating, setRegenerating] = useState(false);
  const [learningPathData, setLearningPathData] = useState<any>(null);

  const handleRegenerateReport = async () => {
    if (!onRefresh) return;

    setRegenerating(true);
    try {
      await onRefresh();
      message.success('诊断报告已重新生成');
    } catch (error) {
      message.error('重新生成报告失败');
    } finally {
      setRegenerating(false);
    }
  };

  const handleDownloadPDF = async () => {
    try {
      message.loading({ content: '正在生成PDF...', key: 'pdf-generation' });

      // 尝试使用高级PDF生成
      await generateDiagnosisPDF(diagnosisData);

      message.success({ content: 'PDF下载成功！', key: 'pdf-generation' });
    } catch (error) {
      console.error('PDF生成失败:', error);
      message.destroy('pdf-generation');

      // 显示错误信息
      message.error('PDF生成失败，请稍后重试');
    }
  };

  const getAbilityLevel = (ability: number) => {
    if (ability >= 0.8) return { level: '优秀', color: '#52c41a' };
    if (ability >= 0.6) return { level: '良好', color: '#1890ff' };
    if (ability >= 0.4) return { level: '一般', color: '#faad14' };
    return { level: '需要提升', color: '#ff4d4f' };
  };

  const getMasteryStats = () => {
    const knowledge = diagnosisData.knowledge_diagnosis || {};
    const total = Object.keys(knowledge).length;
    const mastered = Object.values(knowledge).filter((kc: any) => kc.mastery_probability > 0.7).length;
    const partial = Object.values(knowledge).filter((kc: any) => kc.mastery_probability > 0.5 && kc.mastery_probability <= 0.7).length;
    const weak = total - mastered - partial;
    
    return { total, mastered, partial, weak };
  };

  const abilityInfo = getAbilityLevel(diagnosisData.overall_ability);
  const masteryStats = getMasteryStats();
  const hasLLMReport = !!diagnosisData.llm_report; // 🔧 修复：只要有llm_report就认为有AI报告

  // 🔍 调试信息
  console.log('📋 DiagnosisReport接收到的数据:', {
    studentId: diagnosisData.student_id,
    overallAbility: diagnosisData.overall_ability,
    hasLlmReport: hasLLMReport,
    llmReportKeys: diagnosisData.llm_report ? Object.keys(diagnosisData.llm_report) : [],
    reportGenerated: diagnosisData.report_generated,
    diagnosisDataKeys: Object.keys(diagnosisData)
  });

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text>正在生成诊断报告...</Text>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      {/* 头部信息 */}
      <Card style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              学生认知诊断报告
            </Title>
            <Text type="secondary">
              学生ID: {diagnosisData.student_id} | 
              诊断时间: {new Date(diagnosisData.diagnosis_time).toLocaleString()}
            </Text>
          </div>
          <Space>
            {hasLLMReport && (
              <Tag icon={<RobotOutlined />} color="blue">
                AI智能分析
              </Tag>
            )}
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadPDF}
              type="primary"
              ghost
            >
              下载PDF
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRegenerateReport}
              loading={regenerating}
              disabled={!onRefresh}
            >
              重新生成
            </Button>
          </Space>
        </div>
      </Card>

      {/* 整体能力概览 */}
      <Card title="整体能力评估" style={{ marginBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
          <div style={{ flex: 1 }}>
            <Progress
              type="circle"
              percent={Math.round(diagnosisData.overall_ability * 100)}
              format={() => `${(diagnosisData.overall_ability * 100).toFixed(1)}%`}
              strokeColor={abilityInfo.color}
              size={120}
            />
          </div>
          <div style={{ flex: 2 }}>
            <div style={{ marginBottom: '12px' }}>
              <Text strong>能力水平: </Text>
              <Tag color={abilityInfo.color} style={{ fontSize: '14px' }}>
                {abilityInfo.level}
              </Tag>
            </div>
            <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
              <div>
                <Text type="secondary">已掌握: </Text>
                <Text strong style={{ color: '#52c41a' }}>{masteryStats.mastered}</Text>
              </div>
              <div>
                <Text type="secondary">部分掌握: </Text>
                <Text strong style={{ color: '#faad14' }}>{masteryStats.partial}</Text>
              </div>
              <div>
                <Text type="secondary">需加强: </Text>
                <Text strong style={{ color: '#ff4d4f' }}>{masteryStats.weak}</Text>
              </div>
              <div>
                <Text type="secondary">总计: </Text>
                <Text strong>{masteryStats.total}</Text>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* AI智能分析报告 */}
      {hasLLMReport ? (
        <div>
          {/* 诊断摘要 */}
          <Card 
            title={
              <span>
                <TrophyOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                诊断摘要
              </span>
            } 
            style={{ marginBottom: '16px' }}
          >
            <Paragraph style={{ fontSize: '16px', lineHeight: '1.6' }}>
              {diagnosisData.llm_report?.summary || ''}
            </Paragraph>
          </Card>

          {/* 详细分析 */}
          <Card 
            title={
              <span>
                <BookOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                详细分析
              </span>
            } 
            style={{ marginBottom: '16px' }}
          >
            <Paragraph style={{ fontSize: '15px', lineHeight: '1.7' }}>
              {diagnosisData.llm_report?.detailed_analysis || ''}
            </Paragraph>
          </Card>

          {/* 学习建议 */}
          <Card 
            title={
              <span>
                <BulbOutlined style={{ marginRight: '8px', color: '#faad14' }} />
                学习建议
              </span>
            } 
            style={{ marginBottom: '16px' }}
          >
            <Paragraph style={{ fontSize: '15px', lineHeight: '1.7' }}>
              {diagnosisData.llm_report?.recommendations || ''}
            </Paragraph>
          </Card>

          {/* 学习方法建议 */}
          <Card 
            title={
              <span>
                <ExclamationCircleOutlined style={{ marginRight: '8px', color: '#722ed1' }} />
                学习方法建议
              </span>
            } 
            style={{ marginBottom: '16px' }}
          >
            <Paragraph style={{ fontSize: '15px', lineHeight: '1.7' }}>
              {diagnosisData.llm_report?.learning_suggestions || ''}
            </Paragraph>
            <Divider />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              AI分析生成时间: {diagnosisData.llm_report?.generated_at ? new Date(diagnosisData.llm_report.generated_at).toLocaleString() : '未知'}
            </Text>
          </Card>
        </div>
      ) : (
        <Alert
          message="AI智能分析"
          description={
            diagnosisData.report_generated === false 
              ? "AI分析报告生成失败，显示基础诊断结果。您可以点击重新生成按钮重试。"
              : "正在生成AI智能分析报告，请稍候..."
          }
          type={diagnosisData.report_generated === false ? "warning" : "info"}
          showIcon
          style={{ marginBottom: '16px' }}
          action={
            diagnosisData.report_generated === false && onRefresh ? (
              <Button size="small" onClick={handleRegenerateReport} loading={regenerating}>
                重试生成
              </Button>
            ) : null
          }
        />
      )}

      {/* 详细分析和学习路径推荐 */}
      <Tabs
        defaultActiveKey="knowledge-details"
        items={[
          {
            key: 'knowledge-details',
            label: (
              <span>
                <BookOutlined />
                知识点详情
              </span>
            ),
            children: (
              <Card title="知识点掌握详情">
                <div style={{ display: 'grid', gap: '12px' }}>
                  {Object.entries(diagnosisData.knowledge_diagnosis || {}).map(([kcName, kcData]: [string, any]) => {
                    const probability = kcData.mastery_probability || 0;
                    const level = kcData.mastery_level || '未知';
                    const confidence = kcData.confidence || 0;

                    let color = '#ff4d4f';
                    if (probability > 0.7) color = '#52c41a';
                    else if (probability > 0.5) color = '#faad14';

                    return (
                      <div key={kcName} style={{
                        padding: '12px',
                        border: '1px solid #f0f0f0',
                        borderRadius: '6px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}>
                        <div style={{ flex: 1 }}>
                          <Text strong>{kcName}</Text>
                          <div style={{ marginTop: '4px' }}>
                            <Tag color={color}>{level}</Tag>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              置信度: {(confidence * 100).toFixed(1)}%
                            </Text>
                          </div>
                        </div>
                        <div style={{ width: '120px' }}>
                          <Progress
                            percent={Math.round(probability * 100)}
                            size="small"
                            strokeColor={color}
                            showInfo={false}
                          />
                          <Text style={{ fontSize: '12px', color }}>
                            {(probability * 100).toFixed(1)}%
                          </Text>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </Card>
            )
          },
          {
            key: 'learning-path',
            label: (
              <span>
                <RocketOutlined />
                学习路径推荐
              </span>
            ),
            children: (
              <div style={{ display: 'grid', gap: '16px' }}>
                <PathRecommendation
                  userId={diagnosisData.student_id}
                  diagnosisData={diagnosisData}
                  onPathSelect={(knowledgePoint) => {
                    message.info(`选择了知识点: ${knowledgePoint.knowledge_point_name}`);
                  }}
                />
                {learningPathData && learningPathData.sequence && learningPathData.sequence.length > 0 && (
                  <PathVisualization
                    learningPath={learningPathData.sequence}
                    width={800}
                    height={400}
                  />
                )}
              </div>
            )
          }
        ]}
      />
    </div>
  );
};

export default DiagnosisReport;
