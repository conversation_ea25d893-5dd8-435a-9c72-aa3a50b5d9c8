#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体仿真可视化演示
直观展示仿真过程和原理
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import random
from typing import List, Dict
import json

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False

class VisualizationEnvironment:
    """可视化学习环境"""
    
    def __init__(self, num_kps: int = 5):
        self.num_kps = num_kps
        self.student_mastery = np.random.uniform(0.1, 0.4, num_kps)
        self.learning_rate = 0.3
        self.forgetting_rate = 0.02
        self.step_count = 0
        
        # 知识点名称
        self.kp_names = [f'知识点{i+1}' for i in range(num_kps)]
        
        # 依赖关系
        self.dependency_matrix = np.zeros((num_kps, num_kps))
        for i in range(1, num_kps):
            self.dependency_matrix[i, i-1] = 0.3
        
        # 记录历史
        self.history = []
        
    def reset(self):
        self.student_mastery = np.random.uniform(0.1, 0.4, self.num_kps)
        self.step_count = 0
        self.history = [self.student_mastery.copy()]
        return self.student_mastery.copy()
    
    def step(self, action: int):
        # 学习更新
        current_mastery = self.student_mastery[action]
        learning_increment = (1 - current_mastery) * self.learning_rate
        
        # 依赖关系影响
        dependency_factor = 1.0
        for prereq in range(self.num_kps):
            if self.dependency_matrix[action, prereq] > 0:
                if self.student_mastery[prereq] < 0.4:
                    dependency_factor *= 0.7
        
        # 应用学习
        self.student_mastery[action] = min(1.0, 
            current_mastery + learning_increment * dependency_factor)
        
        # 轻微遗忘其他知识点
        for i in range(self.num_kps):
            if i != action and self.student_mastery[i] > 0.5:
                self.student_mastery[i] *= (1 - self.forgetting_rate)
        
        self.step_count += 1
        self.history.append(self.student_mastery.copy())
        
        # 计算奖励
        reward = learning_increment * dependency_factor
        
        # 检查完成
        done = self.step_count >= 15 or np.mean(self.student_mastery) > 0.7
        
        return self.student_mastery.copy(), reward, done, {}

class VisualizationAgent:
    """可视化智能体"""
    
    def __init__(self, strategy: str = "afm"):
        self.strategy = strategy
        self.name = self._get_strategy_name()
        
    def _get_strategy_name(self):
        names = {
            "afm": "AFM智能体",
            "greedy": "贪心智能体", 
            "random": "随机智能体",
            "smart": "智能贪心"
        }
        return names.get(self.strategy, "未知智能体")
    
    def get_action(self, observation: np.ndarray) -> int:
        if self.strategy == "afm":
            # 模拟AFM决策：结合预测和需求
            scores = []
            for i in range(len(observation)):
                afm_prediction = 0.3 + 0.4 * random.random()  # 模拟AFM预测
                mastery_need = 1 - observation[i]
                combined_score = afm_prediction * 0.7 + mastery_need * 0.3
                scores.append(combined_score)
            return np.argmax(scores)
            
        elif self.strategy == "greedy":
            return np.argmin(observation)
            
        elif self.strategy == "random":
            return random.randint(0, len(observation) - 1)
            
        elif self.strategy == "smart":
            # 考虑依赖关系的贪心
            scores = []
            for i in range(len(observation)):
                need_score = 1 - observation[i]
                # 简单的依赖关系检查
                prereq_score = 1.0
                if i > 0 and observation[i-1] < 0.4:
                    prereq_score = 0.5
                scores.append(need_score * prereq_score)
            return np.argmax(scores)
        
        return 0

def run_single_episode_visualization(agent: VisualizationAgent, env: VisualizationEnvironment):
    """运行单个回合并可视化"""
    
    obs = env.reset()
    done = False
    actions_taken = []
    rewards_received = []
    
    print(f"\n=== {agent.name} 学习过程可视化 ===")
    
    while not done:
        action = agent.get_action(obs)
        obs, reward, done, _ = env.step(action)
        
        actions_taken.append(action)
        rewards_received.append(reward)
        
        print(f"步骤 {env.step_count}: 学习{env.kp_names[action]}, "
              f"奖励={reward:.3f}, 平均掌握度={np.mean(obs):.3f}")
    
    return env.history, actions_taken, rewards_received

def plot_learning_progress(histories: Dict, save_path: str = None):
    """绘制学习进度对比图"""
    
    plt.figure(figsize=(15, 10))
    
    # 子图1：掌握程度变化
    plt.subplot(2, 2, 1)
    for agent_name, history in histories.items():
        avg_mastery = [np.mean(state) for state in history]
        plt.plot(avg_mastery, marker='o', label=agent_name, linewidth=2)
    
    plt.title('平均知识掌握程度变化', fontsize=14, fontweight='bold')
    plt.xlabel('学习步数')
    plt.ylabel('平均掌握程度')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2：各知识点掌握程度热力图（以AFM为例）
    plt.subplot(2, 2, 2)
    if 'AFM智能体' in histories:
        afm_history = np.array(histories['AFM智能体'])
        plt.imshow(afm_history.T, cmap='YlOrRd', aspect='auto')
        plt.colorbar(label='掌握程度')
        plt.title('AFM智能体各知识点掌握程度', fontsize=14, fontweight='bold')
        plt.xlabel('学习步数')
        plt.ylabel('知识点')
        plt.yticks(range(5), [f'KP{i+1}' for i in range(5)])
    
    # 子图3：最终成绩对比
    plt.subplot(2, 2, 3)
    final_scores = {}
    for agent_name, history in histories.items():
        final_scores[agent_name] = np.mean(history[-1])
    
    agents = list(final_scores.keys())
    scores = list(final_scores.values())
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    
    bars = plt.bar(agents, scores, color=colors[:len(agents)])
    plt.title('最终学习成绩对比', fontsize=14, fontweight='bold')
    plt.ylabel('平均掌握程度')
    plt.xticks(rotation=45)
    
    # 在柱状图上显示数值
    for bar, score in zip(bars, scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # 子图4：学习效率分析
    plt.subplot(2, 2, 4)
    for agent_name, history in histories.items():
        learning_rates = []
        for i in range(1, len(history)):
            rate = np.mean(history[i]) - np.mean(history[i-1])
            learning_rates.append(max(0, rate))  # 只考虑正向学习
        
        plt.plot(learning_rates, marker='s', label=agent_name, linewidth=2)
    
    plt.title('学习效率变化', fontsize=14, fontweight='bold')
    plt.xlabel('学习步数')
    plt.ylabel('学习增量')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()

def run_comparison_experiment():
    """运行对比实验"""
    
    print("🎯 智能体仿真对比实验")
    print("=" * 50)
    
    # 创建不同策略的智能体
    agents = [
        VisualizationAgent("afm"),
        VisualizationAgent("smart"), 
        VisualizationAgent("greedy"),
        VisualizationAgent("random")
    ]
    
    # 运行多次实验
    all_results = {}
    histories = {}
    
    for agent in agents:
        print(f"\n测试 {agent.name}...")
        
        episode_scores = []
        episode_steps = []
        
        # 运行10次取平均
        for episode in range(10):
            env = VisualizationEnvironment()
            history, actions, rewards = run_single_episode_visualization(agent, env)
            
            final_score = np.mean(history[-1])
            episode_scores.append(final_score)
            episode_steps.append(len(history) - 1)
            
            # 保存第一次的历史用于可视化
            if episode == 0:
                histories[agent.name] = history
        
        # 统计结果
        all_results[agent.name] = {
            'avg_score': np.mean(episode_scores),
            'std_score': np.std(episode_scores),
            'avg_steps': np.mean(episode_steps),
            'success_rate': len([s for s in episode_scores if s > 0.6]) / len(episode_scores)
        }
        
        print(f"  平均成绩: {all_results[agent.name]['avg_score']:.3f}")
        print(f"  成功率: {all_results[agent.name]['success_rate']:.1%}")
    
    # 结果分析
    print(f"\n📊 实验结果分析")
    print("-" * 30)
    
    # 按成绩排序
    sorted_results = sorted(all_results.items(), 
                           key=lambda x: x[1]['avg_score'], reverse=True)
    
    for i, (agent_name, result) in enumerate(sorted_results):
        print(f"{i+1}. {agent_name}:")
        print(f"   平均成绩: {result['avg_score']:.3f} (±{result['std_score']:.3f})")
        print(f"   平均步数: {result['avg_steps']:.1f}")
        print(f"   成功率: {result['success_rate']:.1%}")
    
    # 计算AFM相对于随机的改进
    if 'AFM智能体' in all_results and '随机智能体' in all_results:
        afm_score = all_results['AFM智能体']['avg_score']
        random_score = all_results['随机智能体']['avg_score']
        improvement = (afm_score - random_score) / random_score * 100
        print(f"\n🎉 AFM智能体相比随机策略改进: {improvement:+.1f}%")
    
    # 可视化结果
    plot_learning_progress(histories, 'simulation_comparison.png')
    
    # 保存详细结果
    with open('simulation_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'experiment_type': '智能体仿真对比实验',
            'agents_tested': list(all_results.keys()),
            'results': all_results,
            'conclusion': '展示了不同学习策略的效果差异'
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细结果已保存到: simulation_results.json")

def demonstrate_simulation_principles():
    """演示仿真原理"""
    
    print("🔬 智能体仿真原理演示")
    print("=" * 50)
    
    print("\n1. 🌍 学习环境建模")
    env = VisualizationEnvironment()
    print(f"   - 知识点数量: {env.num_kps}")
    print(f"   - 初始掌握程度: {[f'{x:.2f}' for x in env.student_mastery]}")
    print(f"   - 学习率: {env.learning_rate}")
    print(f"   - 遗忘率: {env.forgetting_rate}")
    
    print(f"\n   依赖关系矩阵:")
    for i, row in enumerate(env.dependency_matrix):
        print(f"   KP{i+1}: {[f'{x:.1f}' for x in row]}")
    
    print(f"\n2. 🤖 智能体策略")
    strategies = ["afm", "smart", "greedy", "random"]
    for strategy in strategies:
        agent = VisualizationAgent(strategy)
        print(f"   - {agent.name}: {strategy}策略")
    
    print(f"\n3. 📊 评估指标")
    print(f"   - 平均掌握程度: 衡量学习效果")
    print(f"   - 学习步数: 衡量学习效率") 
    print(f"   - 成功率: 达到目标的比例")
    print(f"   - 学习曲线: 掌握程度随时间的变化")
    
    print(f"\n4. 🎯 仿真价值")
    print(f"   ✓ 快速验证: 无需真实用户即可测试算法")
    print(f"   ✓ 参数优化: 找到最佳的模型参数")
    print(f"   ✓ 效果对比: 量化不同策略的优劣")
    print(f"   ✓ 风险控制: 在安全环境中发现问题")

if __name__ == "__main__":
    print("智能体仿真系统演示")
    print("=" * 60)
    
    # 演示仿真原理
    demonstrate_simulation_principles()
    
    # 运行对比实验
    run_comparison_experiment()
    
    print(f"\n🎉 演示完成！")
    print(f"📝 查看生成的图表和结果文件了解详细信息")
    print(f"📚 阅读 '智能体仿真原理详解.md' 了解理论背景")
