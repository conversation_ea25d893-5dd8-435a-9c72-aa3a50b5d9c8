#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的仿真过程演示
展示智能体如何在环境中学习，以及得分如何计算
"""

import numpy as np
from simulation_framework_explained import LearningEnvironment, GreedyAgent, SmartGreedyAgent, RandomAgent

def detailed_episode_demo():
    """详细演示一个完整的学习回合"""
    print("🎮 详细学习回合演示")
    print("=" * 80)
    
    # 创建环境
    env = LearningEnvironment(
        num_knowledge_points=5,
        learning_rate=0.3,
        forgetting_rate=0.02,
        success_threshold=0.6,
        min_success_kps=3,
        max_steps=15  # 限制步数便于演示
    )
    
    # 创建智能体
    agent = SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
    
    print(f"📚 环境设置:")
    print(f"  知识点难度: {[f'{d:.3f}' for d in env.difficulty]}")
    print(f"  成功标准: {env.min_success_kps}个知识点达到{env.success_threshold}")
    print(f"  学习参数: 学习率={env.learning_rate}, 遗忘率={env.forgetting_rate}")
    
    print(f"\n🔗 依赖关系矩阵:")
    for i, row in enumerate(env.dependency_matrix):
        deps = [f"{val:.2f}" if val > 0 else "0.00" for val in row]
        print(f"  KP{i}: {deps}")
    
    # 开始回合
    observation = env.reset()
    agent.reset()
    
    print(f"\n🎯 初始状态: {[f'{x:.3f}' for x in observation]}")
    print(f"初始成功知识点数: {env._count_success_kps()}/{env.min_success_kps}")
    
    total_reward = 0
    step = 0
    
    while True:
        step += 1
        print(f"\n{'='*60}")
        print(f"📍 第 {step} 步")
        print(f"{'='*60}")
        
        # 显示当前状态
        print(f"当前掌握程度: {[f'{x:.3f}' for x in observation]}")
        success_kps = [i for i, x in enumerate(observation) if x >= env.success_threshold]
        print(f"已成功知识点: {success_kps} ({len(success_kps)}/{env.min_success_kps})")
        
        # 智能体决策过程
        print(f"\n🤖 智能体决策过程:")
        action_scores = []
        for action in range(env.action_space_size):
            need = 1.0 - observation[action]
            
            # 计算依赖满足度
            dependencies = env.dependency_matrix[action]
            dependency_satisfaction = 1.0
            
            for i, dep_strength in enumerate(dependencies):
                if dep_strength > 0:
                    if observation[i] < 0.5:
                        dependency_satisfaction *= (1.0 - dep_strength)
            
            score = need * dependency_satisfaction
            action_scores.append(score)
            
            print(f"  动作{action}: 需求={need:.3f}, 依赖满足={dependency_satisfaction:.3f}, 得分={score:.3f}")
        
        # 选择动作
        action = np.argmax(action_scores)
        print(f"\n✅ 选择动作: {action} (学习知识点{action})")
        
        # 执行动作前的状态
        old_observation = observation.copy()
        
        # 执行动作
        observation, reward, done, info = env.step(action)
        total_reward += reward
        
        # 详细分析这一步的变化
        print(f"\n📊 学习效果分析:")
        learning_effect = info.get('learning_effect', 0)
        print(f"  学习效果: {learning_effect:.4f}")
        print(f"  知识点{action}: {old_observation[action]:.3f} → {observation[action]:.3f} (提升: {observation[action] - old_observation[action]:.4f})")
        
        # 遗忘效果
        print(f"  其他知识点遗忘效果:")
        for i in range(env.action_space_size):
            if i != action:
                change = observation[i] - old_observation[i]
                if abs(change) > 0.001:
                    print(f"    知识点{i}: {old_observation[i]:.3f} → {observation[i]:.3f} (变化: {change:.4f})")
        
        # 奖励分解
        print(f"\n💰 奖励计算详解:")
        improvement = observation[action] - old_observation[action]
        base_reward = improvement * 2.0
        print(f"  基础奖励 (提升×2): {improvement:.4f} × 2 = {base_reward:.4f}")
        
        # 成就奖励
        achievement_reward = 0
        if old_observation[action] < env.success_threshold and observation[action] >= env.success_threshold:
            achievement_reward = 1.0
            print(f"  成就奖励 (突破阈值): +{achievement_reward:.4f}")
        
        # 效率奖励
        overall_progress = np.mean(observation)
        efficiency_reward = overall_progress * 0.5
        print(f"  效率奖励 (整体进度×0.5): {overall_progress:.4f} × 0.5 = {efficiency_reward:.4f}")
        
        # 完成奖励
        completion_reward = 0
        if done and env._check_completion():
            completion_reward = 5.0
            print(f"  完成奖励: +{completion_reward:.4f}")
        
        # 惩罚
        penalty = 0
        if old_observation[action] > 0.8:
            penalty = -0.2
            print(f"  过度学习惩罚: {penalty:.4f}")
        
        print(f"  总奖励: {reward:.4f}")
        print(f"  累计奖励: {total_reward:.4f}")
        
        # 检查完成状态
        current_success = env._count_success_kps()
        print(f"\n🎯 当前进度: {current_success}/{env.min_success_kps} 个知识点达标")
        
        if done:
            if env._check_completion():
                print(f"🎉 任务完成！成功掌握 {current_success} 个知识点")
            else:
                print(f"⏰ 达到最大步数限制，任务未完成")
            break
    
    print(f"\n📈 回合总结:")
    print(f"  总步数: {step}")
    print(f"  总奖励: {total_reward:.4f}")
    print(f"  最终得分: {np.mean(observation):.4f}")
    print(f"  成功知识点: {env._count_success_kps()}/{env.min_success_kps}")
    print(f"  任务状态: {'✅ 完成' if env._check_completion() else '❌ 未完成'}")

def compare_agents_step_by_step():
    """逐步对比不同智能体的决策"""
    print(f"\n🔄 智能体决策对比演示")
    print("=" * 80)
    
    # 创建环境
    env = LearningEnvironment(num_knowledge_points=5, max_steps=10)
    
    # 创建不同智能体
    agents = {
        'Random': RandomAgent(env.action_space_size),
        'Greedy': GreedyAgent(env.action_space_size),
        'Smart_Greedy': SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
    }
    
    # 固定初始状态进行对比
    np.random.seed(42)
    initial_state = env.reset()
    
    print(f"🎯 初始状态: {[f'{x:.3f}' for x in initial_state]}")
    print(f"知识点难度: {[f'{d:.3f}' for d in env.difficulty]}")
    
    # 对比前5步的决策
    for step in range(5):
        print(f"\n--- 第 {step + 1} 步决策对比 ---")
        
        for name, agent in agents.items():
            agent.reset()
            
            # 模拟到当前步骤
            env_copy = LearningEnvironment(num_knowledge_points=5, max_steps=10)
            env_copy.current_state = initial_state.copy()
            env_copy.difficulty = env.difficulty.copy()
            env_copy.dependency_matrix = env.dependency_matrix.copy()
            
            observation = env_copy.current_state.copy()
            
            # 执行前面的步骤（简化）
            for prev_step in range(step):
                action = agent.get_action(observation)
                observation, _, _, _ = env_copy.step(action)
            
            # 当前步骤的决策
            action = agent.get_action(observation)
            
            print(f"  {name:12}: 选择动作 {action} (当前状态: {[f'{x:.2f}' for x in observation]})")

def reward_function_analysis():
    """奖励函数详细分析"""
    print(f"\n💰 奖励函数设计分析")
    print("=" * 80)
    
    env = LearningEnvironment()
    
    # 模拟不同场景的奖励
    scenarios = [
        {
            'name': '首次学习新知识点',
            'old_state': np.array([0.2, 0.3, 0.1, 0.4, 0.2]),
            'action': 2,
            'new_state': np.array([0.2, 0.3, 0.4, 0.4, 0.2])
        },
        {
            'name': '突破成功阈值',
            'old_state': np.array([0.5, 0.3, 0.55, 0.4, 0.2]),
            'action': 2,
            'new_state': np.array([0.5, 0.3, 0.65, 0.4, 0.2])
        },
        {
            'name': '过度学习已掌握内容',
            'old_state': np.array([0.5, 0.3, 0.85, 0.4, 0.2]),
            'action': 2,
            'new_state': np.array([0.5, 0.3, 0.95, 0.4, 0.2])
        },
        {
            'name': '完成最终任务',
            'old_state': np.array([0.7, 0.65, 0.55, 0.4, 0.2]),
            'action': 2,
            'new_state': np.array([0.7, 0.65, 0.65, 0.4, 0.2])
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 场景: {scenario['name']}")
        print(f"  动作: 学习知识点 {scenario['action']}")
        print(f"  状态变化: {[f'{x:.2f}' for x in scenario['old_state']]} → {[f'{x:.2f}' for x in scenario['new_state']]}")
        
        # 设置环境状态
        env.current_state = scenario['old_state'].copy()
        
        # 计算奖励
        reward = env._calculate_reward(scenario['action'], scenario['old_state'], scenario['new_state'])
        
        print(f"  获得奖励: {reward:.4f}")
        
        # 分解奖励组成
        improvement = scenario['new_state'][scenario['action']] - scenario['old_state'][scenario['action']]
        base_reward = improvement * 2.0
        
        achievement = 1.0 if (scenario['old_state'][scenario['action']] < 0.6 and 
                             scenario['new_state'][scenario['action']] >= 0.6) else 0.0
        
        efficiency = np.mean(scenario['new_state']) * 0.5
        
        # 检查是否完成任务
        success_count = np.sum(scenario['new_state'] >= 0.6)
        completion = 5.0 if success_count >= 3 else 0.0
        
        penalty = -0.2 if scenario['old_state'][scenario['action']] > 0.8 else 0.0
        
        print(f"    基础奖励: {base_reward:.4f}")
        print(f"    成就奖励: {achievement:.4f}")
        print(f"    效率奖励: {efficiency:.4f}")
        print(f"    完成奖励: {completion:.4f}")
        print(f"    惩罚: {penalty:.4f}")

def main():
    """主演示函数"""
    print("🎯 智能体仿真框架详细演示")
    print("=" * 100)
    
    # 1. 详细回合演示
    detailed_episode_demo()
    
    # 2. 智能体对比
    compare_agents_step_by_step()
    
    # 3. 奖励函数分析
    reward_function_analysis()
    
    print(f"\n" + "=" * 100)
    print(f"📚 总结:")
    print(f"1. 仿真环境模拟了真实的学习过程，包括学习效果、遗忘、依赖关系")
    print(f"2. 奖励函数设计平衡了多个目标：学习效率、任务完成、避免浪费")
    print(f"3. 不同智能体代表不同的学习策略，可以公平比较性能")
    print(f"4. 评估指标全面反映了智能体的学习能力和效率")

if __name__ == "__main__":
    main()
