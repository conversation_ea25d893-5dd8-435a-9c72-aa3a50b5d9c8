#!/usr/bin/env python3
"""
端到端系统测试
"""
import sys
import os
import requests
import time
import json

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), "app"))

# API基础URL
API_BASE_URL = "http://localhost:8001/api/v1"

def test_system_health():
    """测试系统健康状态"""
    print("=== 系统健康检查 ===")
    
    try:
        # 测试健康端点
        response = requests.get(f"{API_BASE_URL.replace('/api/v1', '')}/health", timeout=5)
        if response.status_code == 200:
            print("✓ 系统健康状态正常")
            return True
        else:
            print(f"✗ 系统健康状态异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到系统: {e}")
        return False

def test_data_pipeline():
    """测试数据管道"""
    print("\n=== 数据管道测试 ===")
    
    try:
        # 1. 获取可用数据集
        response = requests.get(f"{API_BASE_URL}/datasets/available", timeout=10)
        if response.status_code != 200:
            print(f"✗ 获取可用数据集失败: {response.status_code}")
            return False
        
        available_data = response.json()
        available_datasets = available_data.get('available_datasets', [])
        training_ready = available_data.get('training_ready', [])
        
        print(f"✓ 可用数据集: {len(available_datasets)} 个")
        print(f"✓ 训练就绪: {len(training_ready)} 个")
        
        if not training_ready:
            print("✗ 没有训练就绪的数据集")
            return False
        
        # 2. 验证数据集
        dataset_name = training_ready[0]
        response = requests.get(f"{API_BASE_URL}/datasets/{dataset_name}/validate", timeout=10)
        if response.status_code != 200:
            print(f"✗ 数据集验证失败: {response.status_code}")
            return False
        
        validation = response.json()
        if not validation.get('valid', False):
            print(f"✗ 数据集无效: {dataset_name}")
            return False
        
        print(f"✓ 数据集验证通过: {dataset_name}")
        
        # 3. 获取可视化数据
        response = requests.get(f"{API_BASE_URL}/datasets/{dataset_name}/visualization-data", timeout=30)
        if response.status_code == 200:
            viz_data = response.json()
            print(f"✓ 可视化数据获取成功")
            print(f"  组件数量: {len(viz_data.get('visualization_data', {}))}")
        else:
            print(f"⚠ 可视化数据获取失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 数据管道测试失败: {e}")
        return False

def test_experiment_workflow():
    """测试实验工作流"""
    print("\n=== 实验工作流测试 ===")
    
    try:
        # 1. 获取实验列表
        response = requests.get(f"{API_BASE_URL}/experiments/", timeout=10)
        if response.status_code != 200:
            print(f"✗ 获取实验列表失败: {response.status_code}")
            return False
        
        experiments = response.json()
        print(f"✓ 实验列表获取成功: {len(experiments)} 个实验")
        
        # 2. 获取支持的模型
        response = requests.get(f"{API_BASE_URL}/models/supported", timeout=10)
        if response.status_code != 200:
            print(f"✗ 获取支持的模型失败: {response.status_code}")
            return False
        
        models_data = response.json()
        models = models_data.get('models', [])
        print(f"✓ 支持的模型: {len(models)} 个")
        
        # 3. 创建测试实验（如果有可用数据集）
        response = requests.get(f"{API_BASE_URL}/datasets/available", timeout=10)
        if response.status_code == 200:
            available_data = response.json()
            training_ready = available_data.get('training_ready', [])
            
            if training_ready and models and len(models) > 0:
                dataset_name = training_ready[0]
                model_name = models[0]['name'] if isinstance(models[0], dict) and 'name' in models[0] else str(models[0])
                
                experiment_data = {
                    "name": f"端到端测试实验_{int(time.time())}",
                    "description": "自动化端到端测试创建的实验",
                    "dataset_id": 1,  # 假设第一个数据集ID为1
                    "model_name": model_name,
                    "hyperparameters": {
                        "learning_rate": 0.001,
                        "batch_size": 32,
                        "epochs": 10
                    }
                }
                
                response = requests.post(f"{API_BASE_URL}/experiments/", 
                                       json=experiment_data, timeout=10)
                if response.status_code in [200, 201]:
                    experiment = response.json()
                    print(f"✓ 测试实验创建成功: {experiment.get('id', 'unknown')}")
                else:
                    print(f"⚠ 测试实验创建失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 实验工作流测试失败: {e}")
        return False

def test_performance_metrics():
    """测试性能指标"""
    print("\n=== 性能指标测试 ===")
    
    performance_results = {}
    
    try:
        # 1. 测试API响应时间
        start_time = time.time()
        response = requests.get(f"{API_BASE_URL}/datasets/", timeout=10)
        response_time = time.time() - start_time
        
        performance_results['datasets_api_response_time'] = response_time
        
        if response.status_code == 200:
            print(f"✓ 数据集API响应时间: {response_time:.3f}s")
        else:
            print(f"✗ 数据集API响应失败: {response.status_code}")
        
        # 2. 测试可用数据集API响应时间
        start_time = time.time()
        response = requests.get(f"{API_BASE_URL}/datasets/available", timeout=10)
        response_time = time.time() - start_time
        
        performance_results['available_datasets_api_response_time'] = response_time
        
        if response.status_code == 200:
            print(f"✓ 可用数据集API响应时间: {response_time:.3f}s")
        else:
            print(f"✗ 可用数据集API响应失败: {response.status_code}")
        
        # 3. 测试实验API响应时间
        start_time = time.time()
        response = requests.get(f"{API_BASE_URL}/experiments/", timeout=10)
        response_time = time.time() - start_time
        
        performance_results['experiments_api_response_time'] = response_time
        
        if response.status_code == 200:
            print(f"✓ 实验API响应时间: {response_time:.3f}s")
        else:
            print(f"✗ 实验API响应失败: {response.status_code}")
        
        # 4. 性能评估
        avg_response_time = sum(performance_results.values()) / len(performance_results)
        print(f"✓ 平均API响应时间: {avg_response_time:.3f}s")
        
        if avg_response_time < 1.0:
            print("✓ 系统性能良好")
        elif avg_response_time < 3.0:
            print("⚠ 系统性能一般")
        else:
            print("✗ 系统性能较差")
        
        return performance_results
        
    except requests.exceptions.RequestException as e:
        print(f"✗ 性能指标测试失败: {e}")
        return {}

def test_error_handling():
    """测试错误处理"""
    print("\n=== 错误处理测试 ===")
    
    error_tests = [
        {
            'name': '不存在的数据集',
            'url': f"{API_BASE_URL}/datasets/nonexistent/validate",
            'expected_status': 404
        },
        {
            'name': '无效的实验ID',
            'url': f"{API_BASE_URL}/experiments/99999",
            'expected_status': 404
        },
        {
            'name': '无效的数据集ID',
            'url': f"{API_BASE_URL}/datasets/99999",
            'expected_status': 404
        }
    ]
    
    passed_tests = 0
    
    for test in error_tests:
        try:
            response = requests.get(test['url'], timeout=5)
            if response.status_code == test['expected_status']:
                print(f"✓ {test['name']}: 正确返回 {response.status_code}")
                passed_tests += 1
            else:
                print(f"✗ {test['name']}: 期望 {test['expected_status']}, 实际 {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"✗ {test['name']}: 请求失败 - {e}")
    
    print(f"✓ 错误处理测试通过: {passed_tests}/{len(error_tests)}")
    return passed_tests == len(error_tests)

def generate_test_report(results):
    """生成测试报告"""
    
    report = {
        'timestamp': time.time(),
        'test_results': results,
        'summary': {
            'total_tests': len(results),
            'passed_tests': sum(1 for r in results.values() if r),
            'failed_tests': sum(1 for r in results.values() if not r),
            'success_rate': sum(1 for r in results.values() if r) / len(results) * 100
        }
    }
    
    return report

def main():
    """主测试函数"""
    print("开始端到端系统测试...")
    print(f"API基础URL: {API_BASE_URL}")
    print("=" * 50)
    
    # 运行所有测试
    test_results = {}
    
    test_results['system_health'] = test_system_health()
    test_results['data_pipeline'] = test_data_pipeline()
    test_results['experiment_workflow'] = test_experiment_workflow()
    
    performance_metrics = test_performance_metrics()
    test_results['performance'] = bool(performance_metrics)
    
    test_results['error_handling'] = test_error_handling()
    
    # 生成报告
    report = generate_test_report(test_results)
    
    print("\n" + "=" * 50)
    print("=== 端到端测试总结 ===")
    print(f"总测试数: {report['summary']['total_tests']}")
    print(f"通过测试: {report['summary']['passed_tests']}")
    print(f"失败测试: {report['summary']['failed_tests']}")
    print(f"成功率: {report['summary']['success_rate']:.1f}%")
    
    if report['summary']['success_rate'] >= 80:
        print("✓ 系统端到端测试基本通过！")
        print("\n系统功能状态:")
        for test_name, result in test_results.items():
            status = "✓" if result else "✗"
            print(f"  {status} {test_name.replace('_', ' ').title()}")
    else:
        print("✗ 系统端到端测试存在问题")
        print("\n需要关注的问题:")
        for test_name, result in test_results.items():
            if not result:
                print(f"  ✗ {test_name.replace('_', ' ').title()}")
    
    print("\n测试完成!")
    return report['summary']['success_rate'] >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
