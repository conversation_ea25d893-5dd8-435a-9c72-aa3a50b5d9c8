/**
 * 学习路径推荐服务
 */

import { message } from 'antd';

// 简单的内网穿透支持函数
const getApiBaseUrl = (): string => {
  console.log('🔍 学习路径服务 - 获取API基础URL...');

  // 优先使用localStorage覆盖配置（用于调试）
  const overrideUrl = localStorage.getItem('OVERRIDE_API_URL');
  if (overrideUrl) {
    console.log('🔧 使用localStorage覆盖配置:', overrideUrl);
    return overrideUrl.replace('/api/v1', ''); // 确保返回基础URL
  }

  // 优先使用环境变量
  if (process.env.REACT_APP_API_URL) {
    const envUrl = process.env.REACT_APP_API_URL.replace('/api/v1', ''); // 确保返回基础URL
    console.log('🌐 使用环境变量配置:', envUrl);
    return envUrl;
  }

  // 检测内网穿透环境
  const currentHost = window.location.host;
  const currentProtocol = window.location.protocol;

  console.log('🌍 当前访问地址:', `${currentProtocol}//${currentHost}`);
  console.log('🔍 检查内网穿透域名...');
  console.log('  - ngrok.io:', currentHost.includes('ngrok.io'));
  console.log('  - cpolar.cn:', currentHost.includes('cpolar.cn'));
  console.log('  - cpolar.top:', currentHost.includes('cpolar.top'));

  // 如果是内网穿透域名，尝试使用相同域名的8000端口
  if (currentHost.includes('ngrok.io') ||
      currentHost.includes('ngrok-free.app') ||
      currentHost.includes('cpolar.top') ||
      currentHost.includes('cpolar.cn') ||  // 添加cpolar.cn支持
      currentHost.includes('localtunnel.me') ||
      currentHost.includes('serveo.net')) {

    let backendUrl = '';

    // 如果当前是3000端口，替换为8000端口
    if (currentHost.includes(':3000')) {
      backendUrl = `${currentProtocol}//${currentHost.replace(':3000', ':8000')}`;
    }
    // 如果是子域名方式，尝试替换前缀
    else if (currentHost.includes('frontend-') || currentHost.includes('app-')) {
      const backendHost = currentHost
        .replace('frontend-', 'backend-')
        .replace('app-', 'api-');
      backendUrl = `${currentProtocol}//${backendHost}`;
    }
    // cpolar特殊处理：检查是否有预设的后端地址映射
    else if (currentHost.includes('cpolar')) {
      // 对于cpolar，通常前端和后端使用不同的子域名
      // 这里需要用户手动配置，或者使用localStorage
      const savedBackendUrl = localStorage.getItem('CPOLAR_BACKEND_URL');
      if (savedBackendUrl) {
        backendUrl = savedBackendUrl;
      } else {
        // 默认尝试相同域名（可能不工作，需要用户手动配置）
        backendUrl = `${currentProtocol}//${currentHost}`;
        console.warn('⚠️ cpolar环境检测到，但未找到后端地址配置。请设置: localStorage.setItem("OVERRIDE_API_URL", "your-backend-url")');
      }
    }
    // 默认使用相同域名（假设后端也通过内网穿透暴露）
    else {
      backendUrl = `${currentProtocol}//${currentHost}`;
    }

    console.log('🚇 检测到内网穿透环境，使用后端URL:', backendUrl);
    return backendUrl;
  }

  // 默认本地开发环境
  console.log('🏠 使用本地开发环境配置');
  return 'http://localhost:8000';
};

export interface KnowledgeDiagnosis {
  mastery_probability: number;
  mastery_level: string;
}

export interface DiagnosisData {
  student_id: string;
  overall_ability: number;
  knowledge_diagnosis: Record<string, KnowledgeDiagnosis>;
}

export interface Preferences {
  max_items?: number;
  difficulty_preference?: string;
  time_constraint?: number;
}

export interface RecommendationRequest {
  user_id: string;
  diagnosis_data: DiagnosisData;
  preferences?: Preferences;
}

export interface KnowledgePoint {
  knowledge_point_id: string;
  knowledge_point_name: string;
  confidence_score: number;
  difficulty_level: string;
  estimated_duration: number;
  category: string;
  prerequisites_met: boolean;
  sequence_order: number;
  learning_suggestion: string;
}

export interface LearningPath {
  total_duration: number;
  overall_difficulty: string;
  sequence: KnowledgePoint[];
  created_at: string;
}

export interface UserProfile {
  overall_ability: number;
  learning_style: string;
  preferred_difficulty: string;
}

export interface RecommendationResponse {
  success: boolean;
  data?: {
    user_id: string;
    learning_path: LearningPath;
    user_profile: UserProfile;
    metadata: {
      total_recommendations: number;
      avg_confidence: number;
      model_info: any;
    };
  };
  error?: string;
}

export interface KnowledgePointInfo {
  id: number;
  name: string;
  difficulty: number;
  category: string;
  prerequisites: string[];
  description: string;
}

export interface ModelStatus {
  model_loaded: boolean;
  embeddings_loaded: boolean;
  embedding_dim: number;
  device: string;
  knowledge_points_count: number;
}

class LearningPathService {
  private getBaseUrl(): string {
    // 每次调用时动态获取API地址，支持内网穿透
    const baseUrl = getApiBaseUrl();

    // 检查baseUrl是否已经包含/api/v1路径
    if (baseUrl.includes('/api/v1')) {
      return `${baseUrl}/learning-path`;
    } else {
      return `${baseUrl}/api/v1/learning-path`;
    }
  }

  /**
   * 获取学习路径推荐
   */
  async getRecommendations(request: RecommendationRequest): Promise<RecommendationResponse> {
    const url = `${this.getBaseUrl()}/recommend`;
    console.log('📡 发送学习路径推荐请求:', url);
    console.log('📝 请求数据:', request);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      console.log('📨 响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ API错误响应:', errorData);
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ 获取学习路径推荐成功:', data);
      return data;
    } catch (error: any) {
      console.error('❌ 获取学习路径推荐失败:', error);
      console.error('🔍 错误详情:', {
        message: error.message,
        stack: error.stack,
        url: url
      });

      // 显示用户友好的错误信息
      message.error(`获取学习路径失败: ${error.message}`);

      return {
        success: false,
        error: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 获取知识点详细信息
   */
  async getKnowledgePointInfo(knowledgeId: number): Promise<{
    success: boolean;
    data?: KnowledgePointInfo;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/knowledge-point/${knowledgeId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      console.error('获取知识点信息失败:', error);
      return {
        success: false,
        error: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 获取模型状态
   */
  async getModelStatus(): Promise<{
    success: boolean;
    data?: ModelStatus;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/model/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      console.error('获取模型状态失败:', error);
      return {
        success: false,
        error: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 加载模型
   */
  async loadModel(modelPath?: string, embeddingsPath?: string): Promise<{
    success: boolean;
    message?: string;
    data?: ModelStatus;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/model/load`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model_path: modelPath,
          embeddings_path: embeddingsPath,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      console.error('加载模型失败:', error);
      return {
        success: false,
        error: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 模拟推荐（用于测试）
   */
  async simulateRecommendation(userId: string = 'test_user'): Promise<RecommendationResponse> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/simulate?user_id=${encodeURIComponent(userId)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      console.error('模拟推荐失败:', error);
      return {
        success: false,
        error: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    success: boolean;
    message?: string;
    timestamp?: string;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      console.error('健康检查失败:', error);
      return {
        success: false,
        error: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 转换认知诊断数据格式
   */
  convertDiagnosisData(diagnosisData: any): DiagnosisData {
    const knowledgeDiagnosis: Record<string, KnowledgeDiagnosis> = {};
    
    if (diagnosisData.knowledge_diagnosis) {
      Object.entries(diagnosisData.knowledge_diagnosis).forEach(([key, value]: [string, any]) => {
        knowledgeDiagnosis[key] = {
          mastery_probability: value.mastery_probability || 0.5,
          mastery_level: value.mastery_level || '未知'
        };
      });
    }

    return {
      student_id: diagnosisData.student_id || 'unknown',
      overall_ability: diagnosisData.overall_ability || 0.5,
      knowledge_diagnosis: knowledgeDiagnosis
    };
  }

  /**
   * 显示推荐成功消息
   */
  showRecommendationSuccess(learningPath: LearningPath) {
    message.success(
      `成功生成学习路径！包含${learningPath.sequence.length}个知识点，预计总时长${learningPath.total_duration}分钟`
    );
  }

  /**
   * 显示推荐失败消息
   */
  showRecommendationError(error: string) {
    message.error(`获取学习路径推荐失败: ${error}`);
  }
  /**
   * 调试API配置 - 打印当前配置信息
   */
  debugApiConfig(): void {
    console.log('🔧 === 学习路径服务API配置调试 ===');
    console.log('🌐 当前页面URL:', window.location.href);
    console.log('🏠 当前主机:', window.location.host);
    console.log('🔗 协议:', window.location.protocol);
    console.log('📡 API基础URL:', getApiBaseUrl());
    console.log('🎯 完整服务URL:', this.getBaseUrl());
    console.log('💾 localStorage覆盖:', localStorage.getItem('OVERRIDE_API_URL'));
    console.log('🌍 环境变量:', process.env.REACT_APP_API_URL);
    console.log('=================================');
  }
}

// 导出单例实例
export const learningPathService = new LearningPathService();

// 在开发环境下自动打印调试信息
if (process.env.NODE_ENV === 'development') {
  learningPathService.debugApiConfig();
}
export default learningPathService;
