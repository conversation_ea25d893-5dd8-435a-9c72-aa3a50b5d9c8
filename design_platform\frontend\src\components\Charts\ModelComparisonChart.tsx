import React from 'react';
import ReactECharts from 'echarts-for-react';

interface ModelComparisonChartProps {
  data: {
    models: string[];
    metrics: {
      name: string;
      data: number[];
    }[];
  };
  title?: string;
  height?: string;
  chartType?: 'bar' | 'radar';
}

const ModelComparisonChart: React.FC<ModelComparisonChartProps> = ({
  data,
  title = '模型性能对比',
  height = '400px',
  chartType = 'bar',
}) => {
  const getBarChartOption = () => ({
    title: {
      text: title,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      data: data.metrics.map(m => m.name),
      bottom: 0,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.models,
      name: '模型',
      nameLocation: 'middle',
      nameGap: 30,
    },
    yAxis: {
      type: 'value',
      name: '性能指标',
      min: 0,
      max: 1,
    },
    series: data.metrics.map((metric, index) => ({
      name: metric.name,
      type: 'bar',
      data: metric.data,
      itemStyle: {
        color: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'][index % 5],
      },
    })),
  });

  const getRadarChartOption = () => {
    const indicators = data.metrics.map(metric => ({
      name: metric.name,
      max: 1,
    }));

    const seriesData = data.models.map((model, modelIndex) => ({
      value: data.metrics.map(metric => metric.data[modelIndex]),
      name: model,
    }));

    return {
      title: {
        text: title,
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
      },
      legend: {
        data: data.models,
        bottom: 0,
      },
      radar: {
        indicator: indicators,
        radius: '60%',
        center: ['50%', '50%'],
      },
      series: [
        {
          name: '模型性能',
          type: 'radar',
          data: seriesData,
          itemStyle: {
            normal: {
              areaStyle: {
                opacity: 0.3,
              },
            },
          },
        },
      ],
    };
  };

  const option = chartType === 'radar' ? getRadarChartOption() : getBarChartOption();

  return <ReactECharts option={option} style={{ height }} />;
};

export default ModelComparisonChart;
