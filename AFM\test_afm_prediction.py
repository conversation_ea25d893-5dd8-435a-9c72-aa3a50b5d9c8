#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AFM模型预测功能
"""

import numpy as np
import torch
import pickle
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_afm_prediction():
    """测试AFM模型的预测功能"""
    print("=== 测试AFM模型预测功能 ===")
    
    try:
        # 加载嵌入数据
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings = np.array(pickle.load(f))
        with open('itm_emb_np.pkl', 'rb') as f:
            item_embeddings = np.array(pickle.load(f))
        
        # 处理嵌入维度
        if len(user_embeddings.shape) == 3:
            user_embeddings = user_embeddings.reshape(user_embeddings.shape[0], -1)
        if len(item_embeddings.shape) == 3:
            item_embeddings = item_embeddings.reshape(item_embeddings.shape[0], -1)
        
        print(f"用户嵌入形状: {user_embeddings.shape}")
        print(f"物品嵌入形状: {item_embeddings.shape}")
        
        # 加载AFM模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model_path = 'afm_model.pth'
        
        if os.path.exists(model_path):
            try:
                model = torch.load(model_path, map_location=device, weights_only=False)
                model.eval()
                print(f"✓ AFM模型加载成功")
                
                # 测试预测
                user_id = 0
                user_emb = user_embeddings[user_id]
                
                print(f"\n=== 测试用户{user_id}的学习效果预测 ===")
                
                for i in range(min(5, len(item_embeddings))):
                    item_emb = item_embeddings[i]
                    
                    # 按照训练时的格式构造输入：(1, 2, 102)
                    combined_features = np.stack([user_emb, item_emb], axis=0)  # [2, 102]
                    input_tensor = torch.FloatTensor(combined_features).unsqueeze(0).to(device)  # [1, 2, 102]
                    
                    # 模型预测
                    with torch.no_grad():
                        logits = model(input_tensor)
                        prediction = torch.sigmoid(logits).cpu().numpy()[0, 0]
                    
                    print(f"知识点{i}: 预测学习效果 = {prediction:.4f}")
                
                # 测试推荐功能
                print(f"\n=== 测试推荐功能 ===")
                from test4 import recommend_knowledge_paths
                
                ranked_indices, scores = recommend_knowledge_paths(
                    model, user_emb, item_embeddings[:10], device, top_k=5
                )
                
                print("推荐的知识点顺序:")
                for i, (idx, score) in enumerate(scores[:5]):
                    print(f"  {i+1}. 知识点{idx}: 得分 = {score:.4f}")
                
                return True
                
            except Exception as e:
                print(f"✗ AFM模型测试失败: {e}")
                return False
        else:
            print(f"✗ AFM模型文件不存在: {model_path}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_afm_agent():
    """测试AFM智能体"""
    print(f"\n=== 测试AFM智能体 ===")
    
    try:
        from optimized_simulator import RealAFMAgent
        
        # 创建AFM智能体
        agent = RealAFMAgent(action_space_size=5, user_id=0)
        
        # 模拟观察状态
        observation = np.array([0.2, 0.3, 0.1, 0.4, 0.15])
        print(f"当前学习状态: {observation}")
        
        # 测试预测功能
        print(f"\n各知识点的学习效果预测:")
        for i in range(5):
            prediction = agent.predict_learning_effect(i)
            print(f"  知识点{i}: {prediction:.4f}")
        
        # 测试决策功能
        action = agent.get_action(observation)
        print(f"\n推荐的学习动作: {action}")
        
        return True
        
    except Exception as e:
        print(f"✗ AFM智能体测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_afm_prediction()
    success2 = test_afm_agent()
    
    if success1 and success2:
        print(f"\n✓ 所有测试通过！")
    else:
        print(f"\n✗ 部分测试失败")
