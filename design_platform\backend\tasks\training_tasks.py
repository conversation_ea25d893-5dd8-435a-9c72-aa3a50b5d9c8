"""
训练任务
"""
import os
import sys
import json
import time
import traceback
from datetime import datetime
from typing import Dict, Any

from celery import current_task
from tasks.celery import celery_app
from app.core.database import SessionLocal
from app.models.experiment import Experiment, ExperimentStatus
from app.services.experiment_service import ExperimentService

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), "../.."))


@celery_app.task(bind=True)
def start_training_task(self, experiment_id: int):
    """启动训练任务"""
    db = SessionLocal()
    service = ExperimentService(db)
    
    try:
        # 获取实验信息
        experiment = service.get_experiment(experiment_id)
        if not experiment:
            raise ValueError(f"实验 {experiment_id} 不存在")
        
        # 更新实验状态
        service.update_experiment_status(
            experiment_id, 
            ExperimentStatus.RUNNING,
            self.request.id
        )
        
        # 记录开始时间
        experiment.started_at = datetime.utcnow()
        db.commit()
        
        # 准备训练配置
        config = experiment.config or {}
        dataset_type = experiment.dataset_type
        model_type = experiment.model_type
        
        # 执行训练
        result = _execute_training(
            experiment_id=experiment_id,
            dataset_type=dataset_type,
            model_type=model_type,
            config=config,
            task=self
        )
        
        # 更新实验结果
        service.update_experiment_results(experiment_id, result)
        service.update_experiment_status(experiment_id, ExperimentStatus.COMPLETED)
        
        # 记录完成时间
        experiment.completed_at = datetime.utcnow()
        db.commit()
        
        return result
        
    except Exception as e:
        # 记录错误信息
        error_msg = str(e)
        traceback_str = traceback.format_exc()
        
        service.update_experiment_status(
            experiment_id, 
            ExperimentStatus.FAILED,
            error_message=f"{error_msg}\n\n{traceback_str}"
        )
        
        raise e
    
    finally:
        db.close()


def run_training_task(experiment_id: int):
    """直接运行训练任务（不使用 Celery）"""
    db = SessionLocal()
    service = ExperimentService(db)

    try:
        # 获取实验信息
        experiment = service.get_experiment(experiment_id)
        if not experiment:
            raise ValueError(f"实验 {experiment_id} 不存在")

        # 记录开始时间
        experiment.started_at = datetime.utcnow()
        db.commit()

        # 准备训练配置
        config = experiment.config or {}
        dataset_type = experiment.dataset_type
        model_type = experiment.model_type

        # 执行训练（简化版本，不更新进度）
        result = _execute_training_simple(
            experiment_id=experiment_id,
            dataset_type=dataset_type,
            model_type=model_type,
            config=config
        )

        # 更新实验结果
        service.update_experiment_results(experiment_id, result)
        service.update_experiment_status(experiment_id, ExperimentStatus.COMPLETED)

        # 记录完成时间
        experiment.completed_at = datetime.utcnow()
        db.commit()

        return result

    except Exception as e:
        # 记录错误信息
        error_msg = str(e)
        traceback_str = traceback.format_exc()

        service.update_experiment_status(
            experiment_id,
            ExperimentStatus.FAILED,
            error_message=f"{error_msg}\n\n{traceback_str}"
        )

        raise e

    finally:
        db.close()


def _execute_training(
    experiment_id: int,
    dataset_type: str,
    model_type: str,
    config: Dict[str, Any],
    task
) -> Dict[str, Any]:
    """执行具体的训练逻辑 - 真实数据版本"""

    # 导入服务
    from app.services.model_integration import ModelIntegrator

    # 更新进度
    task.update_state(state='PROGRESS', meta={'progress': 10, 'status': '准备真实数据集...'})

    # 使用模型集成器
    integrator = ModelIntegrator()

    # 更新进度
    task.update_state(state='PROGRESS', meta={'progress': 20, 'status': '初始化模型...'})

    # 创建进度回调函数
    def progress_callback(epoch, total_epochs, metrics):
        progress = 30 + (epoch / total_epochs) * 60  # 30-90%的进度用于训练
        task.update_state(
            state='PROGRESS',
            meta={
                'progress': progress,
                'status': f'训练中... Epoch {epoch}/{total_epochs}',
                'current_epoch': epoch,
                'total_epochs': total_epochs,
                'metrics': metrics
            }
        )

    # 更新进度
    task.update_state(state='PROGRESS', meta={'progress': 30, 'status': '开始真实训练...'})

    try:
        # 使用真实数据训练
        training_result = integrator.train_model_with_real_data(
            model_type=model_type,
            dataset_name=dataset_type,
            config=config,
            progress_callback=progress_callback
        )

        # 添加训练成功标记
        training_result['training_success'] = True
        training_result['training_type'] = 'real_data'

    except Exception as e:
        # 如果真实训练失败，回退到模拟训练
        task.update_state(state='PROGRESS', meta={'progress': 30, 'status': '回退到模拟训练...'})

        try:
            # 尝试使用旧的训练方法
            training_result = _execute_fallback_training(
                experiment_id, dataset_type, model_type, config, task, progress_callback
            )
            training_result['training_success'] = True
            training_result['training_type'] = 'fallback'
            training_result['fallback_reason'] = str(e)

        except Exception as fallback_error:
            # 如果回退也失败，生成模拟结果
            training_result = integrator._generate_mock_training_result(config)
            training_result['training_success'] = False
            training_result['training_type'] = 'mock'
            training_result['error'] = str(fallback_error)

    # 更新进度到90%
    task.update_state(state='PROGRESS', meta={'progress': 90, 'status': '保存模型...'})

    # 保存模型（如果训练成功）
    if training_result.get('training_success', False):
        try:
            model_path = f"models/experiment_{experiment_id}"
            os.makedirs(model_path, exist_ok=True)

            # 保存模型元数据
            metadata = {
                'experiment_id': experiment_id,
                'model_type': model_type,
                'dataset_type': dataset_type,
                'config': config,
                'training_type': training_result.get('training_type', 'unknown'),
                'completed_at': datetime.utcnow().isoformat()
            }

            with open(os.path.join(model_path, 'metadata.json'), 'w') as f:
                json.dump(metadata, f, indent=2)

            training_result['model_path'] = model_path

        except Exception as e:
            print(f"保存模型失败: {e}")

    # 更新进度到100%
    task.update_state(state='PROGRESS', meta={'progress': 100, 'status': '训练完成'})

    # 返回训练结果
    result = {
        'model_type': model_type,
        'dataset_type': dataset_type,
        'config': config,
        'metrics': training_result.get('metrics', {}),
        'training_history': training_result.get('history', {}),
        'training_type': training_result.get('training_type', 'unknown'),
        'training_success': training_result.get('training_success', False),
        'model_path': training_result.get('model_path', ''),
        'completed_at': datetime.utcnow().isoformat()
    }

    # 添加额外信息
    if 'dataset_info' in training_result:
        result['dataset_info'] = training_result['dataset_info']
    if 'data_statistics' in training_result:
        result['data_statistics'] = training_result['data_statistics']
    if 'fallback_reason' in training_result:
        result['fallback_reason'] = training_result['fallback_reason']
    if 'error' in training_result:
        result['error'] = training_result['error']

    return result


def _execute_fallback_training(
    experiment_id: int,
    dataset_type: str,
    model_type: str,
    config: Dict[str, Any],
    task,
    progress_callback
) -> Dict[str, Any]:
    """回退训练方法"""
    try:
        from inscd.datahub import DataHub
        from app.services.model_integration import ModelIntegrator

        # 尝试加载数据集
        dataset_path = os.path.join("data/datasets", dataset_type)
        if not os.path.exists(dataset_path):
            dataset_path = os.path.join("../datasets", dataset_type)

        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"数据集路径不存在: {dataset_path}")

        datahub = DataHub(dataset_path)
        integrator = ModelIntegrator()

        # 创建模型
        model, default_params = integrator.create_model(
            model_type,
            datahub.student_num,
            datahub.exercise_num,
            datahub.knowledge_num
        )

        # 构建模型
        model = integrator.build_model(model, model_type, config)

        # 执行训练
        training_result = integrator.train_model(
            model,
            datahub,
            config,
            progress_callback=progress_callback
        )

        return training_result

    except Exception as e:
        raise RuntimeError(f"回退训练失败: {str(e)}")

    # 更新进度
    task.update_state(state='PROGRESS', meta={'progress': 90, 'status': '保存模型...'})

    # 保存模型
    model_dir = f"models/experiment_{experiment_id}"
    os.makedirs(model_dir, exist_ok=True)
    model_path = os.path.join(model_dir, f"{model_type}_model.pt")

    # 保存模型和元数据
    metadata = {
        'experiment_id': experiment_id,
        'model_type': model_type,
        'dataset_type': dataset_type,
        'config': config,
        'training_result': training_result
    }

    integrator.save_model(model, model_path, metadata)

    # 更新进度
    task.update_state(state='PROGRESS', meta={'progress': 100, 'status': '训练完成'})

    # 返回训练结果
    result = {
        'model_type': model_type,
        'dataset_type': dataset_type,
        'config': config,
        'metrics': training_result.get('metrics', {}),
        'training_history': training_result.get('history', {}),
        'model_path': model_path,
        'completed_at': datetime.utcnow().isoformat()
    }

    return result


@celery_app.task
def cleanup_training_files(experiment_id: int):
    """清理训练文件"""
    try:
        model_dir = f"models/experiment_{experiment_id}"
        if os.path.exists(model_dir):
            import shutil
            shutil.rmtree(model_dir)
        return f"清理实验 {experiment_id} 的文件成功"
    except Exception as e:
        return f"清理实验 {experiment_id} 的文件失败: {str(e)}"
