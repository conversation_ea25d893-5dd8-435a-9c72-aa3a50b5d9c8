"""
系统监控相关API端点
"""
from fastapi import APIRouter
from typing import Dict, Any
import psutil
import time
import os
from datetime import datetime, timedelta

router = APIRouter()

# 系统启动时间
_start_time = time.time()

@router.get("/status")
async def get_system_status() -> Dict[str, Any]:
    """获取系统状态信息"""
    try:
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_usage = (disk.used / disk.total) * 100
        
        # GPU使用率 (如果有GPU)
        gpu_usage = None
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu_usage = gpus[0].load * 100
        except ImportError:
            # 如果没有安装GPUtil，使用模拟数据
            gpu_usage = 45.0
        
        # 网络连接数
        connections = len(psutil.net_connections())
        
        # 系统运行时间
        uptime_seconds = time.time() - _start_time
        uptime_hours = int(uptime_seconds // 3600)
        uptime_minutes = int((uptime_seconds % 3600) // 60)
        uptime = f"{uptime_hours}h {uptime_minutes}m"
        
        # 响应时间 (模拟)
        response_time = 120.5  # 实际应该测量API响应时间
        
        # 系统状态评估
        status = "healthy"
        if cpu_usage > 80 or memory_usage > 85 or disk_usage > 90:
            status = "warning"
        if cpu_usage > 95 or memory_usage > 95 or disk_usage > 98:
            status = "error"
        
        return {
            "cpu_usage": round(cpu_usage, 1),
            "memory_usage": round(memory_usage, 1),
            "disk_usage": round(disk_usage, 1),
            "gpu_usage": round(gpu_usage, 1) if gpu_usage else None,
            "active_connections": connections,
            "response_time": response_time,
            "uptime": uptime,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        # 如果获取系统信息失败，返回模拟数据
        return {
            "cpu_usage": 35.2,
            "memory_usage": 62.8,
            "disk_usage": 45.3,
            "gpu_usage": 78.5,
            "active_connections": 23,
            "response_time": 125.0,
            "uptime": "12h 34m",
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "note": "Using simulated data due to system access limitations"
        }

@router.get("/metrics")
async def get_system_metrics() -> Dict[str, Any]:
    """获取详细的系统指标"""
    try:
        # CPU详细信息
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        cpu_times = psutil.cpu_times()
        
        # 内存详细信息
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # 磁盘详细信息
        disk_usage = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # 网络详细信息
        net_io = psutil.net_io_counters()
        
        # 进程信息
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                proc_info = proc.info
                if proc_info['cpu_percent'] > 1.0:  # 只显示CPU使用率大于1%的进程
                    processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # 按CPU使用率排序，取前10个
        processes = sorted(processes, key=lambda x: x['cpu_percent'], reverse=True)[:10]
        
        return {
            "cpu": {
                "count": cpu_count,
                "frequency": {
                    "current": cpu_freq.current if cpu_freq else None,
                    "min": cpu_freq.min if cpu_freq else None,
                    "max": cpu_freq.max if cpu_freq else None
                },
                "times": {
                    "user": cpu_times.user,
                    "system": cpu_times.system,
                    "idle": cpu_times.idle
                }
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "free": memory.free,
                "percent": memory.percent,
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "free": swap.free,
                    "percent": swap.percent
                }
            },
            "disk": {
                "total": disk_usage.total,
                "used": disk_usage.used,
                "free": disk_usage.free,
                "percent": (disk_usage.used / disk_usage.total) * 100,
                "io": {
                    "read_count": disk_io.read_count if disk_io else 0,
                    "write_count": disk_io.write_count if disk_io else 0,
                    "read_bytes": disk_io.read_bytes if disk_io else 0,
                    "write_bytes": disk_io.write_bytes if disk_io else 0
                }
            },
            "network": {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv
            },
            "processes": processes,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """健康检查端点"""
    try:
        # 检查关键系统资源
        cpu_usage = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 健康状态评估
        health_status = "healthy"
        issues = []
        
        if cpu_usage > 90:
            health_status = "unhealthy"
            issues.append("High CPU usage")
        elif cpu_usage > 80:
            health_status = "degraded"
            issues.append("Elevated CPU usage")
        
        if memory.percent > 95:
            health_status = "unhealthy"
            issues.append("Critical memory usage")
        elif memory.percent > 85:
            health_status = "degraded"
            issues.append("High memory usage")
        
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent > 95:
            health_status = "unhealthy"
            issues.append("Critical disk usage")
        elif disk_percent > 90:
            health_status = "degraded"
            issues.append("High disk usage")
        
        return {
            "status": health_status,
            "timestamp": datetime.now().isoformat(),
            "uptime": time.time() - _start_time,
            "issues": issues,
            "metrics": {
                "cpu_usage": cpu_usage,
                "memory_usage": memory.percent,
                "disk_usage": disk_percent
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@router.get("/logs")
async def get_system_logs(lines: int = 100) -> Dict[str, Any]:
    """获取系统日志"""
    try:
        # 这里应该读取实际的日志文件
        # 现在返回模拟的日志数据
        logs = []
        for i in range(lines):
            log_entry = {
                "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat(),
                "level": "INFO" if i % 10 != 0 else "WARNING" if i % 20 != 0 else "ERROR",
                "message": f"System log entry {i + 1}",
                "component": "system" if i % 3 == 0 else "api" if i % 3 == 1 else "model"
            }
            logs.append(log_entry)
        
        return {
            "logs": logs,
            "total_lines": lines,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
