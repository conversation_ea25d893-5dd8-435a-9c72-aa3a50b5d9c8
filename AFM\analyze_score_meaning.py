#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析AFM模型中分数的确切含义
"""

import numpy as np
import pickle
from scipy.sparse import vstack

def analyze_training_data_structure():
    """分析训练数据的结构来理解分数含义"""
    print("=== 分析训练数据结构 ===")
    
    try:
        # 加载训练数据
        with open('trn_mat.pkl', 'rb') as f:
            trn_mat = pickle.load(f)
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings = np.array(pickle.load(f))
        with open('itm_emb_np.pkl', 'rb') as f:
            item_embeddings = np.array(pickle.load(f))
        
        # 处理嵌入维度
        if len(user_embeddings.shape) == 3:
            user_embeddings = user_embeddings.reshape(user_embeddings.shape[0], -1)
        if len(item_embeddings.shape) == 3:
            item_embeddings = item_embeddings.reshape(item_embeddings.shape[0], -1)
        
        print(f"训练矩阵形状: {trn_mat.shape}")
        print(f"用户嵌入数量: {len(user_embeddings)}")
        print(f"物品嵌入数量: {len(item_embeddings)}")
        
        # 关键分析：数据的对应关系
        print(f"\n🔍 关键分析：")
        print(f"每一行训练数据对应：")
        print(f"  - 一个用户嵌入 (102维)")
        print(f"  - 一个物品嵌入 (102维)")  
        print(f"  - 一个标签 (0或1)")
        
        print(f"\n这意味着每一行代表：")
        print(f"  一个具体的 (用户, 物品) 交互记录")
        print(f"  标签表示这次交互的结果")
        
        # 分析物品的含义
        print(f"\n🤔 物品嵌入的含义分析：")
        print(f"物品嵌入数量 = 用户嵌入数量 = {len(item_embeddings)}")
        print(f"这种1:1的对应关系说明：")
        print(f"  选项A: 每个用户对应一个专属的学习内容")
        print(f"  选项B: 每行数据是一个独立的交互实例")
        print(f"  选项C: 物品嵌入实际上是题目/练习的嵌入")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def analyze_prediction_granularity():
    """分析预测的粒度级别"""
    print(f"\n=== 分析预测粒度 ===")
    
    print(f"🎯 两种可能的解释：")
    
    print(f"\n解释1: 知识点级别预测")
    print(f"  - AFM预测: 用户对整个知识点的掌握概率")
    print(f"  - 训练数据: 用户学习知识点后的掌握情况")
    print(f"  - 预测含义: P(用户掌握知识点X)")
    
    print(f"\n解释2: 题目级别预测")
    print(f"  - AFM预测: 用户答对特定题目的概率")
    print(f"  - 训练数据: 用户答题的历史记录")
    print(f"  - 预测含义: P(用户答对题目Y)")
    
    print(f"\n解释3: 交互级别预测")
    print(f"  - AFM预测: 用户与学习内容交互成功的概率")
    print(f"  - 训练数据: 各种学习交互的成功/失败记录")
    print(f"  - 预测含义: P(用户成功完成学习任务Z)")

def analyze_embedding_relationship():
    """分析嵌入之间的关系"""
    print(f"\n=== 分析嵌入关系 ===")
    
    try:
        with open('usr_emb_np.pkl', 'rb') as f:
            user_embeddings = np.array(pickle.load(f))
        with open('itm_emb_np.pkl', 'rb') as f:
            item_embeddings = np.array(pickle.load(f))
        
        # 处理维度
        if len(user_embeddings.shape) == 3:
            user_embeddings = user_embeddings.reshape(user_embeddings.shape[0], -1)
        if len(item_embeddings.shape) == 3:
            item_embeddings = item_embeddings.reshape(item_embeddings.shape[0], -1)
        
        print(f"用户嵌入统计:")
        print(f"  形状: {user_embeddings.shape}")
        print(f"  均值: {np.mean(user_embeddings):.6f}")
        print(f"  标准差: {np.std(user_embeddings):.6f}")
        
        print(f"\n物品嵌入统计:")
        print(f"  形状: {item_embeddings.shape}")
        print(f"  均值: {np.mean(item_embeddings):.6f}")
        print(f"  标准差: {np.std(item_embeddings):.6f}")
        
        # 检查是否有重复的嵌入
        unique_users = len(np.unique(user_embeddings.view(np.void), axis=0))
        unique_items = len(np.unique(item_embeddings.view(np.void), axis=0))
        
        print(f"\n唯一性分析:")
        print(f"  唯一用户嵌入数: {unique_users}")
        print(f"  唯一物品嵌入数: {unique_items}")
        print(f"  总用户嵌入数: {len(user_embeddings)}")
        print(f"  总物品嵌入数: {len(item_embeddings)}")
        
        if unique_users < len(user_embeddings):
            print(f"  → 用户嵌入有重复，说明同一用户有多条记录")
        if unique_items < len(item_embeddings):
            print(f"  → 物品嵌入有重复，说明同一物品有多条记录")
            
        return user_embeddings, item_embeddings
        
    except Exception as e:
        print(f"❌ 嵌入分析失败: {e}")
        return None, None

def test_afm_predictions():
    """测试AFM预测来理解其含义"""
    print(f"\n=== 测试AFM预测 ===")
    
    try:
        from optimized_simulator import RealAFMAgent
        
        # 创建多个用户的Agent
        agents = []
        for user_id in range(min(3, 10)):  # 测试前3个用户
            agent = RealAFMAgent(action_space_size=5, user_id=user_id)
            if agent.model is not None:
                agents.append((user_id, agent))
        
        if not agents:
            print("❌ 无法创建AFM智能体")
            return
        
        print(f"🧪 测试不同用户对相同知识点的预测:")
        for knowledge_id in range(5):
            print(f"\n知识点{knowledge_id}:")
            predictions = []
            for user_id, agent in agents:
                pred = agent.predict_learning_effect(knowledge_id)
                predictions.append(pred)
                print(f"  用户{user_id}: {pred:.4f}")
            
            pred_std = np.std(predictions)
            print(f"  标准差: {pred_std:.4f}")
            
            if pred_std > 0.01:
                print(f"  → 不同用户预测差异显著，体现个性化")
            else:
                print(f"  → 不同用户预测相似，可能缺乏个性化")
        
        print(f"\n🧪 测试同一用户对不同知识点的预测:")
        user_id, agent = agents[0]
        print(f"用户{user_id}:")
        predictions = []
        for knowledge_id in range(5):
            pred = agent.predict_learning_effect(knowledge_id)
            predictions.append(pred)
            print(f"  知识点{knowledge_id}: {pred:.4f}")
        
        pred_std = np.std(predictions)
        print(f"标准差: {pred_std:.4f}")
        
        if pred_std > 0.01:
            print(f"→ 同一用户对不同知识点预测有差异，体现内容区分度")
        else:
            print(f"→ 同一用户对不同知识点预测相似，可能缺乏内容区分度")
            
    except Exception as e:
        print(f"❌ AFM预测测试失败: {e}")

def infer_score_meaning():
    """基于分析结果推断分数含义"""
    print(f"\n=== 推断分数含义 ===")
    
    print(f"🔍 基于数据结构分析：")
    print(f"1. 训练数据: 每行是一个(用户,物品)交互记录")
    print(f"2. 标签: 该交互的成功/失败结果")
    print(f"3. 用户嵌入: 代表用户的学习特征")
    print(f"4. 物品嵌入: 代表学习内容的特征")
    
    print(f"\n🎯 最可能的分数含义：")
    print(f"AFM(用户A, 物品B) = 0.7 表示：")
    print(f"  '用户A与物品B进行学习交互时成功的概率是70%'")
    
    print(f"\n📚 在教育场景中，这可能是：")
    print(f"  选项1: 用户A答对物品B(题目)的概率")
    print(f"  选项2: 用户A成功学习物品B(知识点)的概率")
    print(f"  选项3: 用户A完成物品B(学习任务)的概率")
    
    print(f"\n🤔 关键区别：")
    print(f"  - 如果是题目级别: 预测单次答题结果")
    print(f"  - 如果是知识点级别: 预测整体掌握情况")
    print(f"  - 如果是任务级别: 预测学习任务完成情况")
    
    print(f"\n💡 实际应用中的含义：")
    print(f"  无论具体是哪种，AFM分数都反映了：")
    print(f"  '用户在当前状态下，与该学习内容交互的成功概率'")
    print(f"  这个概率可以用来：")
    print(f"  - 评估用户当前能力水平")
    print(f"  - 预测学习效果")
    print(f"  - 指导个性化推荐")

def main():
    """主分析函数"""
    print("🔍 分析AFM模型中分数的确切含义")
    print("=" * 60)
    
    # 分析训练数据结构
    analyze_training_data_structure()
    
    # 分析预测粒度
    analyze_prediction_granularity()
    
    # 分析嵌入关系
    user_emb, item_emb = analyze_embedding_relationship()
    
    # 测试AFM预测
    test_afm_predictions()
    
    # 推断分数含义
    infer_score_meaning()
    
    print(f"\n" + "=" * 60)
    print(f"🎯 结论：")
    print(f"AFM分数最可能的含义是：")
    print(f"'用户与特定学习内容交互时成功的概率'")
    print(f"")
    print(f"具体可能是：")
    print(f"1. 答对该内容相关题目的概率")
    print(f"2. 成功掌握该知识点的概率") 
    print(f"3. 完成该学习任务的概率")
    print(f"")
    print(f"无论具体是哪种，都反映了用户对该内容的'适配程度'。")

if __name__ == "__main__":
    main()
