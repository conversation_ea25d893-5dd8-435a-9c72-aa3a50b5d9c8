"""
认知诊断相关的数据模型
"""
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
from app.models.experiment import ModelType


class TrainingRequest(BaseModel):
    """训练请求模型"""
    experiment_id: int
    model_type: ModelType
    config: Dict[str, Any]
    
    class Config:
        from_attributes = True


class TrainingResponse(BaseModel):
    """训练响应模型"""
    success: bool
    message: str
    experiment_id: int
    results: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class DiagnosisRequest(BaseModel):
    """诊断请求模型"""
    student_id: str
    experiment_id: int
    
    class Config:
        from_attributes = True


class DiagnosisResponse(BaseModel):
    """诊断响应模型"""
    success: bool
    student_id: str
    diagnosis: Dict[str, Any]
    
    class Config:
        from_attributes = True


class LearningPathRequest(BaseModel):
    """学习路径请求模型"""
    student_id: str
    experiment_id: int
    preferences: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class LearningPathResponse(BaseModel):
    """学习路径响应模型"""
    success: bool
    student_id: str
    learning_path: Dict[str, Any]
    diagnosis: Dict[str, Any]
    
    class Config:
        from_attributes = True


class KnowledgeComponent(BaseModel):
    """知识点模型"""
    id: int
    name: str
    description: str
    subject: Optional[str] = None
    difficulty: Optional[float] = None
    
    class Config:
        from_attributes = True


class StudentDiagnosis(BaseModel):
    """学生诊断结果模型"""
    student_id: str
    knowledge_diagnosis: Dict[str, Any]
    overall_ability: float
    diagnosis_time: str
    confidence_level: Optional[float] = None
    
    class Config:
        from_attributes = True


class LearningActivity(BaseModel):
    """学习活动模型"""
    activity_id: str
    type: str
    title: str
    description: str
    duration_minutes: int
    difficulty_level: str
    resources: Optional[List[Dict[str, Any]]] = None
    
    class Config:
        from_attributes = True


class LearningStage(BaseModel):
    """学习阶段模型"""
    stage: int
    knowledge_component: str
    current_mastery: float
    target_mastery: float
    learning_strategy: str
    activities: List[LearningActivity]
    estimated_duration: int
    prerequisites: List[str]
    success_criteria: List[str]
    
    class Config:
        from_attributes = True


class LearningPath(BaseModel):
    """学习路径模型"""
    student_id: str
    generated_at: str
    learning_path: List[LearningStage]
    recommendations: Dict[str, Any]
    estimated_time: Dict[str, Any]
    weak_knowledge_components: List[Dict[str, Any]]
    strong_knowledge_components: List[Dict[str, Any]]
    overall_ability_level: str
    
    class Config:
        from_attributes = True


class ModelComparison(BaseModel):
    """模型对比结果"""
    experiment_id: int
    model_type: str
    accuracy: float
    auc: float
    rmse: float
    training_time: float
    config: Dict[str, Any]
    
    class Config:
        from_attributes = True


class BatchDiagnosisRequest(BaseModel):
    """批量诊断请求"""
    experiment_id: int
    student_ids: List[str]
    
    class Config:
        from_attributes = True


class BatchDiagnosisResponse(BaseModel):
    """批量诊断响应"""
    experiment_id: int
    total_students: int
    successful_diagnoses: int
    diagnoses: List[Dict[str, Any]]
    
    class Config:
        from_attributes = True


class LearningProgress(BaseModel):
    """学习进度模型"""
    student_id: str
    progress_percentage: float
    completed_activities: int
    total_activities: int
    average_performance: float
    estimated_remaining_minutes: float
    learning_velocity: float
    recommendations: List[str]
    
    class Config:
        from_attributes = True


class ProgressUpdateRequest(BaseModel):
    """进度更新请求"""
    student_id: str
    activity_id: str
    score: float
    time_spent: int
    completed: bool = True
    
    class Config:
        from_attributes = True


class DataPreprocessingInfo(BaseModel):
    """数据预处理信息"""
    dataset_id: int
    dataset_name: str
    student_count: int
    item_count: int
    interaction_count: int
    q_matrix_shape: tuple
    response_matrix_shape: tuple
    knowledge_components: List[Dict[str, Any]]
    
    class Config:
        from_attributes = True
