#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强化学习智能体演示

展示Q-Learning、DQN和PPO算法在学习路径规划中的应用。
"""

import sys
import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Any
import time

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from environments.learning_env import LearningEnvironment
from agents.rule_based import RandomAgent, GreedyAgent, SmartGreedyAgent
from agents.rl_agents import QLearningAgent, DQNAgent, PPOAgent
from evaluation.evaluator import Evaluator


def train_q_learning_demo():
    """Q-Learning训练演示"""
    print("🧠 Q-Learning智能体训练演示")
    print("=" * 50)
    
    try:
        # 创建环境
        env = LearningEnvironment(
            num_knowledge_points=4,
            learning_rate=0.3,
            max_steps=20
        )
        
        # 创建Q-Learning智能体
        agent = QLearningAgent(
            action_space_size=env.action_space_size,
            learning_rate=0.1,
            epsilon=0.3,
            epsilon_decay=0.995
        )
        
        print(f"📚 环境: {env.num_kps}个知识点")
        print(f"🤖 智能体: {agent.name}")
        print(f"⚙️  参数: lr={agent.learning_rate}, ε={agent.epsilon:.3f}")
        
        # 训练过程
        training_rewards = []
        training_episodes = 100
        
        print(f"\n🏋️ 开始训练 {training_episodes} 回合...")
        
        for episode in range(training_episodes):
            observation = env.reset()
            agent.reset()
            
            episode_reward = 0
            step_count = 0
            
            while True:
                action = agent.get_action(observation)
                next_observation, reward, done, info = env.step(action)
                agent.update(observation, action, reward, next_observation, done)
                
                episode_reward += reward
                step_count += 1
                observation = next_observation
                
                if done:
                    break
            
            training_rewards.append(episode_reward)
            
            # 显示进度
            if (episode + 1) % 20 == 0:
                avg_reward = np.mean(training_rewards[-20:])
                print(f"回合 {episode + 1}: 平均奖励={avg_reward:.3f}, ε={agent.epsilon:.3f}")
        
        # 训练后评估
        print(f"\n📊 训练后评估:")
        evaluator = Evaluator(save_detailed_logs=False)
        result = evaluator.evaluate(agent, env, num_episodes=20)
        
        metrics = result['metrics']
        print(f"  平均奖励: {metrics['AverageReward']:.3f}")
        print(f"  最终得分: {metrics['LearningEffectiveness']:.3f}")
        print(f"  成功率: {metrics['SuccessRate']:.1%}")
        print(f"  Q表大小: {len(agent.q_table)}")
        
        return {
            'agent': agent,
            'training_rewards': training_rewards,
            'evaluation_result': result
        }
        
    except Exception as e:
        print(f"❌ Q-Learning演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def train_dqn_demo():
    """DQN训练演示"""
    print(f"\n🧠 DQN智能体训练演示")
    print("=" * 50)
    
    try:
        # 检查PyTorch是否可用
        if not torch.cuda.is_available():
            print("⚠️  CUDA不可用，使用CPU训练（速度较慢）")
        
        # 创建环境
        env = LearningEnvironment(
            num_knowledge_points=4,
            learning_rate=0.3,
            max_steps=20
        )
        
        # 创建DQN智能体
        agent = DQNAgent(
            action_space_size=env.action_space_size,
            state_size=env.num_kps,
            learning_rate=0.001,
            epsilon=0.3,
            memory_size=5000,
            batch_size=32
        )
        
        print(f"📚 环境: {env.num_kps}个知识点")
        print(f"🤖 智能体: {agent.name}")
        print(f"💻 设备: {agent.device}")
        print(f"⚙️  参数: lr={agent.learning_rate}, ε={agent.epsilon:.3f}")
        
        # 训练过程
        training_rewards = []
        training_episodes = 200
        
        print(f"\n🏋️ 开始训练 {training_episodes} 回合...")
        
        for episode in range(training_episodes):
            observation = env.reset()
            agent.reset()
            
            episode_reward = 0
            
            while True:
                action = agent.get_action(observation)
                next_observation, reward, done, info = env.step(action)
                agent.update(observation, action, reward, next_observation, done)
                
                episode_reward += reward
                observation = next_observation
                
                if done:
                    break
            
            training_rewards.append(episode_reward)
            
            # 显示进度
            if (episode + 1) % 40 == 0:
                avg_reward = np.mean(training_rewards[-40:])
                print(f"回合 {episode + 1}: 平均奖励={avg_reward:.3f}, ε={agent.epsilon:.3f}, 经验={len(agent.memory)}")
        
        # 训练后评估
        print(f"\n📊 训练后评估:")
        evaluator = Evaluator(save_detailed_logs=False)
        result = evaluator.evaluate(agent, env, num_episodes=20)
        
        metrics = result['metrics']
        print(f"  平均奖励: {metrics['AverageReward']:.3f}")
        print(f"  最终得分: {metrics['LearningEffectiveness']:.3f}")
        print(f"  成功率: {metrics['SuccessRate']:.1%}")
        print(f"  经验池大小: {len(agent.memory)}")
        
        return {
            'agent': agent,
            'training_rewards': training_rewards,
            'evaluation_result': result
        }
        
    except Exception as e:
        print(f"❌ DQN演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def train_ppo_demo():
    """PPO训练演示"""
    print(f"\n🧠 PPO智能体训练演示")
    print("=" * 50)
    
    try:
        # 创建环境
        env = LearningEnvironment(
            num_knowledge_points=4,
            learning_rate=0.3,
            max_steps=20
        )
        
        # 创建PPO智能体
        agent = PPOAgent(
            action_space_size=env.action_space_size,
            state_size=env.num_kps,
            learning_rate=3e-4,
            buffer_size=1024,
            batch_size=64,
            ppo_epochs=4
        )
        
        print(f"📚 环境: {env.num_kps}个知识点")
        print(f"🤖 智能体: {agent.name}")
        print(f"💻 设备: {agent.device}")
        print(f"⚙️  参数: lr={agent.learning_rate}, buffer_size={agent.buffer_size}")
        
        # 训练过程
        training_rewards = []
        training_episodes = 300
        
        print(f"\n🏋️ 开始训练 {training_episodes} 回合...")
        
        for episode in range(training_episodes):
            observation = env.reset()
            agent.reset()
            
            episode_reward = 0
            
            while True:
                action = agent.get_action(observation)
                next_observation, reward, done, info = env.step(action)
                agent.update(observation, action, reward, next_observation, done)
                
                episode_reward += reward
                observation = next_observation
                
                if done:
                    break
            
            training_rewards.append(episode_reward)
            
            # 显示进度
            if (episode + 1) % 50 == 0:
                avg_reward = np.mean(training_rewards[-50:])
                print(f"回合 {episode + 1}: 平均奖励={avg_reward:.3f}, 缓冲区={len(agent.buffer)}")
        
        # 训练后评估
        print(f"\n📊 训练后评估:")
        evaluator = Evaluator(save_detailed_logs=False)
        result = evaluator.evaluate(agent, env, num_episodes=20)
        
        metrics = result['metrics']
        print(f"  平均奖励: {metrics['AverageReward']:.3f}")
        print(f"  最终得分: {metrics['LearningEffectiveness']:.3f}")
        print(f"  成功率: {metrics['SuccessRate']:.1%}")
        
        # 显示训练统计
        stats = agent.get_training_stats()
        if stats['total_loss']:
            print(f"  平均总损失: {np.mean(stats['total_loss'][-10:]):.4f}")
            print(f"  平均策略损失: {np.mean(stats['policy_loss'][-10:]):.4f}")
            print(f"  平均价值损失: {np.mean(stats['value_loss'][-10:]):.4f}")
        
        return {
            'agent': agent,
            'training_rewards': training_rewards,
            'evaluation_result': result,
            'training_stats': stats
        }
        
    except Exception as e:
        print(f"❌ PPO演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def compare_rl_agents():
    """比较强化学习智能体"""
    print(f"\n🏁 强化学习智能体比较")
    print("=" * 50)
    
    try:
        # 创建环境
        env = LearningEnvironment(
            num_knowledge_points=4,
            learning_rate=0.3,
            max_steps=20
        )
        
        # 创建智能体（使用较小的参数以加快演示）
        agents = []
        
        # 基线智能体
        agents.append(RandomAgent(env.action_space_size))
        agents.append(GreedyAgent(env.action_space_size))
        agents.append(SmartGreedyAgent(env.action_space_size, env.dependency_matrix))
        
        # 强化学习智能体（预训练）
        print("🏋️ 预训练强化学习智能体...")
        
        # Q-Learning
        q_agent = QLearningAgent(env.action_space_size, epsilon=0.1)
        for _ in range(50):  # 简单预训练
            obs = env.reset()
            while True:
                action = q_agent.get_action(obs)
                next_obs, reward, done, _ = env.step(action)
                q_agent.update(obs, action, reward, next_obs, done)
                obs = next_obs
                if done:
                    break
        agents.append(q_agent)
        
        print(f"🤖 比较 {len(agents)} 个智能体:")
        for agent in agents:
            print(f"  - {agent.name}")
        
        # 评估比较
        evaluator = Evaluator(save_detailed_logs=False)
        results = []
        
        for agent in agents:
            print(f"\n评估 {agent.name}...")
            result = evaluator.evaluate(agent, env, num_episodes=30)
            results.append(result)
        
        # 显示比较结果
        print(f"\n🏆 比较结果:")
        print(f"{'智能体':<20} {'平均奖励':<10} {'最终得分':<10} {'成功率':<8} {'平均步数':<8}")
        print("-" * 70)
        
        for result in results:
            name = result['agent_name']
            metrics = result['metrics']
            print(f"{name:<20} {metrics['AverageReward']:<10.3f} {metrics['LearningEffectiveness']:<10.3f} "
                  f"{metrics['SuccessRate']:<8.1%} {metrics['AverageSteps']:<8.1f}")
        
        return results
        
    except Exception as e:
        print(f"❌ 智能体比较失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主演示函数"""
    print("🚀 强化学习智能体演示")
    print("=" * 80)
    
    # 设置随机种子
    np.random.seed(42)
    torch.manual_seed(42)
    
    results = {}
    
    try:
        # 1. Q-Learning演示
        q_result = train_q_learning_demo()
        if q_result:
            results['q_learning'] = q_result
        
        # 2. DQN演示
        dqn_result = train_dqn_demo()
        if dqn_result:
            results['dqn'] = dqn_result
        
        # 3. PPO演示
        ppo_result = train_ppo_demo()
        if ppo_result:
            results['ppo'] = ppo_result
        
        # 4. 智能体比较
        comparison_results = compare_rl_agents()
        if comparison_results:
            results['comparison'] = comparison_results
        
        # 总结
        print(f"\n" + "=" * 80)
        print(f"🎉 强化学习演示完成！")
        
        print(f"\n💡 关键发现:")
        print(f"1. Q-Learning适合简单的离散状态空间")
        print(f"2. DQN能处理连续状态，但需要更多训练数据")
        print(f"3. PPO是最先进的策略梯度方法，通常表现最好")
        print(f"4. 强化学习智能体需要充分训练才能超越基于规则的方法")
        
        print(f"\n🔧 优化建议:")
        print(f"1. 增加训练回合数以获得更好的性能")
        print(f"2. 调整超参数（学习率、探索率等）")
        print(f"3. 使用更复杂的网络架构")
        print(f"4. 实现经验回放和目标网络等技巧")
        
        return results
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    results = main()
    print(f"\n{'='*80}")
    if results:
        print(f"✅ 强化学习演示成功完成！")
        print(f"📊 共完成 {len(results)} 个演示模块")
    else:
        print(f"❌ 演示过程中出现错误。")
    print(f"{'='*80}")
