"""
学习路径推荐API路由 - FastAPI版本
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, List, Any, Optional
import logging
import traceback
import sys
import os

# 添加路径以导入服务
current_dir = os.path.dirname(__file__)
parent_dir = os.path.join(current_dir, '..')
sys.path.insert(0, parent_dir)

try:
    from services.afm_service import afm_service
    from services.data_adapter import diagnosis_adapter
    SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import services: {e}")
    SERVICES_AVAILABLE = False
    # 创建模拟服务
    class MockService:
        def get_model_info(self):
            return {'model_loaded': False, 'embeddings_loaded': False}
        def recommend_learning_path(self, *args, **kwargs):
            raise HTTPException(status_code=503, detail="AFM服务不可用")

    class MockAdapter:
        def convert_diagnosis_to_user_profile(self, *args, **kwargs):
            return {}
        def generate_learning_sequence(self, *args, **kwargs):
            return []

    afm_service = MockService()
    diagnosis_adapter = MockAdapter()

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/learning-path", tags=["learning-path"])

# Pydantic模型定义
class KnowledgeDiagnosis(BaseModel):
    mastery_probability: float
    mastery_level: str

class DiagnosisData(BaseModel):
    student_id: str
    overall_ability: float
    knowledge_diagnosis: Dict[str, KnowledgeDiagnosis]

class Preferences(BaseModel):
    max_items: Optional[int] = 10
    difficulty_preference: Optional[str] = "medium"
    time_constraint: Optional[int] = 120

class RecommendationRequest(BaseModel):
    user_id: str
    diagnosis_data: DiagnosisData
    preferences: Optional[Preferences] = None

class ModelLoadRequest(BaseModel):
    model_path: Optional[str] = "models/afm_model.pth"
    embeddings_path: Optional[str] = "models/knowledge_embeddings.pkl"

@router.post("/recommend")
async def recommend_learning_path(request: RecommendationRequest):
    """
    为用户推荐学习路径
    """
    try:
        user_id = request.user_id
        diagnosis_data = request.diagnosis_data.dict()
        preferences = request.preferences.dict() if request.preferences else {}
        
        # 检查服务状态
        model_info = afm_service.get_model_info()
        if not model_info['model_loaded']:
            raise HTTPException(
                status_code=503,
                detail='AFM模型未加载，请联系管理员'
            )

        # 获取推荐参数
        max_items = preferences.get('max_items', 10)

        # 生成推荐
        learning_path = afm_service.recommend_learning_path(
            user_id=user_id,
            diagnosis_data=diagnosis_data,
            top_k=max_items
        )

        # 转换用户画像
        user_profile = diagnosis_adapter.convert_diagnosis_to_user_profile(diagnosis_data)

        # 处理推荐结果
        recommendations = []
        for rec in learning_path.recommended_sequence:
            rec_dict = {
                'knowledge_point_id': rec.knowledge_point_id,
                'confidence_score': rec.confidence_score,
                'difficulty_level': rec.difficulty_level,
                'estimated_duration': rec.estimated_duration
            }
            recommendations.append(rec_dict)

        # 生成学习序列
        learning_sequence = diagnosis_adapter.generate_learning_sequence(
            recommendations, user_profile
        )

        # 构造响应
        response_data = {
            'success': True,
            'data': {
                'user_id': user_id,
                'learning_path': {
                    'total_duration': learning_path.total_duration,
                    'overall_difficulty': learning_path.difficulty,
                    'sequence': learning_sequence,
                    'created_at': learning_path.created_at
                },
                'user_profile': {
                    'overall_ability': user_profile.overall_ability,
                    'learning_style': user_profile.learning_style,
                    'preferred_difficulty': user_profile.preferred_difficulty
                },
                'metadata': {
                    'total_recommendations': len(learning_sequence),
                    'avg_confidence': sum(r['confidence_score'] for r in recommendations) / len(recommendations) if recommendations else 0,
                    'model_info': model_info
                }
            }
        }

        logger.info(f"Generated learning path for user {user_id} with {len(learning_sequence)} recommendations")
        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in recommend_learning_path: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f'服务器内部错误: {str(e)}'
        )

@router.get("/knowledge-point/{knowledge_id}")
async def get_knowledge_point_info(knowledge_id: int):
    """获取知识点详细信息"""
    try:
        knowledge_info = diagnosis_adapter.get_knowledge_point_info(knowledge_id)

        return {
            'success': True,
            'data': knowledge_info
        }

    except Exception as e:
        logger.error(f"Error getting knowledge point info: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'获取知识点信息失败: {str(e)}'
        )

@router.get("/model/status")
async def get_model_status():
    """获取AFM模型状态"""
    try:
        model_info = afm_service.get_model_info()

        return {
            'success': True,
            'data': model_info
        }

    except Exception as e:
        logger.error(f"Error getting model status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'获取模型状态失败: {str(e)}'
        )

@router.post("/model/load")
async def load_model(request: ModelLoadRequest):
    """加载AFM模型"""
    try:
        # 加载模型
        afm_service.load_model(request.model_path)
        afm_service.load_knowledge_embeddings(request.embeddings_path)

        model_info = afm_service.get_model_info()

        return {
            'success': True,
            'message': '模型加载成功',
            'data': model_info
        }

    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'模型加载失败: {str(e)}'
        )

@router.post("/simulate")
async def simulate_recommendation(user_id: str = "test_user"):
    """模拟推荐（用于测试）"""
    try:
        
        # 模拟诊断数据
        mock_diagnosis = {
            'student_id': user_id,
            'overall_ability': 0.65,
            'knowledge_diagnosis': {
                '代数运算': {'mastery_probability': 0.8, 'mastery_level': '掌握'},
                '几何推理': {'mastery_probability': 0.4, 'mastery_level': '未掌握'},
                '函数理解': {'mastery_probability': 0.6, 'mastery_level': '部分掌握'},
                '概率统计': {'mastery_probability': 0.3, 'mastery_level': '未掌握'},
                '逻辑推理': {'mastery_probability': 0.5, 'mastery_level': '部分掌握'}
            }
        }
        
        # 模拟推荐结果
        mock_recommendations = [
            {
                'knowledge_point_id': '1',
                'knowledge_point_name': '几何推理',
                'confidence_score': 0.75,
                'difficulty_level': 'medium',
                'estimated_duration': 25,
                'category': '空间思维',
                'prerequisites_met': True,
                'sequence_order': 1,
                'learning_suggestion': '这个知识点比较适合您当前水平，建议重点学习'
            },
            {
                'knowledge_point_id': '3',
                'knowledge_point_name': '概率统计', 
                'confidence_score': 0.68,
                'difficulty_level': 'medium',
                'estimated_duration': 30,
                'category': '数据分析',
                'prerequisites_met': True,
                'sequence_order': 2,
                'learning_suggestion': '这个知识点比较适合您当前水平，建议重点学习'
            },
            {
                'knowledge_point_id': '4',
                'knowledge_point_name': '逻辑推理',
                'confidence_score': 0.62,
                'difficulty_level': 'hard',
                'estimated_duration': 35,
                'category': '逻辑思维',
                'prerequisites_met': False,
                'sequence_order': 3,
                'learning_suggestion': '这个知识点有一定挑战性，建议分步骤深入学习'
            }
        ]
        
        response_data = {
            'success': True,
            'data': {
                'user_id': user_id,
                'learning_path': {
                    'total_duration': 90,
                    'overall_difficulty': 'medium',
                    'sequence': mock_recommendations,
                    'created_at': '2025-01-26T12:00:00'
                },
                'user_profile': {
                    'overall_ability': 0.65,
                    'learning_style': 'intermediate',
                    'preferred_difficulty': 'medium'
                },
                'metadata': {
                    'total_recommendations': len(mock_recommendations),
                    'avg_confidence': 0.68,
                    'model_info': {
                        'model_loaded': False,
                        'embeddings_loaded': False,
                        'mode': 'simulation'
                    }
                }
            }
        }
        
        return response_data

    except Exception as e:
        logger.error(f"Error in simulate_recommendation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f'模拟推荐失败: {str(e)}'
        )

@router.get("/health")
async def health_check():
    """健康检查"""
    import datetime
    return {
        'success': True,
        'message': 'Learning Path Service is running',
        'timestamp': datetime.datetime.now().isoformat()
    }
