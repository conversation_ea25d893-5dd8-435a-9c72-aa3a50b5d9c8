# 🚀 EduBrain 快速启动指南

## 📋 环境要求

- **Python**: 3.8 或更高版本
- **Node.js**: 16.0 或更高版本
- **npm**: 8.0 或更高版本

## ⚡ 一键启动

### Windows 用户
```bash
# 双击运行或在命令行执行
start_dev.bat
```

### Linux/macOS 用户
```bash
# 给脚本执行权限（首次运行）
chmod +x start_dev.sh

# 运行启动脚本
./start_dev.sh
```

## 🔧 手动启动

如果一键启动脚本不工作，可以手动启动：

### 1. 启动后端

```bash
# 进入后端目录
cd backend

# 安装 Python 依赖
pip install -r requirements.txt

# 启动后端服务
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 启动前端

```bash
# 新开一个终端，进入前端目录
cd frontend

# 安装 Node.js 依赖
npm install

# 启动前端开发服务器
npm start
```

## 📊 访问服务

启动成功后，可以访问：

- **前端应用**: http://localhost:3000
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs

## 🛑 停止服务

- **一键启动的服务**: 在各自的命令行窗口中按 `Ctrl+C`
- **手动启动的服务**: 在对应终端中按 `Ctrl+C`

## ❓ 常见问题

### 端口被占用
```bash
# Windows 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 杀死进程
taskkill /PID <进程ID> /F
```

### 依赖安装失败
```bash
# 清理 npm 缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf frontend/node_modules
cd frontend && npm install

# Python 依赖问题
pip install --upgrade pip
pip install -r backend/requirements.txt
```

### 数据库问题
```bash
# 检查数据库文件是否存在
ls backend/*.db

# 如果数据库文件损坏，可以删除重新创建
# 注意：这会丢失所有数据
rm backend/*.db
```

## 📚 更多信息

- 详细文档: [README.md](README.md)
- 开发指南: [DEVELOPMENT.md](DEVELOPMENT.md)
- 项目结构: [PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)
