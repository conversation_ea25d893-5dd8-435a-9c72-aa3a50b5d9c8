import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Statistic,
  Typography,
  Select,
  Input,
  Tabs,
  Alert,
  Tooltip,
} from 'antd';
import {
  BulbOutlined,
  EyeOutlined,
  ReloadOutlined,
  SearchOutlined,
  FilterOutlined,
  UserOutlined,
  ExperimentOutlined,
  RobotOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import { experimentService, cognitiveService } from '../../services/api';
import CognitiveDiagnosisModal from '../../components/CognitiveDiagnosisModal';
import DiagnosisReport from '../../components/DiagnosisReport';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;
const { TabPane } = Tabs;

interface Experiment {
  id: number;
  name: string;
  model_type: string;
  dataset_name: string;
  status: string;
  created_at: string;
  completed_at?: string;
  metrics?: any;
}

interface DiagnosisRecord {
  id: string;
  student_id: string;
  experiment_id: number;
  experiment_name: string;
  model_type: string;
  diagnosis_time: string;
  overall_ability: number;
  diagnosis_type: 'basic' | 'llm';
  has_report: boolean;
}

const CognitiveDiagnosisCenter: React.FC = () => {
  const [experiments, setExperiments] = useState<Experiment[]>([]);
  const [diagnosisRecords, setDiagnosisRecords] = useState<DiagnosisRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedExperiment, setSelectedExperiment] = useState<Experiment | null>(null);
  const [diagnosisModalVisible, setDiagnosisModalVisible] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);
  const [selectedDiagnosis, setSelectedDiagnosis] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('experiments');
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // 加载实验列表
      const experimentsResponse = await experimentService.getExperiments();
      const completedExperiments = experimentsResponse.data.filter(
        (exp: Experiment) => exp.status === 'completed'
      );
      setExperiments(completedExperiments);

      // 加载真实的诊断记录
      await fetchDiagnosisRecords();
    } catch (error) {
      message.error('加载数据失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDiagnosisRecords = async () => {
    try {
      const response = await cognitiveService.getDiagnosisRecords({
        limit: 100
      });

      if (response.data && response.data.records) {
        // 转换数据格式以匹配前端接口
        const records = response.data.records.map((record: any) => ({
          id: record.id.toString(),
          student_id: record.student_id,
          experiment_id: record.experiment_id,
          experiment_name: `实验_${record.experiment_id}`, // 可以后续优化为获取实验名称
          model_type: 'ORCDF', // 可以从实验信息中获取
          diagnosis_time: record.diagnosis_time || record.created_at,
          overall_ability: record.overall_ability,
          diagnosis_type: record.diagnosis_type,
          has_report: !!record.llm_report,
        }));
        setDiagnosisRecords(records);
      } else {
        setDiagnosisRecords([]);
      }
    } catch (error) {
      console.error('获取诊断记录失败:', error);
      // 如果API调用失败，使用空数组
      setDiagnosisRecords([]);
    }
  };

  const handleStartDiagnosis = (experiment: Experiment) => {
    setSelectedExperiment(experiment);
    setDiagnosisModalVisible(true);
  };

  const handleDiagnosisComplete = () => {
    // 诊断完成后刷新诊断记录列表
    fetchDiagnosisRecords();
  };

  const handleViewReport = async (record: DiagnosisRecord) => {
    try {
      // 调用API获取完整的诊断记录详情
      const response = await cognitiveService.getDiagnosisRecord(parseInt(record.id));

      if (response.data) {
        const diagnosisData = {
          student_id: response.data.student_id,
          overall_ability: response.data.overall_ability,
          knowledge_diagnosis: response.data.knowledge_diagnosis || {},
          llm_report: response.data.llm_report,
          diagnosis_time: response.data.diagnosis_time,
          report_generated: !!response.data.llm_report,
        };

        setSelectedDiagnosis(diagnosisData);
        setReportModalVisible(true);
      } else {
        message.error('诊断记录不存在');
      }
    } catch (error) {
      console.error('加载诊断报告失败:', error);
      message.error('加载诊断报告失败');
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      completed: { color: 'success', text: '已完成' },
      running: { color: 'processing', text: '运行中' },
      failed: { color: 'error', text: '失败' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getDiagnosisTypeTag = (type: 'basic' | 'llm') => {
    return type === 'llm' ? (
      <Tag color="blue" icon={<RobotOutlined />}>AI诊断</Tag>
    ) : (
      <Tag color="default" icon={<ExperimentOutlined />}>基础诊断</Tag>
    );
  };

  // 实验列表表格列定义
  const experimentColumns: ColumnsType<Experiment> = [
    {
      title: '实验名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Experiment) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.model_type.toUpperCase()} • {record.dataset_name}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: getStatusTag,
    },
    {
      title: '准确率',
      key: 'accuracy',
      render: (_, record: Experiment) => (
        <span>{record.metrics?.accuracy ? `${(record.metrics.accuracy * 100).toFixed(1)}%` : 'N/A'}</span>
      ),
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      render: (time: string) => time ? new Date(time).toLocaleString() : 'N/A',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Experiment) => (
        <Button
          type="primary"
          icon={<BulbOutlined />}
          onClick={() => handleStartDiagnosis(record)}
        >
          开始诊断
        </Button>
      ),
    },
  ];

  // 诊断记录表格列定义
  const diagnosisColumns: ColumnsType<DiagnosisRecord> = [
    {
      title: '学生ID',
      dataIndex: 'student_id',
      key: 'student_id',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '实验名称',
      dataIndex: 'experiment_name',
      key: 'experiment_name',
      render: (text: string, record: DiagnosisRecord) => (
        <div>
          <div>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.model_type}
          </div>
        </div>
      ),
    },
    {
      title: '诊断类型',
      dataIndex: 'diagnosis_type',
      key: 'diagnosis_type',
      render: getDiagnosisTypeTag,
    },
    {
      title: '整体能力',
      dataIndex: 'overall_ability',
      key: 'overall_ability',
      render: (value: number) => (
        <span style={{ fontWeight: 'bold', color: value > 0.7 ? '#52c41a' : value > 0.5 ? '#faad14' : '#ff4d4f' }}>
          {(value * 100).toFixed(1)}%
        </span>
      ),
    },
    {
      title: '诊断时间',
      dataIndex: 'diagnosis_time',
      key: 'diagnosis_time',
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: DiagnosisRecord) => (
        <Space>
          <Tooltip title="查看详细报告">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewReport(record)}
            >
              查看报告
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 过滤实验数据
  const filteredExperiments = experiments.filter(exp => {
    const matchesSearch = exp.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         exp.model_type.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || exp.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div>
      <Card
        title={
          <Space>
            <BulbOutlined style={{ color: '#1890ff' }} />
            <span>诊断与规划中心</span>
          </Space>
        }
        extra={
          <Button
            icon={<ReloadOutlined />}
            onClick={loadData}
            loading={loading}
          >
            刷新
          </Button>
        }
      >
        {/* 统计概览 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Statistic
              title="可用实验"
              value={experiments.length}
              prefix={<ExperimentOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="诊断记录"
              value={diagnosisRecords.length}
              prefix={<HistoryOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="AI诊断"
              value={diagnosisRecords.filter(r => r.diagnosis_type === 'llm').length}
              prefix={<RobotOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="平均能力"
              value={diagnosisRecords.length > 0 ? 
                (diagnosisRecords.reduce((sum, r) => sum + r.overall_ability, 0) / diagnosisRecords.length * 100).toFixed(1) : 0}
              suffix="%"
              prefix={<UserOutlined />}
            />
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="可用实验" key="experiments">
            {/* 搜索和过滤 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Search
                  placeholder="搜索实验名称或模型类型"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  prefix={<SearchOutlined />}
                />
              </Col>
              <Col span={6}>
                <Select
                  value={statusFilter}
                  onChange={setStatusFilter}
                  style={{ width: '100%' }}
                  prefix={<FilterOutlined />}
                >
                  <Option value="all">全部状态</Option>
                  <Option value="completed">已完成</Option>
                  <Option value="running">运行中</Option>
                  <Option value="failed">失败</Option>
                </Select>
              </Col>
            </Row>

            <Table
              columns={experimentColumns}
              dataSource={filteredExperiments}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个实验`,
              }}
            />
          </TabPane>

          <TabPane tab="诊断记录" key="records">
            <Table
              columns={diagnosisColumns}
              dataSource={diagnosisRecords}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 认知诊断模态框 */}
      <Modal
        title={`智能诊断 - ${selectedExperiment?.name}`}
        open={diagnosisModalVisible}
        onCancel={() => setDiagnosisModalVisible(false)}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedExperiment && (
          <CognitiveDiagnosisModal
            experimentId={selectedExperiment.id}
            onClose={() => setDiagnosisModalVisible(false)}
            onDiagnosisComplete={handleDiagnosisComplete}
          />
        )}
      </Modal>

      {/* 诊断报告模态框 */}
      <Modal
        title="诊断报告详情"
        open={reportModalVisible}
        onCancel={() => setReportModalVisible(false)}
        footer={null}
        width={1000}
        destroyOnClose
      >
        {selectedDiagnosis && (
          <DiagnosisReport
            diagnosisData={selectedDiagnosis}
            onRefresh={() => {}}
          />
        )}
      </Modal>
    </div>
  );
};

export default CognitiveDiagnosisCenter;
