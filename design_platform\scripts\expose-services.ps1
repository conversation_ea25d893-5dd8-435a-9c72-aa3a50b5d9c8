# 内网穿透脚本 - 暴露前端和后端服务
# 使用方法: .\expose-services.ps1 [ngrok|cpolar]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("ngrok", "cpolar", "frp")]
    [string]$Tool = "ngrok"
)

Write-Host "🚀 启动内网穿透服务..." -ForegroundColor Green
Write-Host "工具: $Tool" -ForegroundColor Yellow

# 检查服务是否运行
function Test-ServiceRunning {
    param($Port)
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 5 -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# 检查前端和后端服务
Write-Host "📡 检查服务状态..." -ForegroundColor Blue

$frontendRunning = Test-ServiceRunning -Port 3000
$backendRunning = Test-ServiceRunning -Port 8000

if (-not $frontendRunning) {
    Write-Host "❌ 前端服务未运行 (端口3000)" -ForegroundColor Red
    Write-Host "请先启动前端: npm start" -ForegroundColor Yellow
}

if (-not $backendRunning) {
    Write-Host "❌ 后端服务未运行 (端口8000)" -ForegroundColor Red
    Write-Host "请先启动后端: uvicorn app.main:app --reload" -ForegroundColor Yellow
}

if (-not $frontendRunning -or -not $backendRunning) {
    Write-Host "⚠️  请先启动所有服务后再运行此脚本" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 前端服务运行正常 (端口3000)" -ForegroundColor Green
Write-Host "✅ 后端服务运行正常 (端口8000)" -ForegroundColor Green

# 根据选择的工具启动内网穿透
switch ($Tool) {
    "ngrok" {
        Write-Host "🌐 使用 ngrok 启动内网穿透..." -ForegroundColor Cyan
        Write-Host "请确保已安装 ngrok 并配置了 authtoken" -ForegroundColor Yellow
        Write-Host "访问 https://ngrok.com/ 注册并获取 authtoken" -ForegroundColor Yellow
        Write-Host ""
        
        # 启动前端穿透
        Write-Host "启动前端穿透 (端口3000)..." -ForegroundColor Blue
        Start-Process -FilePath "ngrok" -ArgumentList "http", "3000", "--log=stdout" -WindowStyle Normal
        
        Start-Sleep -Seconds 3
        
        # 启动后端穿透
        Write-Host "启动后端穿透 (端口8000)..." -ForegroundColor Blue
        Start-Process -FilePath "ngrok" -ArgumentList "http", "8000", "--log=stdout" -WindowStyle Normal
        
        Write-Host ""
        Write-Host "🎉 ngrok 隧道已启动!" -ForegroundColor Green
        Write-Host "请查看 ngrok 窗口获取公网地址" -ForegroundColor Yellow
    }
    
    "cpolar" {
        Write-Host "🌐 使用 cpolar 启动内网穿透..." -ForegroundColor Cyan
        Write-Host "请确保已安装 cpolar 并登录账号" -ForegroundColor Yellow
        Write-Host ""
        
        # 启动前端穿透
        Write-Host "启动前端穿透 (端口3000)..." -ForegroundColor Blue
        Start-Process -FilePath "cpolar" -ArgumentList "http", "3000" -WindowStyle Normal
        
        Start-Sleep -Seconds 3
        
        # 启动后端穿透
        Write-Host "启动后端穿透 (端口8000)..." -ForegroundColor Blue
        Start-Process -FilePath "cpolar" -ArgumentList "http", "8000" -WindowStyle Normal
        
        Write-Host ""
        Write-Host "🎉 cpolar 隧道已启动!" -ForegroundColor Green
        Write-Host "访问 https://dashboard.cpolar.com/ 查看公网地址" -ForegroundColor Yellow
    }
    
    "frp" {
        Write-Host "🌐 使用 frp 启动内网穿透..." -ForegroundColor Cyan
        Write-Host "请确保已配置 frp 客户端" -ForegroundColor Yellow
        Write-Host "需要自己的服务器或使用免费的 frp 服务" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📋 使用说明:" -ForegroundColor Magenta
Write-Host "1. 获取公网地址后，需要更新前端的API地址" -ForegroundColor White
Write-Host "2. 将后端的公网地址配置到前端项目中" -ForegroundColor White
Write-Host "3. 分享前端的公网地址给组员访问" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  注意事项:" -ForegroundColor Red
Write-Host "- 免费版本通常有流量限制" -ForegroundColor White
Write-Host "- 公网地址可能会变化，需要及时更新" -ForegroundColor White
Write-Host "- 确保防火墙允许相关端口访问" -ForegroundColor White
