#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的演示脚本

直接运行模拟器的基本功能演示。
"""

import sys
import os
import numpy as np

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simple_demo():
    """简单演示"""
    print("🎯 学习路径规划模拟器简单演示")
    print("=" * 50)
    
    try:
        # 导入必要模块
        from environments.learning_env import LearningEnvironment
        from agents.rule_based import RandomAgent, GreedyAgent, SmartGreedyAgent
        from evaluation.evaluator import Evaluator
        
        print("✅ 模块导入成功")
        
        # 1. 创建环境
        env = LearningEnvironment(
            num_knowledge_points=4,
            learning_rate=0.3,
            success_threshold=0.6,
            min_success_kps=2,
            max_steps=20
        )
        print(f"📚 环境创建成功: {env.num_kps}个知识点")
        
        # 2. 创建智能体
        agents = [
            RandomAgent(env.action_space_size),
            GreedyAgent(env.action_space_size),
            SmartGreedyAgent(env.action_space_size, env.dependency_matrix)
        ]
        print(f"🤖 创建了{len(agents)}个智能体")
        
        # 3. 单回合演示
        print(f"\n🎮 单回合演示:")
        agent = agents[1]  # 使用贪心智能体
        observation = env.reset()
        agent.reset()
        
        print(f"初始状态: {[f'{x:.3f}' for x in observation]}")
        
        total_reward = 0
        for step in range(10):
            action = agent.get_action(observation)
            next_observation, reward, done, info = env.step(action)
            agent.update(observation, action, reward, next_observation, done)
            
            total_reward += reward
            print(f"步骤{step+1}: 动作={action}, 奖励={reward:.3f}, 新状态={[f'{x:.3f}' for x in next_observation]}")
            
            observation = next_observation
            if done:
                print(f"✅ 任务完成！")
                break
        
        print(f"总奖励: {total_reward:.3f}")
        
        # 4. 简单评估
        print(f"\n📊 简单评估:")
        evaluator = Evaluator(save_detailed_logs=False)
        
        for agent in agents:
            print(f"\n评估智能体: {agent.name}")
            result = evaluator.evaluate(agent, env, num_episodes=10)
            
            metrics = result['metrics']
            print(f"  平均奖励: {metrics['AverageReward']:.3f}")
            print(f"  最终得分: {metrics['LearningEffectiveness']:.3f}")
            print(f"  成功率: {metrics['SuccessRate']:.1%}")
            print(f"  平均步数: {metrics['AverageSteps']:.1f}")
        
        print(f"\n🎉 演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def cognitive_demo():
    """认知模型演示"""
    print(f"\n🧠 认知模型演示")
    print("=" * 50)
    
    try:
        from core.cognitive_models import (
            EbbinghausForgettingCurve, VygotskyZPD, SwellerCognitiveLoad,
            MetacognitionModel, SpacingEffectModel, FlowStateModel
        )
        
        # 1. 遗忘曲线
        forgetting_model = EbbinghausForgettingCurve(tau=24.0)
        print(f"📉 遗忘曲线模型:")
        for hours in [1, 6, 12, 24, 48]:
            retention = forgetting_model.apply(None, None, time_elapsed=hours)
            print(f"  {hours}小时后保持率: {retention:.3f}")
        
        # 2. ZPD模型
        zpd_model = VygotskyZPD(optimal_challenge=0.2, zpd_width=0.3)
        state = np.array([0.3, 0.5, 0.7, 0.9])
        print(f"\n🎯 ZPD模型 (当前状态: {[f'{x:.1f}' for x in state]}):")
        for difficulty in [0.2, 0.4, 0.6, 0.8]:
            zpd_effect = zpd_model.apply(state, 0, task_difficulty=difficulty)
            print(f"  难度{difficulty:.1f}的ZPD效应: {zpd_effect:.3f}")
        
        # 3. 认知负荷模型
        cognitive_load_model = SwellerCognitiveLoad(working_memory_capacity=7.0)
        print(f"\n🧮 认知负荷模型:")
        for complexity in [0.5, 1.0, 1.5, 2.0]:
            load_effect = cognitive_load_model.apply(
                state, 0, task_complexity=complexity, interface_complexity=0.3
            )
            print(f"  复杂度{complexity:.1f}的负荷效应: {load_effect:.3f}")
        
        # 4. 元认知模型
        metacognition_model = MetacognitionModel(monitoring_accuracy=0.8)
        print(f"\n🤔 元认知模型:")
        meta_result = metacognition_model.apply(state, 1)
        print(f"  感知掌握度: {meta_result['perceived_mastery']:.3f}")
        print(f"  掌握度差异: {meta_result['mastery_gap']:.3f}")
        print(f"  信心水平: {meta_result['confidence']:.3f}")
        
        # 5. 间隔效应模型
        spacing_model = SpacingEffectModel(spacing_factor=1.5)
        print(f"\n⏰ 间隔效应模型:")
        for interval in [0.5, 1, 2, 4, 8]:
            spacing_effect = spacing_model.apply(
                state, 0, last_study_time=0, current_time=interval
            )
            print(f"  间隔{interval}的效应: {spacing_effect:.3f}")
        
        # 6. 心流状态模型
        flow_model = FlowStateModel(flow_threshold=0.1)
        print(f"\n🌊 心流状态模型:")
        for challenge in [0.2, 0.4, 0.6, 0.8]:
            flow_effect = flow_model.apply(state, 1, challenge_level=challenge)
            print(f"  挑战度{challenge:.1f}的心流效应: {flow_effect:.3f}")
        
        print(f"\n✅ 认知模型演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 认知模型演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def statistics_demo():
    """统计分析演示"""
    print(f"\n📊 统计分析演示")
    print("=" * 50)
    
    try:
        from evaluation.statistics import WelchTTest, CohenDEffect, StatisticalAnalyzer
        
        # 生成模拟数据
        np.random.seed(42)
        algorithm_a = np.random.normal(0.65, 0.1, 30).tolist()
        algorithm_b = np.random.normal(0.55, 0.12, 30).tolist()
        
        print(f"🔬 比较两个算法的性能:")
        print(f"  算法A: 均值={np.mean(algorithm_a):.3f}, 标准差={np.std(algorithm_a):.3f}")
        print(f"  算法B: 均值={np.mean(algorithm_b):.3f}, 标准差={np.std(algorithm_b):.3f}")
        
        # 1. t检验
        t_test = WelchTTest(alpha=0.05)
        t_result = t_test.test(algorithm_a, algorithm_b)
        
        print(f"\n📈 Welch's t检验:")
        print(f"  t统计量: {t_result['statistic']:.3f}")
        print(f"  p值: {t_result['p_value']:.4f}")
        print(f"  显著性: {'是' if t_result['significant'] else '否'}")
        
        # 2. 效应量
        effect_test = CohenDEffect()
        effect_result = effect_test.test(algorithm_a, algorithm_b)
        
        print(f"\n📏 Cohen's d效应量:")
        print(f"  Cohen's d: {effect_result['cohens_d']:.3f}")
        print(f"  效应大小: {effect_result['effect_size']}")
        print(f"  解释: {effect_result['interpretation']}")
        
        # 3. 综合分析
        analyzer = StatisticalAnalyzer()
        comparison = analyzer.compare_two_groups(algorithm_a, algorithm_b, "算法A", "算法B")
        
        print(f"\n📋 综合统计分析:")
        print(comparison['summary'])
        
        print(f"\n✅ 统计分析演示完成！")
        return True
        
    except Exception as e:
        print(f"❌ 统计分析演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 学习路径规划模拟器演示")
    print("=" * 80)
    
    success_count = 0
    total_demos = 3
    
    # 运行各个演示
    if simple_demo():
        success_count += 1
    
    if cognitive_demo():
        success_count += 1
    
    if statistics_demo():
        success_count += 1
    
    # 总结
    print(f"\n" + "=" * 80)
    print(f"📋 演示总结:")
    print(f"  成功: {success_count}/{total_demos}")
    print(f"  成功率: {success_count/total_demos*100:.1f}%")
    
    if success_count == total_demos:
        print(f"🎉 所有演示成功！模拟器运行正常。")
    else:
        print(f"⚠️  部分演示失败，请检查相关模块。")
    
    return success_count == total_demos


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
