"""
仪表板API端点
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from datetime import datetime, timedelta

from app.core.database import get_db
from app.models.experiment import Experiment, Dataset, ExperimentStatus
from app.schemas.dashboard import (
    DashboardStats, 
    RecentActivity, 
    SystemStatus,
    ActivityType
)

router = APIRouter()


@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(db: Session = Depends(get_db)):
    """获取仪表板统计数据"""
    
    # 统计数据集数量
    total_datasets = db.query(Dataset).filter(Dataset.is_active == True).count()
    
    # 统计实验数量
    total_experiments = db.query(Experiment).count()
    running_experiments = db.query(Experiment).filter(
        Experiment.status == ExperimentStatus.RUNNING
    ).count()
    completed_experiments = db.query(Experiment).filter(
        Experiment.status == ExperimentStatus.COMPLETED
    ).count()
    failed_experiments = db.query(Experiment).filter(
        Experiment.status == ExperimentStatus.FAILED
    ).count()
    
    return DashboardStats(
        total_datasets=total_datasets,
        total_experiments=total_experiments,
        running_experiments=running_experiments,
        completed_experiments=completed_experiments,
        failed_experiments=failed_experiments,
        success_rate=completed_experiments / max(total_experiments, 1) * 100
    )


@router.get("/recent-activities", response_model=List[RecentActivity])
async def get_recent_activities(
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """获取最近活动"""
    
    activities = []
    
    # 获取最近的实验
    recent_experiments = db.query(Experiment).order_by(
        desc(Experiment.created_at)
    ).limit(limit // 2).all()
    
    for exp in recent_experiments:
        activity_type = ActivityType.EXPERIMENT_CREATED
        if exp.status == ExperimentStatus.COMPLETED:
            activity_type = ActivityType.EXPERIMENT_COMPLETED
        elif exp.status == ExperimentStatus.FAILED:
            activity_type = ActivityType.EXPERIMENT_FAILED
        elif exp.status == ExperimentStatus.RUNNING:
            activity_type = ActivityType.EXPERIMENT_STARTED
            
        activities.append(RecentActivity(
            id=f"exp_{exp.id}",
            type=activity_type,
            title=exp.name,
            description=f"模型: {exp.model_type}, 数据集: {exp.dataset.name if exp.dataset else 'Unknown'}",
            timestamp=exp.created_at,
            status=exp.status.value
        ))
    
    # 获取最近的数据集
    recent_datasets = db.query(Dataset).order_by(
        desc(Dataset.created_at)
    ).limit(limit // 2).all()
    
    for dataset in recent_datasets:
        activities.append(RecentActivity(
            id=f"dataset_{dataset.id}",
            type=ActivityType.DATASET_UPLOADED,
            title=f"数据集: {dataset.name}",
            description=f"类型: {dataset.dataset_type.value}, 大小: {dataset.file_size or 0} bytes",
            timestamp=dataset.created_at,
            status="success"
        ))
    
    # 按时间排序
    activities.sort(key=lambda x: x.timestamp, reverse=True)
    
    return activities[:limit]


@router.get("/system-status", response_model=SystemStatus)
async def get_system_status(db: Session = Depends(get_db)):
    """获取系统状态"""
    
    # 计算系统负载（模拟）
    running_experiments = db.query(Experiment).filter(
        Experiment.status == ExperimentStatus.RUNNING
    ).count()
    
    # 模拟系统负载计算
    cpu_usage = min(running_experiments * 15 + 20, 95)  # 基础20% + 每个运行实验15%
    memory_usage = min(running_experiments * 12 + 25, 90)  # 基础25% + 每个运行实验12%
    disk_usage = 45  # 固定磁盘使用率
    
    # 计算完成率
    total_experiments = db.query(Experiment).count()
    completed_experiments = db.query(Experiment).filter(
        Experiment.status == ExperimentStatus.COMPLETED
    ).count()
    
    completion_rate = completed_experiments / max(total_experiments, 1) * 100
    
    return SystemStatus(
        cpu_usage=cpu_usage,
        memory_usage=memory_usage,
        disk_usage=disk_usage,
        completion_rate=completion_rate,
        active_tasks=running_experiments,
        uptime_hours=24 * 7  # 模拟7天运行时间
    )


@router.get("/performance-metrics")
async def get_performance_metrics(
    days: int = 7,
    db: Session = Depends(get_db)
):
    """获取性能指标"""
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    # 按天统计实验数量
    daily_stats = db.query(
        func.date(Experiment.created_at).label('date'),
        func.count(Experiment.id).label('count'),
        func.sum(
            func.case(
                (Experiment.status == ExperimentStatus.COMPLETED, 1),
                else_=0
            )
        ).label('completed')
    ).filter(
        Experiment.created_at >= start_date
    ).group_by(
        func.date(Experiment.created_at)
    ).all()
    
    # 格式化数据
    metrics = {
        'daily_experiments': [],
        'success_rate': [],
        'dates': []
    }
    
    for stat in daily_stats:
        metrics['dates'].append(stat.date.strftime('%Y-%m-%d'))
        metrics['daily_experiments'].append(stat.count)
        success_rate = (stat.completed / stat.count * 100) if stat.count > 0 else 0
        metrics['success_rate'].append(round(success_rate, 2))
    
    return metrics


@router.get("/model-comparison")
async def get_model_comparison(db: Session = Depends(get_db)):
    """获取模型对比数据"""
    
    # 按模型类型统计
    model_stats = db.query(
        Experiment.model_type,
        func.count(Experiment.id).label('total'),
        func.avg(Experiment.accuracy).label('avg_accuracy'),
        func.avg(Experiment.auc).label('avg_auc')
    ).filter(
        Experiment.status == ExperimentStatus.COMPLETED,
        Experiment.accuracy.isnot(None)
    ).group_by(
        Experiment.model_type
    ).all()
    
    comparison_data = []
    for stat in model_stats:
        comparison_data.append({
            'model_type': stat.model_type,
            'total_experiments': stat.total,
            'avg_accuracy': round(stat.avg_accuracy or 0, 4),
            'avg_auc': round(stat.avg_auc or 0, 4)
        })
    
    return comparison_data
