import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Table, Tag, Typography, Statistic, Button, Space, Divider } from 'antd';
import { TrophyOutlined, BarChartOutlined, UserOutlined, BulbOutlined } from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Title, Paragraph, Text } = Typography;

interface ModelComparison {
  model_name: string;
  accuracy: number;
  auc: number;
  rmse: number;
  training_time: string;
  is_best: boolean;
}

interface SampleDiagnosis {
  student_id: string;
  overall_ability: number;
  strong_areas: string[];
  weak_areas: string[];
  recommendation: string;
}

interface DemoResultsProps {
  datasetId: number;
  datasetName: string;
}

const DemoResults: React.FC<DemoResultsProps> = ({ datasetId, datasetName }) => {
  const [demoData, setDemoData] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDemoResults();
  }, [datasetId]);

  const loadDemoResults = async () => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:8000/api/v1/demo-results/${datasetId}`);
      const data = await response.json();
      setDemoData(data);
    } catch (error) {
      console.error('加载演示结果失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 模型对比表格列定义
  const modelColumns = [
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name',
      render: (text: string, record: ModelComparison) => (
        <Space>
          <Text strong={record.is_best}>{text}</Text>
          {record.is_best && <Tag color="gold">最佳</Tag>}
        </Space>
      )
    },
    {
      title: '准确率',
      dataIndex: 'accuracy',
      key: 'accuracy',
      render: (value: number) => `${(value * 100).toFixed(2)}%`,
      sorter: (a: ModelComparison, b: ModelComparison) => a.accuracy - b.accuracy
    },
    {
      title: 'AUC',
      dataIndex: 'auc',
      key: 'auc',
      render: (value: number) => value.toFixed(3),
      sorter: (a: ModelComparison, b: ModelComparison) => a.auc - b.auc
    },
    {
      title: 'RMSE',
      dataIndex: 'rmse',
      key: 'rmse',
      render: (value: number) => value.toFixed(3),
      sorter: (a: ModelComparison, b: ModelComparison) => a.rmse - b.rmse
    },
    {
      title: '训练时间',
      dataIndex: 'training_time',
      key: 'training_time'
    }
  ];

  // 性能对比柱状图
  const getPerformanceChart = () => {
    if (!demoData?.models_comparison) return null;

    const models = demoData.models_comparison;
    const option = {
      title: {
        text: '模型性能对比',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['准确率', 'AUC'],
        top: 30
      },
      xAxis: {
        type: 'category',
        data: models.map((m: ModelComparison) => m.model_name)
      },
      yAxis: {
        type: 'value',
        max: 1,
        axisLabel: {
          formatter: (value: number) => `${(value * 100).toFixed(0)}%`
        }
      },
      series: [
        {
          name: '准确率',
          type: 'bar',
          data: models.map((m: ModelComparison) => m.accuracy),
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: 'AUC',
          type: 'bar',
          data: models.map((m: ModelComparison) => m.auc),
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  // 雷达图对比
  const getRadarChart = () => {
    if (!demoData?.visualization_charts?.performance_radar) return null;

    const radarData = demoData.visualization_charts.performance_radar;
    const option = {
      title: {
        text: '模型综合性能雷达图',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        data: ['ORCDF', 'NCDM', 'KANCD'],
        top: 30
      },
      radar: {
        indicator: radarData.categories.map((cat: string) => ({
          name: cat,
          max: 1
        })),
        radius: '70%'
      },
      series: [{
        type: 'radar',
        data: [
          {
            value: radarData.orcdf,
            name: 'ORCDF',
            areaStyle: {
              color: 'rgba(24, 144, 255, 0.3)'
            }
          },
          {
            value: radarData.ncdm,
            name: 'NCDM',
            areaStyle: {
              color: 'rgba(82, 196, 26, 0.3)'
            }
          },
          {
            value: radarData.kancd,
            name: 'KANCD',
            areaStyle: {
              color: 'rgba(250, 173, 20, 0.3)'
            }
          }
        ]
      }]
    };

    return <ReactECharts option={option} style={{ height: '400px' }} />;
  };

  return (
    <div>
      <Card style={{ marginBottom: '16px' }}>
        <Row align="middle" gutter={16}>
          <Col>
            <TrophyOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          </Col>
          <Col flex={1}>
            <Title level={4} style={{ margin: 0 }}>演示结果展示 - {datasetName}</Title>
            <Text type="secondary">预训练模型性能对比和认知诊断示例</Text>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<BarChartOutlined />}
              onClick={loadDemoResults}
              loading={loading}
            >
              刷新数据
            </Button>
          </Col>
        </Row>
      </Card>

      {demoData && (
        <>
          {/* 模型性能统计 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="最佳准确率"
                  value={Math.max(...demoData.models_comparison.map((m: ModelComparison) => m.accuracy)) * 100}
                  precision={2}
                  suffix="%"
                  valueStyle={{ color: '#3f8600' }}
                  prefix={<TrophyOutlined />}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="最佳AUC"
                  value={Math.max(...demoData.models_comparison.map((m: ModelComparison) => m.auc))}
                  precision={3}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="参与模型"
                  value={demoData.models_comparison.length}
                  suffix="个"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card>
                <Statistic
                  title="诊断样例"
                  value={demoData.sample_diagnoses.length}
                  suffix="个"
                  valueStyle={{ color: '#fa8c16' }}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
          </Row>

          {/* 模型对比表格 */}
          <Card title="📊 模型性能对比" style={{ marginBottom: '16px' }}>
            <Table
              dataSource={demoData.models_comparison}
              columns={modelColumns}
              rowKey="model_name"
              pagination={false}
              size="middle"
            />
          </Card>

          {/* 可视化图表 */}
          <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
            <Col xs={24} lg={12}>
              <Card title="性能对比柱状图">
                {getPerformanceChart()}
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="综合性能雷达图">
                {getRadarChart()}
              </Card>
            </Col>
          </Row>

          {/* 认知诊断样例 */}
          <Card title="🎯 认知诊断样例展示">
            <Row gutter={[16, 16]}>
              {demoData.sample_diagnoses.map((diagnosis: SampleDiagnosis, index: number) => (
                <Col xs={24} lg={12} key={diagnosis.student_id}>
                  <Card size="small" style={{ height: '100%' }}>
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong>{diagnosis.student_id}</Text>
                        <Tag color={
                          diagnosis.overall_ability > 0.8 ? 'green' :
                          diagnosis.overall_ability > 0.6 ? 'orange' : 'red'
                        }>
                          能力水平: {(diagnosis.overall_ability * 100).toFixed(1)}%
                        </Tag>
                      </div>

                      <Divider style={{ margin: '8px 0' }} />

                      <div>
                        <Text strong style={{ color: '#52c41a' }}>优势领域：</Text>
                        <div style={{ marginTop: '4px' }}>
                          {diagnosis.strong_areas.map((area, idx) => (
                            <Tag key={idx} color="green" style={{ marginBottom: '4px' }}>
                              {area}
                            </Tag>
                          ))}
                        </div>
                      </div>

                      <div>
                        <Text strong style={{ color: '#ff4d4f' }}>薄弱环节：</Text>
                        <div style={{ marginTop: '4px' }}>
                          {diagnosis.weak_areas.map((area, idx) => (
                            <Tag key={idx} color="red" style={{ marginBottom: '4px' }}>
                              {area}
                            </Tag>
                          ))}
                        </div>
                      </div>

                      <div>
                        <Text strong style={{ color: '#1890ff' }}>学习建议：</Text>
                        <Paragraph style={{ margin: '4px 0 0 0', fontSize: '12px' }}>
                          <BulbOutlined style={{ marginRight: '4px' }} />
                          {diagnosis.recommendation}
                        </Paragraph>
                      </div>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>

          {/* EduBrain优势说明 */}
          <Card title="🏆 EduBrain模型优势" style={{ marginTop: '16px' }}>
            <Row gutter={[24, 24]}>
              <Col xs={24} md={8}>
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🛡️</div>
                  <Title level={4}>抗过度平滑</Title>
                  <Paragraph type="secondary">
                    通过响应图和响应感知图卷积网络，有效缓解传统方法中的过度平滑问题。
                  </Paragraph>
                </div>
              </Col>

              <Col xs={24} md={8}>
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎯</div>
                  <Title level={4}>高准确率</Title>
                  <Paragraph type="secondary">
                    在多个数据集上均取得了最佳的诊断准确率，显著优于基线模型。
                  </Paragraph>
                </div>
              </Col>

              <Col xs={24} md={8}>
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  <div style={{ fontSize: '48px', marginBottom: '16px' }}>🔍</div>
                  <Title level={4}>可解释性</Title>
                  <Paragraph type="secondary">
                    结合大模型语义增强，提供直观可理解的诊断结果和学习建议。
                  </Paragraph>
                </div>
              </Col>
            </Row>
          </Card>
        </>
      )}
    </div>
  );
};

export default DemoResults;
