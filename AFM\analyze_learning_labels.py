#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析训练数据中的学习效果标签来源
"""

import numpy as np
import pickle
from scipy.sparse import vstack
import matplotlib.pyplot as plt

def analyze_interaction_matrix():
    """分析交互矩阵的结构"""
    print("=== 分析交互矩阵结构 ===")
    
    try:
        # 加载原始交互矩阵
        with open('trn_mat.pkl', 'rb') as f:
            trn_mat = pickle.load(f)
        
        print(f"训练矩阵形状: {trn_mat.shape}")
        print(f"矩阵类型: {type(trn_mat)}")
        print(f"数据类型: {trn_mat.dtype}")
        print(f"非零元素数量: {trn_mat.nnz}")
        print(f"稀疏度: {trn_mat.nnz / (trn_mat.shape[0] * trn_mat.shape[1]):.6f}")
        
        # 检查矩阵的列结构
        print(f"\n🔍 矩阵列分析:")
        print(f"总列数: {trn_mat.shape[1]}")
        
        # 检查最后一列（标签列）
        last_col = trn_mat.getcol(-1)
        print(f"最后一列（标签列）非零元素: {last_col.nnz}")
        print(f"最后一列唯一值: {np.unique(last_col.data)}")
        
        # 检查前几列的数据分布
        print(f"\n📊 前几列数据分析:")
        for i in range(min(10, trn_mat.shape[1]-1)):
            col = trn_mat.getcol(i)
            if col.nnz > 0:
                print(f"  列{i}: 非零元素={col.nnz}, 唯一值={len(np.unique(col.data))}, 范围=[{col.data.min():.3f}, {col.data.max():.3f}]")
        
        return trn_mat
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def analyze_labels():
    """分析标签的分布和含义"""
    print(f"\n=== 分析学习效果标签 ===")
    
    try:
        # 加载所有数据
        with open('trn_mat.pkl', 'rb') as f:
            trn_mat = pickle.load(f)
        with open('val_mat.pkl', 'rb') as f:
            val_mat = pickle.load(f)
        with open('tst_mat.pkl', 'rb') as f:
            tst_mat = pickle.load(f)
        
        # 合并并提取标签
        full_interaction = vstack([trn_mat, val_mat, tst_mat]).tocsr()
        labels = np.array((full_interaction.getcol(-1) != 0).toarray(), dtype=np.float32).flatten()
        
        print(f"标签统计:")
        print(f"  总样本数: {len(labels)}")
        print(f"  正样本数: {np.sum(labels)}")
        print(f"  负样本数: {len(labels) - np.sum(labels)}")
        print(f"  正样本比例: {np.mean(labels):.6f}")
        
        # 分析标签的原始值
        last_col_data = full_interaction.getcol(-1).data
        print(f"\n原始标签值分析:")
        print(f"  唯一值: {np.unique(last_col_data)}")
        print(f"  值分布: {np.bincount(last_col_data.astype(int))}")
        
        return labels
        
    except Exception as e:
        print(f"❌ 标签分析失败: {e}")
        return None

def analyze_data_source():
    """分析数据可能的来源"""
    print(f"\n=== 推断数据来源 ===")
    
    # 基于数据特征推断
    print(f"🔍 基于数据特征的推断:")
    print(f"1. 数据规模: 100万+交互记录，1.6万+用户和知识点")
    print(f"   → 这是大规模在线教育平台的数据规模")
    
    print(f"\n2. 稀疏度: 0.01%正样本比例")
    print(f"   → 符合真实教育场景：大多数学习尝试不成功")
    
    print(f"\n3. 嵌入维度: 102维")
    print(f"   → 可能来自预训练的知识图谱嵌入或协同过滤模型")
    
    print(f"\n4. 矩阵结构: 稀疏矩阵，最后一列为标签")
    print(f"   → 典型的推荐系统/CTR预测数据格式")
    
    print(f"\n🎯 可能的标签来源:")
    print(f"A. 答题正确性: 学生答对题目 = 1, 答错 = 0")
    print(f"B. 学习完成度: 完成学习任务 = 1, 未完成 = 0") 
    print(f"C. 掌握程度提升: 学习后掌握度提升 = 1, 无提升 = 0")
    print(f"D. 学习时长: 学习时长超过阈值 = 1, 否则 = 0")
    print(f"E. 学习评分: 学习效果评分 > 阈值 = 1, 否则 = 0")

def analyze_educational_context():
    """分析教育场景下的标签含义"""
    print(f"\n=== 教育场景分析 ===")
    
    print(f"📚 在教育数据中，'学习效果'标签通常来自:")
    
    print(f"\n1. 【最可能】答题结果:")
    print(f"   - 数据来源: 在线练习、测验、作业")
    print(f"   - 标签定义: 答对 = 1, 答错 = 0")
    print(f"   - 符合特征: 低正样本比例（学习有难度）")
    
    print(f"\n2. 【可能】学习行为:")
    print(f"   - 数据来源: 学习视频、阅读材料、互动练习")
    print(f"   - 标签定义: 完成学习 = 1, 中途放弃 = 0")
    print(f"   - 符合特征: 反映学习参与度")
    
    print(f"\n3. 【可能】掌握程度评估:")
    print(f"   - 数据来源: 前后测、能力评估")
    print(f"   - 标签定义: 掌握程度提升 = 1, 无提升 = 0")
    print(f"   - 符合特征: 反映真实学习效果")
    
    print(f"\n4. 【不太可能】主观评价:")
    print(f"   - 数据来源: 学生自评、教师评价")
    print(f"   - 原因: 规模太大，难以人工标注100万+样本")

def simulate_afm_prediction_meaning():
    """模拟AFM预测在教育场景中的含义"""
    print(f"\n=== AFM预测的教育含义 ===")
    
    try:
        from optimized_simulator import RealAFMAgent
        
        agent = RealAFMAgent(action_space_size=5, user_id=0)
        
        if agent.model is None:
            print("❌ 无法加载AFM模型")
            return
        
        print(f"🤖 AFM模型预测示例:")
        predictions = []
        for i in range(5):
            pred = agent.predict_learning_effect(i)
            predictions.append(pred)
            print(f"  用户0学习知识点{i}: {pred:.4f} ({pred*100:.1f}%)")
        
        print(f"\n📊 预测解释:")
        print(f"这些概率值表示:")
        print(f"- 如果是答题场景: 该用户答对该知识点题目的概率")
        print(f"- 如果是学习场景: 该用户成功完成该知识点学习的概率")
        print(f"- 如果是掌握场景: 该用户通过学习该知识点提升掌握度的概率")
        
        # 分析预测的合理性
        pred_std = np.std(predictions)
        print(f"\n🎯 预测合理性分析:")
        print(f"- 预测值范围: [{min(predictions):.4f}, {max(predictions):.4f}]")
        print(f"- 标准差: {pred_std:.4f}")
        
        if pred_std > 0.01:
            print(f"✅ 预测有差异性，模型学到了用户-知识点的个性化模式")
        else:
            print(f"⚠️  预测差异较小，可能需要更多训练或特征工程")
            
    except Exception as e:
        print(f"❌ 预测分析失败: {e}")

def main():
    """主分析函数"""
    print("🔍 分析AFM训练数据中的学习效果标签来源")
    print("=" * 60)
    
    # 分析交互矩阵
    trn_mat = analyze_interaction_matrix()
    
    # 分析标签
    labels = analyze_labels()
    
    # 推断数据来源
    analyze_data_source()
    
    # 教育场景分析
    analyze_educational_context()
    
    # AFM预测含义
    simulate_afm_prediction_meaning()
    
    print(f"\n" + "=" * 60)
    print(f"🎯 总结:")
    print(f"AFM模型训练时的'学习效果'标签最可能来自:")
    print(f"1. 【主要】学生答题的正确性（答对=1，答错=0）")
    print(f"2. 【辅助】学习任务的完成情况（完成=1，未完成=0）")
    print(f"3. 【可能】学习后的掌握程度提升（提升=1，无提升=0）")
    print(f"\n这些都是客观、可量化的教育数据，不是人工标注的主观评价。")
    print(f"AFM模型学习的是'在给定用户和知识点特征下，学习成功的概率'。")

if __name__ == "__main__":
    main()
