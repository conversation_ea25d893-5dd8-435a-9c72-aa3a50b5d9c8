"""
认知诊断API端点
"""
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
import json
import asyncio

from app.core.database import get_db
from app.services.cognitive_diagnosis_service import CognitiveDiagnosisService
from app.services.learning_planning_service import LearningPlanningService
from app.models.experiment import Experiment, ModelType, DiagnosisRecord
from app.schemas.cognitive_diagnosis import (
    DiagnosisRequest,
    DiagnosisResponse,
    LearningPathRequest,
    LearningPathResponse,
    TrainingRequest,
    TrainingResponse
)

router = APIRouter()


@router.post("/train", response_model=TrainingResponse)
async def train_diagnosis_model(
    request: TrainingRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """训练认知诊断模型"""
    service = CognitiveDiagnosisService(db)
    
    try:
        # 异步训练模型
        result = service.train_model(
            experiment_id=request.experiment_id,
            model_type=request.model_type,
            config=request.config
        )
        
        return TrainingResponse(
            success=True,
            message="模型训练完成",
            experiment_id=request.experiment_id,
            results=result
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"训练失败: {str(e)}")


@router.post("/diagnose", response_model=DiagnosisResponse)
async def diagnose_student(
    request: DiagnosisRequest,
    db: Session = Depends(get_db)
):
    """对学生进行认知诊断"""
    service = CognitiveDiagnosisService(db)

    try:
        # 获取实验结果
        experiment = db.query(Experiment).filter(
            Experiment.id == request.experiment_id
        ).first()

        if not experiment:
            raise HTTPException(status_code=404, detail="实验不存在")

        if not experiment.results:
            raise HTTPException(status_code=400, detail="模型尚未训练完成")

        # 进行诊断
        diagnosis = service.diagnose_student(
            student_id=request.student_id,
            model_results=experiment.results,
            experiment_id=request.experiment_id
        )

        return DiagnosisResponse(
            success=True,
            student_id=request.student_id,
            diagnosis=diagnosis
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"诊断失败: {str(e)}")


@router.post("/diagnose-with-report")
async def diagnose_student_with_llm_report_json(
    request: DiagnosisRequest,
    db: Session = Depends(get_db)
):
    """对学生进行认知诊断并生成LLM报告（JSON响应）"""
    import logging
    logger = logging.getLogger(__name__)

    logger.info(f"🎯 收到LLM诊断请求: 学生={request.student_id}, 实验={request.experiment_id}")

    service = CognitiveDiagnosisService(db)

    try:
        # 获取实验结果
        logger.info(f"🔍 查找实验 {request.experiment_id}...")
        experiment = db.query(Experiment).filter(
            Experiment.id == request.experiment_id
        ).first()

        if not experiment:
            logger.error(f"❌ 实验 {request.experiment_id} 不存在")
            raise HTTPException(status_code=404, detail="实验不存在")

        if not experiment.results:
            logger.error(f"❌ 实验 {request.experiment_id} 模型尚未训练完成")
            raise HTTPException(status_code=400, detail="模型尚未训练完成")

        logger.info(f"✅ 实验 {request.experiment_id} 找到，状态: {experiment.status}")

        # 进行诊断并生成LLM报告
        logger.info(f"🚀 开始诊断...")
        diagnosis = await service.diagnose_student_with_llm_report(
            student_id=request.student_id,
            model_results=experiment.results,
            experiment_id=request.experiment_id
        )

        logger.info(f"✅ 诊断完成，报告生成状态: {diagnosis.get('report_generated', False)}")

        response = {
            "success": True,
            "student_id": request.student_id,
            "diagnosis": diagnosis
        }

        logger.info(f"📤 返回诊断结果")
        return response

    except HTTPException as e:
        logger.error(f"❌ HTTP异常: {e.detail}")
        raise
    except Exception as e:
        logger.error(f"❌ 诊断异常: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"诊断失败: {str(e)}")


@router.post("/diagnose-with-report-stream")
async def diagnose_student_with_llm_report_stream(
    request: DiagnosisRequest,
    db: Session = Depends(get_db)
):
    """对学生进行认知诊断并生成LLM报告（流式输出）"""
    service = CognitiveDiagnosisService(db)

    try:
        # 获取实验结果
        experiment = db.query(Experiment).filter(
            Experiment.id == request.experiment_id
        ).first()

        if not experiment:
            raise HTTPException(status_code=404, detail="实验不存在")

        if not experiment.results:
            raise HTTPException(status_code=400, detail="模型尚未训练完成")

        # 创建流式响应生成器
        async def generate_diagnosis_stream():
            try:
                # 首先发送基础诊断数据
                basic_diagnosis = service.diagnose_student(
                    student_id=request.student_id,
                    model_results=experiment.results,
                    experiment_id=request.experiment_id,
                    save_record=False
                )

                # 发送基础诊断结果
                yield f"data: {json.dumps({'type': 'basic_diagnosis', 'data': basic_diagnosis}, ensure_ascii=False)}\n\n"

                # 开始LLM思考过程
                yield f"data: {json.dumps({'type': 'thinking_start', 'message': '🤔 AI正在分析您的学习数据...'}, ensure_ascii=False)}\n\n"

                # 生成LLM报告（支持流式输出）
                async for chunk in service.diagnose_student_with_llm_report_stream(
                    student_id=request.student_id,
                    model_results=experiment.results,
                    experiment_id=request.experiment_id,
                    basic_diagnosis=basic_diagnosis
                ):
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'complete'}, ensure_ascii=False)}\n\n"

            except Exception as e:
                # 发送错误信息
                yield f"data: {json.dumps({'type': 'error', 'message': str(e)}, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            generate_diagnosis_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"诊断失败: {str(e)}")


@router.post("/learning-path", response_model=LearningPathResponse)
async def generate_learning_path(
    request: LearningPathRequest,
    db: Session = Depends(get_db)
):
    """生成个性化学习路径"""
    diagnosis_service = CognitiveDiagnosisService(db)
    planning_service = LearningPlanningService(db)
    
    try:
        # 首先进行认知诊断
        experiment = db.query(Experiment).filter(
            Experiment.id == request.experiment_id
        ).first()
        
        if not experiment or not experiment.results:
            raise HTTPException(status_code=400, detail="需要先完成模型训练和诊断")
        
        # 获取诊断结果
        diagnosis = diagnosis_service.diagnose_student(
            student_id=request.student_id,
            model_results=experiment.results
        )
        
        # 生成学习路径
        learning_path = planning_service.generate_learning_path(
            student_id=request.student_id,
            diagnosis_result=diagnosis
        )
        
        return LearningPathResponse(
            success=True,
            student_id=request.student_id,
            learning_path=learning_path,
            diagnosis=diagnosis
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成学习路径失败: {str(e)}")


@router.get("/models/comparison")
async def compare_models(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """对比不同模型的性能"""
    
    # 获取该数据集上的所有完成的实验
    experiments = db.query(Experiment).filter(
        Experiment.dataset_id == dataset_id,
        Experiment.status == "completed",
        Experiment.results.isnot(None)
    ).all()
    
    if not experiments:
        raise HTTPException(status_code=404, detail="没有找到完成的实验")
    
    comparison_data = []
    for exp in experiments:
        if exp.results and exp.metrics:
            comparison_data.append({
                "experiment_id": exp.id,
                "model_type": exp.model_type,
                "accuracy": exp.metrics.get("accuracy", 0),
                "auc": exp.metrics.get("auc", 0),
                "rmse": exp.metrics.get("rmse", 0),
                "training_time": (exp.completed_at - exp.started_at).total_seconds() if exp.completed_at and exp.started_at else 0,
                "config": exp.config
            })
    
    # 按准确率排序
    comparison_data.sort(key=lambda x: x["accuracy"], reverse=True)
    
    return {
        "dataset_id": dataset_id,
        "model_count": len(comparison_data),
        "best_model": comparison_data[0] if comparison_data else None,
        "comparison_data": comparison_data
    }


@router.get("/knowledge-components")
async def get_knowledge_components(db: Session = Depends(get_db)):
    """获取知识点列表"""
    service = CognitiveDiagnosisService(db)
    kcs = service._get_knowledge_components()
    
    return {
        "knowledge_components": kcs,
        "total_count": len(kcs)
    }


@router.get("/data-preprocessing/{dataset_id}")
async def get_data_preprocessing_info(
    dataset_id: int,
    db: Session = Depends(get_db)
):
    """获取数据预处理信息"""
    service = CognitiveDiagnosisService(db)
    
    try:
        data_info = service.prepare_data(dataset_id)
        
        return {
            "success": True,
            "dataset_id": dataset_id,
            "preprocessing_info": data_info["data_info"],
            "q_matrix_shape": data_info["data_info"]["q_matrix_shape"],
            "response_matrix_shape": data_info["data_info"]["response_matrix_shape"],
            "knowledge_components": data_info["knowledge_components"]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据预处理失败: {str(e)}")


@router.post("/batch-diagnosis")
async def batch_diagnose_students(
    experiment_id: int,
    student_ids: List[str],
    db: Session = Depends(get_db)
):
    """批量诊断学生"""
    service = CognitiveDiagnosisService(db)
    
    experiment = db.query(Experiment).filter(
        Experiment.id == experiment_id
    ).first()
    
    if not experiment or not experiment.results:
        raise HTTPException(status_code=400, detail="模型尚未训练完成")
    
    diagnoses = []
    for student_id in student_ids:
        try:
            diagnosis = service.diagnose_student(
                student_id=student_id,
                model_results=experiment.results
            )
            diagnoses.append({
                "student_id": student_id,
                "success": True,
                "diagnosis": diagnosis
            })
        except Exception as e:
            diagnoses.append({
                "student_id": student_id,
                "success": False,
                "error": str(e)
            })
    
    return {
        "experiment_id": experiment_id,
        "total_students": len(student_ids),
        "successful_diagnoses": len([d for d in diagnoses if d["success"]]),
        "diagnoses": diagnoses
    }


@router.get("/learning-progress/{student_id}")
async def get_learning_progress(
    student_id: str,
    db: Session = Depends(get_db)
):
    """获取学生学习进度"""
    planning_service = LearningPlanningService(db)
    
    # 模拟已完成的学习活动
    completed_activities = [
        {
            "activity_id": "act_1",
            "type": "concept_learning",
            "title": "代数运算概念学习",
            "completed": True,
            "score": 85,
            "time_spent": 35,
            "date": "2024-01-15"
        },
        {
            "activity_id": "act_2", 
            "type": "practice",
            "title": "代数运算练习题",
            "completed": True,
            "score": 78,
            "time_spent": 45,
            "date": "2024-01-16"
        },
        {
            "activity_id": "act_3",
            "type": "application",
            "title": "几何推理应用练习",
            "completed": False,
            "score": 0,
            "time_spent": 0,
            "date": None
        }
    ]
    
    progress = planning_service.track_learning_progress(
        student_id=student_id,
        completed_activities=completed_activities
    )
    
    return {
        "student_id": student_id,
        "progress": progress,
        "activities": completed_activities
    }


@router.post("/update-learning-progress")
async def update_learning_progress(
    student_id: str,
    activity_id: str,
    score: float,
    time_spent: int,
    db: Session = Depends(get_db)
):
    """更新学习进度"""
    
    # 这里应该更新数据库中的学习进度记录
    # 现在返回模拟响应
    
    return {
        "success": True,
        "message": "学习进度已更新",
        "student_id": student_id,
        "activity_id": activity_id,
        "updated_at": "2024-01-17T10:30:00"
    }


@router.post("/batch-diagnosis-with-reports")
async def batch_diagnose_students_with_reports(
    experiment_id: int,
    student_ids: List[str],
    db: Session = Depends(get_db)
):
    """批量诊断学生并生成LLM报告"""
    service = CognitiveDiagnosisService(db)

    try:
        diagnoses = await service.generate_batch_diagnosis_with_reports(
            experiment_id=experiment_id,
            student_ids=student_ids
        )

        return {
            "experiment_id": experiment_id,
            "total_students": len(student_ids),
            "successful_diagnoses": sum(1 for d in diagnoses if d["success"]),
            "diagnoses": diagnoses
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量诊断失败: {str(e)}")


@router.get("/diagnosis-records")
async def get_diagnosis_records(
    experiment_id: Optional[int] = None,
    student_id: Optional[str] = None,
    diagnosis_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取诊断记录列表"""
    query = db.query(DiagnosisRecord)

    # 添加过滤条件
    if experiment_id:
        query = query.filter(DiagnosisRecord.experiment_id == experiment_id)
    if student_id:
        query = query.filter(DiagnosisRecord.student_id == student_id)
    if diagnosis_type:
        query = query.filter(DiagnosisRecord.diagnosis_type == diagnosis_type)

    # 按诊断时间倒序排列
    query = query.order_by(DiagnosisRecord.diagnosis_time.desc())

    # 分页
    total = query.count()
    records = query.offset(skip).limit(limit).all()

    # 转换为字典格式
    result_records = []
    for record in records:
        result_records.append({
            "id": record.id,
            "student_id": record.student_id,
            "experiment_id": record.experiment_id,
            "diagnosis_type": record.diagnosis_type,
            "overall_ability": record.overall_ability,
            "confidence_level": record.confidence_level,
            "knowledge_diagnosis": record.knowledge_diagnosis,
            "diagnosis_data": record.diagnosis_data,
            "llm_report": record.llm_report,
            "diagnosis_time": record.diagnosis_time.isoformat() if record.diagnosis_time else None,
            "created_at": record.created_at.isoformat() if record.created_at else None
        })

    return {
        "total": total,
        "records": result_records,
        "skip": skip,
        "limit": limit
    }


@router.get("/diagnosis-records/{record_id}")
async def get_diagnosis_record(
    record_id: int,
    db: Session = Depends(get_db)
):
    """获取单个诊断记录详情"""
    record = db.query(DiagnosisRecord).filter(DiagnosisRecord.id == record_id).first()

    if not record:
        raise HTTPException(status_code=404, detail="诊断记录不存在")

    return {
        "id": record.id,
        "student_id": record.student_id,
        "experiment_id": record.experiment_id,
        "diagnosis_type": record.diagnosis_type,
        "overall_ability": record.overall_ability,
        "confidence_level": record.confidence_level,
        "knowledge_diagnosis": record.knowledge_diagnosis,
        "diagnosis_data": record.diagnosis_data,
        "llm_report": record.llm_report,
        "diagnosis_time": record.diagnosis_time.isoformat() if record.diagnosis_time else None,
        "created_at": record.created_at.isoformat() if record.created_at else None,
        "updated_at": record.updated_at.isoformat() if record.updated_at else None
    }


@router.get("/reports")
async def get_diagnosis_reports(
    student_id: Optional[str] = None,
    experiment_id: Optional[int] = None,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """获取诊断报告列表"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        query = db.query(DiagnosisRecord)

        if student_id:
            query = query.filter(DiagnosisRecord.student_id == student_id)
        if experiment_id:
            query = query.filter(DiagnosisRecord.experiment_id == experiment_id)

        # 按创建时间倒序排列
        query = query.order_by(DiagnosisRecord.created_at.desc())

        # 分页
        total = query.count()
        records = query.offset(offset).limit(limit).all()

        reports = []
        for record in records:
            reports.append({
                "id": record.id,
                "student_id": record.student_id,
                "experiment_id": record.experiment_id,
                "diagnosis_type": record.diagnosis_type,
                "overall_ability": record.overall_ability,
                "confidence_level": record.confidence_level,
                "has_llm_report": bool(record.llm_report),
                "diagnosis_time": record.diagnosis_time.isoformat() if record.diagnosis_time else None,
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "title": f"学生{record.student_id}的认知诊断报告",
                "summary": record.llm_report.get("summary", "基础诊断报告") if record.llm_report else "基础诊断报告"
            })

        return {
            "success": True,
            "reports": reports,
            "total": total,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"获取诊断报告列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取报告列表失败: {str(e)}")


@router.get("/reports/{report_id}")
async def get_diagnosis_report(
    report_id: int,
    db: Session = Depends(get_db)
):
    """获取单个诊断报告详情"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        record = db.query(DiagnosisRecord).filter(DiagnosisRecord.id == report_id).first()

        if not record:
            raise HTTPException(status_code=404, detail="诊断报告不存在")

        return {
            "success": True,
            "report": {
                "id": record.id,
                "student_id": record.student_id,
                "experiment_id": record.experiment_id,
                "diagnosis_type": record.diagnosis_type,
                "overall_ability": record.overall_ability,
                "confidence_level": record.confidence_level,
                "knowledge_diagnosis": record.knowledge_diagnosis,
                "diagnosis_data": record.diagnosis_data,
                "llm_report": record.llm_report,
                "diagnosis_time": record.diagnosis_time.isoformat() if record.diagnosis_time else None,
                "created_at": record.created_at.isoformat() if record.created_at else None,
                "updated_at": record.updated_at.isoformat() if record.updated_at else None
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取诊断报告详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取报告详情失败: {str(e)}")


@router.delete("/reports/{report_id}")
async def delete_diagnosis_report(
    report_id: int,
    db: Session = Depends(get_db)
):
    """删除诊断报告"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        record = db.query(DiagnosisRecord).filter(DiagnosisRecord.id == report_id).first()

        if not record:
            raise HTTPException(status_code=404, detail="诊断报告不存在")

        db.delete(record)
        db.commit()

        return {
            "success": True,
            "message": "诊断报告已删除"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除诊断报告失败: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除报告失败: {str(e)}")
