#!/usr/bin/env python3
"""
项目清理脚本
用于清理开发过程中产生的临时文件和缓存
"""

import os
import shutil
import glob
from pathlib import Path

def clean_python_cache():
    """清理 Python 缓存文件"""
    print("🧹 清理 Python 缓存文件...")
    
    # 查找并删除 __pycache__ 目录
    for pycache_dir in glob.glob("**/__pycache__", recursive=True):
        if os.path.exists(pycache_dir):
            shutil.rmtree(pycache_dir)
            print(f"  ✅ 删除: {pycache_dir}")
    
    # 删除 .pyc 文件
    for pyc_file in glob.glob("**/*.pyc", recursive=True):
        if os.path.exists(pyc_file):
            os.remove(pyc_file)
            print(f"  ✅ 删除: {pyc_file}")

def clean_node_modules():
    """清理 node_modules 目录"""
    print("📦 清理 node_modules...")
    
    node_modules_dirs = [
        "frontend/node_modules",
        "backend/node_modules"
    ]
    
    for node_dir in node_modules_dirs:
        if os.path.exists(node_dir):
            print(f"  🗑️  删除: {node_dir}")
            shutil.rmtree(node_dir)
            print(f"  ✅ 已删除: {node_dir}")

def clean_logs():
    """清理日志文件"""
    print("📝 清理日志文件...")
    
    log_patterns = [
        "**/*.log",
        "**/logs/**/*",
        "backend/logs/**/*"
    ]
    
    for pattern in log_patterns:
        for log_file in glob.glob(pattern, recursive=True):
            if os.path.isfile(log_file):
                os.remove(log_file)
                print(f"  ✅ 删除: {log_file}")

def clean_build_artifacts():
    """清理构建产物"""
    print("🔨 清理构建产物...")
    
    build_dirs = [
        "frontend/build",
        "frontend/dist",
        ".next",
        "out"
    ]
    
    for build_dir in build_dirs:
        if os.path.exists(build_dir):
            shutil.rmtree(build_dir)
            print(f"  ✅ 删除: {build_dir}")

def clean_temp_files():
    """清理临时文件"""
    print("🗂️  清理临时文件...")
    
    temp_patterns = [
        "**/.DS_Store",
        "**/Thumbs.db",
        "**/*.tmp",
        "**/*.temp",
        "**/~*"
    ]
    
    for pattern in temp_patterns:
        for temp_file in glob.glob(pattern, recursive=True):
            if os.path.isfile(temp_file):
                os.remove(temp_file)
                print(f"  ✅ 删除: {temp_file}")

def clean_test_artifacts():
    """清理测试产物"""
    print("🧪 清理测试产物...")
    
    test_dirs = [
        "coverage",
        ".coverage",
        ".pytest_cache",
        ".nyc_output"
    ]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            if os.path.isdir(test_dir):
                shutil.rmtree(test_dir)
            else:
                os.remove(test_dir)
            print(f"  ✅ 删除: {test_dir}")

def main():
    """主函数"""
    print("🚀 开始清理项目...")
    print("=" * 50)
    
    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    try:
        clean_python_cache()
        clean_node_modules()
        clean_logs()
        clean_build_artifacts()
        clean_temp_files()
        clean_test_artifacts()
        
        print("=" * 50)
        print("✨ 项目清理完成！")
        print("\n📋 接下来的步骤:")
        print("1. 重新安装依赖:")
        print("   cd frontend && npm install")
        print("   cd backend && pip install -r requirements.txt")
        print("2. 启动服务:")
        print("   后端: cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
        print("   前端: cd frontend && npm start")
        
    except Exception as e:
        print(f"❌ 清理过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
