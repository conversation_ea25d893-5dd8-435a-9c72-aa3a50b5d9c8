"""
LLM相关API端点
"""
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

router = APIRouter()


class DiagnosisReportRequest(BaseModel):
    student_id: str
    knowledge_diagnosis: Dict[str, Any]
    overall_ability: float


class QuestionRecommendationRequest(BaseModel):
    student_id: str
    knowledge_diagnosis: Dict[str, Any]
    overall_ability: float


class ReportResponse(BaseModel):
    success: bool
    report: str


class QuestionRecommendationResponse(BaseModel):
    success: bool
    recommended_questions: List[Dict[str, Any]]


@router.post("/generate-report", response_model=ReportResponse)
async def generate_diagnosis_report(request: DiagnosisReportRequest):
    """使用大模型生成诊断报告"""
    try:
        student_id = request.student_id
        knowledge_diagnosis = request.knowledge_diagnosis
        overall_ability = request.overall_ability

        # 分析强项和弱项
        strong_areas = [kc for kc, data in knowledge_diagnosis.items() if data["mastery_probability"] > 0.7]
        weak_areas = [kc for kc, data in knowledge_diagnosis.items() if data["mastery_probability"] < 0.5]
        moderate_areas = [kc for kc, data in knowledge_diagnosis.items() if 0.5 <= data["mastery_probability"] <= 0.7]

        # 生成诊断报告
        report = f"""## 📊 学生 {student_id} 认知诊断报告

### 🎯 整体能力评估
学生的整体能力水平为 **{overall_ability * 100:.1f}%**，{'表现优秀' if overall_ability > 0.8 else '表现良好' if overall_ability > 0.6 else '需要加强练习'}。

### 💪 优势知识点 ({len(strong_areas)}个)
{chr(10).join([f"- **{area}**: 掌握程度 {knowledge_diagnosis[area]['mastery_probability']*100:.1f}%" for area in strong_areas]) if strong_areas else "暂无明显优势知识点"}

### 📈 待提升知识点 ({len(moderate_areas)}个)
{chr(10).join([f"- **{area}**: 掌握程度 {knowledge_diagnosis[area]['mastery_probability']*100:.1f}%" for area in moderate_areas]) if moderate_areas else "无需重点关注的知识点"}

### ⚠️ 薄弱知识点 ({len(weak_areas)}个)
{chr(10).join([f"- **{area}**: 掌握程度 {knowledge_diagnosis[area]['mastery_probability']*100:.1f}%" for area in weak_areas]) if weak_areas else "无明显薄弱知识点"}

### 🎯 学习建议
{'继续保持当前的学习状态，可以尝试更有挑战性的题目。' if overall_ability > 0.8 else '建议重点关注薄弱知识点的练习，同时巩固已掌握的知识点。' if weak_areas else '学习状态良好，建议均衡发展各个知识点。'}

### 📚 推荐学习路径
1. **巩固优势**: 继续强化已掌握的知识点
2. **重点突破**: 针对薄弱知识点进行专项练习
3. **综合提升**: 通过综合性题目提升整体能力
"""

        return ReportResponse(success=True, report=report)
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成诊断报告失败: {str(e)}"
        )


@router.post("/recommend-questions", response_model=QuestionRecommendationResponse)
async def recommend_questions(request: QuestionRecommendationRequest):
    """基于诊断结果推荐题目"""
    try:
        knowledge_diagnosis = request.knowledge_diagnosis
        
        # 找出薄弱知识点
        weak_areas = [kc for kc, data in knowledge_diagnosis.items() if data["mastery_probability"] < 0.6]
        
        # 模拟推荐题目
        questions = []
        for i, kc in enumerate(weak_areas[:3]):  # 最多推荐3个知识点的题目
            for j in range(3):  # 每个知识点推荐3道题
                difficulty = "基础" if j == 0 else "中等" if j == 1 else "提高"
                questions.append({
                    "id": f"q_{kc}_{j+1}",
                    "knowledge_component": kc,
                    "difficulty": difficulty,
                    "difficulty_level": j + 1,
                    "question": f"这是一道关于{kc}的{difficulty}题目，帮助学生巩固相关概念。",
                    "explanation": f"通过这道题目，学生可以练习{kc}的核心概念和解题方法。",
                    "estimated_time": 5 + j * 3,  # 预估完成时间（分钟）
                    "mastery_probability": knowledge_diagnosis[kc]["mastery_probability"],
                    "priority": 1 if j == 0 else 2 if j == 1 else 3
                })
        
        # 如果没有薄弱知识点，推荐一些综合性题目
        if not questions:
            for i in range(3):
                questions.append({
                    "id": f"comprehensive_{i+1}",
                    "knowledge_component": "综合应用",
                    "difficulty": "中等",
                    "difficulty_level": 2,
                    "question": f"这是一道综合性题目，涉及多个知识点的应用。",
                    "explanation": "通过综合性题目，可以提升学生的整体应用能力。",
                    "estimated_time": 10,
                    "mastery_probability": request.overall_ability,
                    "priority": 2
                })
        
        # 按优先级排序
        questions.sort(key=lambda x: x["priority"])
        
        return QuestionRecommendationResponse(
            success=True,
            recommended_questions=questions
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"推荐题目失败: {str(e)}"
        )


@router.post("/analyze-learning-path")
async def analyze_learning_path(request: DiagnosisReportRequest):
    """分析学习路径"""
    try:
        knowledge_diagnosis = request.knowledge_diagnosis
        
        # 按掌握程度排序知识点
        sorted_kcs = sorted(
            knowledge_diagnosis.items(),
            key=lambda x: x[1]["mastery_probability"]
        )
        
        learning_path = {
            "student_id": request.student_id,
            "current_level": request.overall_ability,
            "learning_stages": [
                {
                    "stage": 1,
                    "name": "基础巩固",
                    "description": "巩固薄弱知识点",
                    "knowledge_components": [kc for kc, data in sorted_kcs[:3]],
                    "estimated_duration": "2-3周"
                },
                {
                    "stage": 2,
                    "name": "能力提升",
                    "description": "提升中等掌握的知识点",
                    "knowledge_components": [kc for kc, data in sorted_kcs[3:6]],
                    "estimated_duration": "3-4周"
                },
                {
                    "stage": 3,
                    "name": "综合应用",
                    "description": "综合运用所有知识点",
                    "knowledge_components": ["综合应用", "实际问题解决"],
                    "estimated_duration": "2-3周"
                }
            ]
        }
        
        return {"success": True, "learning_path": learning_path}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析学习路径失败: {str(e)}"
        )
