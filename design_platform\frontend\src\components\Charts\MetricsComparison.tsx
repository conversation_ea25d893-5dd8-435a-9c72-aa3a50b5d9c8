import React from 'react';
import { Card, Row, Col, Statistic, Progress, Typography, Tag } from 'antd';
import { 
  TrophyOutlined, 
  RiseOutlined, 
  FallOutlined,
  MinusOutlined 
} from '@ant-design/icons';
import ReactECharts from 'echarts-for-react';

const { Title, Text } = Typography;

interface MetricsData {
  accuracy: number;
  auc: number;
  loss: number;
  precision?: number;
  recall?: number;
  f1_score?: number;
}

interface MetricsComparisonProps {
  currentMetrics: MetricsData;
  previousMetrics?: MetricsData;
  modelType: string;
}

const MetricsComparison: React.FC<MetricsComparisonProps> = ({
  currentMetrics,
  previousMetrics,
  modelType
}) => {
  const calculateChange = (current: number, previous?: number) => {
    if (!previous) return null;
    const change = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(change),
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'same',
      color: change > 0 ? '#52c41a' : change < 0 ? '#ff4d4f' : '#d9d9d9'
    };
  };

  const getChangeIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <RiseOutlined />;
      case 'down': return <FallOutlined />;
      default: return <MinusOutlined />;
    }
  };

  const getRadarChart = () => {
    const data = [
      { name: '准确率', value: currentMetrics.accuracy * 100, max: 100 },
      { name: 'AUC', value: currentMetrics.auc * 100, max: 100 },
      { name: '损失', value: (1 - currentMetrics.loss) * 100, max: 100 }, // 反转损失值
      { name: '精确率', value: (currentMetrics.precision || currentMetrics.accuracy) * 100, max: 100 },
      { name: '召回率', value: (currentMetrics.recall || currentMetrics.accuracy) * 100, max: 100 },
      { name: 'F1分数', value: (currentMetrics.f1_score || currentMetrics.accuracy) * 100, max: 100 }
    ];

    const option = {
      title: {
        text: `${modelType} 模型性能雷达图`,
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `${params.name}: ${params.value.toFixed(1)}%`;
        }
      },
      radar: {
        indicator: data.map(item => ({
          name: item.name,
          max: item.max
        })),
        radius: '70%',
        splitNumber: 4,
        axisName: {
          fontSize: 12,
          color: '#666'
        },
        splitLine: {
          lineStyle: {
            color: '#e8e8e8'
          }
        },
        splitArea: {
          areaStyle: {
            color: ['rgba(114, 172, 209, 0.1)', 'rgba(114, 172, 209, 0.05)']
          }
        }
      },
      series: [{
        type: 'radar',
        data: [{
          value: data.map(item => item.value),
          name: '当前性能',
          areaStyle: {
            color: 'rgba(24, 144, 255, 0.3)'
          },
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          }
        }]
      }]
    };

    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  const getMetricsBarChart = () => {
    const metrics = [
      { name: '准确率', value: currentMetrics.accuracy * 100, color: '#1890ff' },
      { name: 'AUC', value: currentMetrics.auc * 100, color: '#52c41a' },
      { name: '精确率', value: (currentMetrics.precision || currentMetrics.accuracy) * 100, color: '#faad14' },
      { name: '召回率', value: (currentMetrics.recall || currentMetrics.accuracy) * 100, color: '#722ed1' },
      { name: 'F1分数', value: (currentMetrics.f1_score || currentMetrics.accuracy) * 100, color: '#eb2f96' }
    ];

    const option = {
      title: {
        text: '性能指标对比',
        left: 'center',
        textStyle: {
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: (params: any) => {
          const param = params[0];
          return `${param.name}: ${param.value.toFixed(1)}%`;
        }
      },
      xAxis: {
        type: 'category',
        data: metrics.map(m => m.name),
        axisLabel: {
          fontSize: 11,
          rotate: 45
        }
      },
      yAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [{
        type: 'bar',
        data: metrics.map(m => ({
          value: m.value,
          itemStyle: {
            color: m.color
          }
        })),
        barWidth: '60%',
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          fontSize: 10
        }
      }]
    };

    return <ReactECharts option={option} style={{ height: '300px' }} />;
  };

  return (
    <div>
      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="准确率"
              value={currentMetrics.accuracy * 100}
              precision={2}
              suffix="%"
              valueStyle={{ color: '#1890ff' }}
              prefix={<TrophyOutlined />}
            />
            {previousMetrics && (() => {
              const change = calculateChange(currentMetrics.accuracy, previousMetrics.accuracy);
              return change && (
                <div style={{ marginTop: '8px' }}>
                  <Text style={{ color: change.color, fontSize: '12px' }}>
                    {getChangeIcon(change.direction)} {change.value.toFixed(2)}%
                  </Text>
                </div>
              );
            })()}
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="AUC"
              value={currentMetrics.auc}
              precision={4}
              valueStyle={{ color: '#52c41a' }}
            />
            {previousMetrics && (() => {
              const change = calculateChange(currentMetrics.auc, previousMetrics.auc);
              return change && (
                <div style={{ marginTop: '8px' }}>
                  <Text style={{ color: change.color, fontSize: '12px' }}>
                    {getChangeIcon(change.direction)} {change.value.toFixed(4)}
                  </Text>
                </div>
              );
            })()}
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="损失"
              value={currentMetrics.loss}
              precision={4}
              valueStyle={{ color: '#ff4d4f' }}
            />
            {previousMetrics && (() => {
              const change = calculateChange(currentMetrics.loss, previousMetrics.loss);
              return change && (
                <div style={{ marginTop: '8px' }}>
                  <Text style={{ color: change.direction === 'down' ? '#52c41a' : '#ff4d4f', fontSize: '12px' }}>
                    {getChangeIcon(change.direction === 'down' ? 'up' : 'down')} {change.value.toFixed(4)}
                  </Text>
                </div>
              );
            })()}
          </Card>
        </Col>
      </Row>

      {/* 性能进度条 */}
      <Card title="性能评估" style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>准确率</Text>
              <Progress 
                percent={currentMetrics.accuracy * 100} 
                strokeColor="#1890ff"
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>AUC</Text>
              <Progress 
                percent={currentMetrics.auc * 100} 
                strokeColor="#52c41a"
                format={(percent) => `${(percent! / 100).toFixed(3)}`}
              />
            </div>
            <div>
              <Text strong>损失 (反向)</Text>
              <Progress 
                percent={(1 - currentMetrics.loss) * 100} 
                strokeColor="#faad14"
                format={(percent) => `${(1 - percent! / 100).toFixed(3)}`}
              />
            </div>
          </Col>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>精确率</Text>
              <Progress 
                percent={(currentMetrics.precision || currentMetrics.accuracy) * 100} 
                strokeColor="#722ed1"
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>召回率</Text>
              <Progress 
                percent={(currentMetrics.recall || currentMetrics.accuracy) * 100} 
                strokeColor="#eb2f96"
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
            <div>
              <Text strong>F1分数</Text>
              <Progress 
                percent={(currentMetrics.f1_score || currentMetrics.accuracy) * 100} 
                strokeColor="#13c2c2"
                format={(percent) => `${percent?.toFixed(1)}%`}
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* 可视化图表 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card>
            {getRadarChart()}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card>
            {getMetricsBarChart()}
          </Card>
        </Col>
      </Row>

      {/* 模型评级 */}
      <Card title="模型评级" style={{ marginTop: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <div style={{ textAlign: 'center' }}>
              <Title level={4}>整体评级</Title>
              {(() => {
                const avgScore = (currentMetrics.accuracy + currentMetrics.auc + (1 - currentMetrics.loss)) / 3;
                let grade = 'C';
                let color = '#ff4d4f';
                if (avgScore > 0.9) { grade = 'A+'; color = '#52c41a'; }
                else if (avgScore > 0.85) { grade = 'A'; color = '#73d13d'; }
                else if (avgScore > 0.8) { grade = 'B+'; color = '#95de64'; }
                else if (avgScore > 0.75) { grade = 'B'; color = '#faad14'; }
                else if (avgScore > 0.7) { grade = 'C+'; color = '#ffc53d'; }
                else if (avgScore > 0.6) { grade = 'C'; color = '#ff7a45'; }
                
                return <Tag color={color} style={{ fontSize: '24px', padding: '8px 16px' }}>{grade}</Tag>;
              })()}
            </div>
          </Col>
          <Col xs={24} md={16}>
            <div>
              <Text strong>评级说明：</Text>
              <ul style={{ marginTop: '8px', fontSize: '12px' }}>
                <li><Tag color="#52c41a">A+</Tag> 优秀 (90%+): 模型性能卓越，可用于生产环境</li>
                <li><Tag color="#73d13d">A</Tag> 良好 (85-90%): 模型性能良好，适合大多数应用</li>
                <li><Tag color="#faad14">B</Tag> 中等 (75-85%): 模型性能中等，需要进一步优化</li>
                <li><Tag color="#ff7a45">C</Tag> 较差 (60-75%): 模型性能较差，建议重新训练</li>
                <li><Tag color="#ff4d4f">D</Tag> 很差 (&lt;60%): 模型性能很差，需要检查数据和参数</li>
              </ul>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default MetricsComparison;
