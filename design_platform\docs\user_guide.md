# EduBrain设计平台用户使用指南

## 平台概述

EduBrain设计平台是一个基于Web的认知诊断框架可视化设计平台，专门用于展示和对比EduBrain及其他认知诊断模型的性能。平台提供了完整的实验流程，从数据集管理到模型训练，再到结果分析和可视化。

## 功能特性

- **数据集管理**: 支持多种教育数据集的导入和管理
- **模型配置**: 提供直观的界面配置各种认知诊断模型
- **实验监控**: 实时监控模型训练进度和状态
- **结果分析**: 丰富的可视化图表展示实验结果
- **过度平滑分析**: 专门的分析工具展示EduBrain的优势

## 快速开始

### 1. 访问平台

打开浏览器，访问平台地址：
- 开发环境: http://localhost:3000
- 生产环境: https://your-domain.com

### 2. 平台导航

平台主要包含以下几个模块：

- **仪表板**: 系统概览和统计信息
- **数据集管理**: 数据集的查看和管理
- **实验配置**: 创建和配置新实验
- **实验监控**: 监控实验运行状态
- **结果分析**: 查看和分析实验结果

## 详细使用说明

### 数据集管理

#### 查看数据集列表
1. 点击左侧菜单的"数据集管理"
2. 查看所有可用数据集的列表
3. 每个数据集显示基本信息：学生数、题目数、知识点数等

#### 查看数据集详情
1. 点击数据集列表中的"查看详情"按钮
2. 查看详细的统计信息：
   - 基本统计：学生数量、题目数量、响应数量
   - 分布信息：平均响应数、数据稀疏度
   - 难度分布：题目难度的统计分析

#### 同步数据集
1. 点击"同步数据集"按钮
2. 系统会自动扫描数据集目录并更新数据库
3. 新发现的数据集会自动添加到列表中

### 实验配置

#### 创建新实验
1. 点击左侧菜单的"实验管理" → "实验配置"
2. 填写实验基本信息：
   - **实验名称**: 为实验起一个有意义的名称
   - **实验描述**: 简要描述实验目的和内容

#### 选择数据集和模型
1. **数据集选择**: 
   - 选择要使用的数据集
   - Assist0910数据集用于实际训练演示
   - 其他数据集仅用于可视化展示
2. **模型选择**:
   - ORCDF: 过度平滑抗性认知诊断框架
   - NCDM: 神经认知诊断模型
   - KANCD: 知识感知神经认知诊断
   - 其他经典模型

#### 配置训练参数

**基础训练参数**:
- **批次大小**: 建议256-1024，根据内存调整
- **训练轮数**: 建议10-50轮
- **学习率**: 建议0.001-0.01
- **权重衰减**: L2正则化参数，建议0-0.01
- **测试集比例**: 建议0.2 (20%)
- **随机种子**: 确保实验可重复性

**模型特定参数**:
- **潜在维度**: 学生和题目嵌入的维度
- **GCN层数**: 仅用于图模型，建议1-5层
- **保持概率**: Dropout参数，建议0.5-1.0

**ORCDF特定参数**:
- **SSL权重**: 自监督学习损失权重
- **SSL温度**: 对比学习温度参数
- **翻转比例**: 数据增强中的标签翻转比例

#### 启动实验
1. 配置完成后，点击"创建实验"
2. 选择是否"自动开始训练"
3. 实验创建成功后会跳转到监控页面

### 实验监控

#### 查看实验列表
1. 点击"实验管理" → "实验监控"
2. 查看所有实验的状态：
   - **等待中**: 实验已创建但未开始
   - **运行中**: 实验正在训练
   - **已完成**: 实验训练完成
   - **失败**: 实验训练失败
   - **已取消**: 实验被手动取消

#### 控制实验执行
- **启动实验**: 对于等待中的实验，点击"启动"按钮
- **停止实验**: 对于运行中的实验，点击"停止"按钮
- **查看详情**: 点击"详情"按钮查看实验详细信息

#### 监控训练进度
1. 点击实验的"详情"按钮
2. 查看实时进度信息：
   - 当前训练轮次
   - 训练进度百分比
   - 当前损失值
   - 最佳性能指标
3. 对于已完成的实验，可以查看完整的训练曲线

### 结果分析

#### 查看实验结果
1. 点击"结果分析"菜单
2. 查看所有已完成实验的列表
3. 每个实验显示关键性能指标：AUC、ACC、MND等

#### 查看训练曲线
1. 点击实验的"训练曲线"按钮
2. 查看训练过程中的损失和性能指标变化
3. 包含训练集和验证集的对比

#### 过度平滑分析
1. 点击实验的"过度平滑分析"按钮
2. 查看MND值的改善情况：
   - 训练前后的MND值对比
   - 改善倍数统计
   - 详细的分析说明

#### 模型对比
1. 在实验列表中选择多个要对比的实验
2. 点击"生成对比"按钮
3. 查看不同模型在各项指标上的对比：
   - 柱状图对比
   - 雷达图对比
   - 详细数值对比

## 高级功能

### 批量实验管理
1. 可以同时创建多个实验进行对比
2. 支持不同模型在同一数据集上的对比
3. 支持同一模型在不同参数下的对比

### 实验结果导出
1. 实验结果可以导出为CSV或JSON格式
2. 图表可以导出为PNG或PDF格式
3. 支持批量导出多个实验的结果

### 自定义可视化
1. 支持自定义图表的样式和配色
2. 可以调整图表的显示范围和精度
3. 支持交互式图表操作

## 最佳实践

### 实验设计建议
1. **对照实验**: 使用相同数据集和参数对比不同模型
2. **参数调优**: 系统性地调整关键参数观察效果
3. **多次运行**: 使用不同随机种子多次运行确保结果稳定性
4. **记录详情**: 为每个实验添加详细的描述和目的

### 性能优化建议
1. **批次大小**: 根据GPU内存调整，过大可能导致内存不足
2. **学习率**: 从较大值开始，逐步减小寻找最优值
3. **早停策略**: 监控验证集性能，避免过拟合
4. **数据预处理**: 确保数据质量和格式正确

### 结果解读指南
1. **AUC值**: 越接近1.0表示分类性能越好
2. **ACC值**: 准确率，越高越好
3. **MND值**: 学生能力区分度，越大表示过度平滑问题越轻
4. **训练曲线**: 应该呈现收敛趋势，避免震荡

## 常见问题

### Q: 为什么实验一直处于等待状态？
A: 可能原因：
- 系统资源不足，等待其他实验完成
- Celery任务队列服务未启动
- 检查系统日志获取详细信息

### Q: 训练过程中出现内存不足错误怎么办？
A: 解决方案：
- 减小批次大小
- 减少模型参数（如潜在维度）
- 使用更强的硬件配置

### Q: 如何理解过度平滑分析的结果？
A: 解读说明：
- MND值越大表示学生能力区分度越高
- 改善倍数越大表示EduBrain效果越明显
- 通常EduBrain相比其他模型有显著改善

### Q: 实验结果不理想怎么办？
A: 优化建议：
- 检查数据质量和预处理
- 调整学习率和训练轮数
- 尝试不同的模型参数组合
- 增加训练数据量

## 技术支持

如果在使用过程中遇到问题：

1. **查看帮助文档**: 详细阅读相关功能的说明
2. **检查系统日志**: 在浏览器开发者工具中查看错误信息
3. **联系技术支持**: 发送邮件至 <EMAIL>
4. **GitHub Issues**: 在项目GitHub页面提交问题

---

**提示**: 建议在正式使用前先进行小规模的测试实验，熟悉平台的各项功能和操作流程。
